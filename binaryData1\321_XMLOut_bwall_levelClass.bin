<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="normal">
		<gather name="">
			<level name="bwallWoTu">
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>WoTu</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="30" dpsMul="8" dieGotoState="stru" armsRange="firstRifle,pistol1"/>
					</unitOrder>
					<unitOrder id="we4" camp="we">
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" dieGotoState="stru" />
					</unitOrder>
					<!-- 敌方 -->
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="8" dpsMul="0.1" />
						<unit cnName="战斗僵尸" num="8" dpsMul="0.1"/>
						<unit cnName="携弹僵尸" num="1" dpsMul="0.1"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="僵尸王" lifeMul="999" dpsMul="12" skillArr="bwallKingUnder" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">600,290,200,117</rect><rect id="r_over">2940,563,60,125</rect>
					<rect id="r1">1500,180,500,80</rect><rect id="r2">2000,180,500,80</rect><rect id="r3">2500,180,500,80</rect><rect id="r4">30,30,30,30</rect>
					<rect label="addCharger">491,504,78,30</rect><rect label="addCharger">2507,504,78,30</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>createUnit:we4; r2</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
							<order>setDiyString:die</order>
						</event>
						<event><condition delay="1"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>
						<event>
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event><condition delay="1">enemyNumber:less_1</condition><order>say; startList:sOther</order></event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>createUnit:enemy4; r2</order>
							<order>setDiyString:noDie</order>
						</event>
						<![CDATA[主角被干死]]>
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; 本我</condition>
							<order>body:沃龙; ai:followBody:本我</order>
							<order>say; startList:s2</order>
						</event>
						<![CDATA[对话结束，复活主角]]>
						<event>
							<condition>say:listOver</condition>
							<order>level; rebirthWeStruHero</order>
							<order>body:本我; noUnderHurt:true</order>
							<order>setDiyString:die</order>
						</event>
					</group>
					<![CDATA[对话2之后的死亡判断]]>
					<group>
						<event><condition delay="0.1">diyString:die</condition></event>
						<event><condition delay="1">bodyEvent:die; 本我</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
					<![CDATA[胜利判断]]>
					<group>
						<event>
							<condition delay="0.1">bodyEvent:die; 僵尸王</condition>
							<order>say; startList:s3</order>
						</event>
						<event>
							<condition>say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="bwallYangMei">
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>YangMei</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="20" dpsMul="8" dieGotoState="stru" armsRange="pistol1,sniperRifle"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" dieGotoState="stru" />
					</unitOrder>
					<!-- 敌方 -->
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1" dpsMul="0.1"/>
						<unit cnName="战斗僵尸" num="1" dpsMul="0.1"/>
						<unit cnName="携弹僵尸" num="9" dpsMul="0.1"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="狂战尸" lifeMul="999" dpsMul="4" skillArr="bwallFightUnder" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">173,490,246,148</rect>
					<rect id="r1">1300,757,400,60</rect>
					<rect id="r2">2357,515,455,86</rect>
					<rect id="r3">2600,-200,314,66</rect>
					<rect id="r_over">-20,-129,63,117</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">212,578,78,30</rect>
					<rect label="addCharger">2704,578,78,30</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
							<order>setDiyString:die</order>
						</event>
						<event>
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event>
							<condition delay="1">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r2</order>
							<order>setDiyString:noDie</order>
						</event>
						<![CDATA[主角被干死]]>
						<event>
							<condition delay="1">bodyEvent:die; 本我</condition>
							<order>body:沃龙; ai:followBody:本我</order>
							<order>say; startList:s2</order>
						</event>
						<![CDATA[对话结束，复活主角]]>
						<event>
							<condition>say:listOver</condition>
							<order>level; rebirthWeStruHero</order>
							<order>body:本我; noUnderHurt:true</order>
							<order>setDiyString:die</order>
						</event>
					</group>
					<![CDATA[对话2之后的死亡判断]]>
					<group>
						<event><condition delay="0.1">diyString:die</condition></event>
						<event><condition delay="1">bodyEvent:die; 本我</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
					<![CDATA[胜利判断]]>
					<group>
						<event>
							<condition delay="0.1">bodyEvent:die; 狂战尸</condition>
							<order>say; startList:s3</order>
						</event>
						<event>
							<condition>say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			
			<level name="bwallXiaSha">
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>XiaSha</sceneLabel>
				<drop noB="1" />
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="20" dpsMul="8" dieGotoState="stru" skillArr="bwallScale" armsRange="firstRifle,pistol1"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" dieGotoState="stru" />
					</unitOrder>
					<!-- 敌方 -->
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="10" lifeMul="0.7" dpsMul="8" skillArr="State_AddMove50" />
						<unit cnName="携弹僵尸" num="10" lifeMul="0.7" dpsMul="8" skillArr="State_AddMove50"/>
						<unit cnName="僵尸暴枪兵" num="1" lifeMul="0.7" dpsMul="8" skillArr="State_AddMove50"/>
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">192,60,232,120</rect>
					<rect id="r_over">2956,1073,53,113</rect>
					<rect id="r1">2944,1067,56,127</rect>
					<rect id="r2">1034,-16,300,134</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">45,111,80,46</rect>
					<rect label="addCharger">2400,838,80,46</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
							<order>setDiyString:die</order>
						</event>
						<event><condition delay="1"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>
						<event>
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<event><condition></condition><order>setDiyString:unitOver</order></event>
					</group>
					<![CDATA[死亡判断]]>
					<group>
						<event>
							<condition delay="1">bodyEvent:die; 本我</condition>
							<order>body:沃龙; ai:followBody:本我</order>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition>say:listOver</condition>
							<order>level; rebirthWeStruHero</order>
							<order>body:本我; noUnderHurt:true</order>
							<order>doBwall:setMeStateExtraValue; bwallScale:3</order>
						</event>
						<event>
							<condition delay="0.1"></condition>
							<order>say; startList:s3</order>
						</event>
					</group>
					<![CDATA[胜利判断，在最后一波发兵结束后，开始判断]]>
					<group>
						<event><condition delay="0.1">diyString:unitOver</condition></event>
						<event>
							<condition delay="0.1">enemyNumber:less_1</condition>
							<order>say; startList:s4</order>
						</event>
						<event>
							<condition>say:listOver</condition>
							<order>doBwall:setMeStateExtraValue; bwallScale:1</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			
			<level name="bwallFengWei">
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>FengWei</sceneLabel>
				<drop noB="1" />
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="10" dpsMul="8" dieGotoState="stru" skillArr="crazy_hero_7,bwallScale" armsRange="firstRifle,pistol1"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" dieGotoState="stru" />
					</unitOrder>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.25"/>
						<unit cnName="战斗僵尸" num="0.25" />
						<unit cnName="携弹僵尸" num="0.25" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.25"/>
						<unit cnName="战斗僵尸" num="0.25" />
						<unit cnName="携弹僵尸" num="0.25" />
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸狙击兵" num="3"/>
						<unit cnName="僵尸暴枪兵" num="3"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="携弹僵尸" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">1734,640,145,83</rect>
					<rect id="r_over">3376,1008,56,127</rect>
					<rect id="r1">20,968,442,158</rect>
					<rect id="r2">3000,968,442,158</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">514,900,145,83</rect>
					<rect label="addCharger">2600,900,145,83</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
						</event>
						<event><condition delay="1"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>
						<event>
							<condition doNumber="5" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event>
							<condition doNumber="5" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event>
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<event>
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
						</event>
						<event>
							<condition delay="0.1">enemyNumber:less_1</condition>
							<order>say; startList:s4</order>
							<order>body:沃龙; ai:followBody:本我</order>
						</event>
						<event>
							<condition>say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<![CDATA[死亡判断]]>
					<group>
						<event>
							<condition delay="1">bodyEvent:die; 本我</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.05"></condition>
							<order>level; fail</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="bwallXiChi">
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>XiChi</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="100" dpsMul="1" dieGotoState="stru" skillArr="crazy_hero_7,bwallScale" armsRange="firstRifle,pistol1,sniperRifle"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" dieGotoState="stru" />
					</unitOrder>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="狂战尸" unitType="boss" skillArr="bwallFightUnder" />
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸王" unitType="boss" skillArr="bwallKingUnder" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="狂战射手" unitType="boss" skillArr="bwallFightUnder" />
					</unitOrder>
					<unitOrder id="enemy5">
						<unit cnName="钢铁僵尸王" lifeMul="99" dpsMul="2" skillArr="bwallKingUnder" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">1144,963,286,52</rect>
					<rect id="r_over">2896,1109,70,126</rect>
					<rect id="r1">1331,221,352,92</rect>
					<rect id="r2">15,1141,222,92</rect>
					<rect id="r3">2504,1171,222,92</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">771,1011,120,62</rect>
					<rect label="addCharger">2600,1166,145,83</rect>
					<rect label="addCharger">2600,1166,145,83</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
						</event>
						<event><condition delay="1"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r2</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy5; r2</order>
						</event>
						<event>
							<condition delay="0.1">enemyNumber:less_1</condition>
							<order>say; startList:s4</order>
							<order>body:沃龙; ai:followBody:本我</order>
						</event>
						<event>
							<condition>say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<![CDATA[死亡判断]]>
					<group>
						<event>
							<condition delay="1">bodyEvent:die; 本我</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.05"></condition>
							<order>level; fail</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="bwallBaiLu">
				<info enemyLv="1" diy="bwallTask" preSkillArr="godHiding_things" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>BaiLu</sceneLabel>
				<drop noB="1" />
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="2" dpsMul="8" dieGotoState="stru" skillArr="noArmsShoot" armsRange="firstRifle,pistol1"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" dieGotoState="stru" />
					</unitOrder>
					<unitOrder id="we2" camp="we">
						<unit cnName="文杰表哥" num="1" aiOrder="followBodyAttack:我"/>
						<unit cnName="雇佣兵" num="4"  aiOrder="followBodyAttack:文杰表哥"/><!-- 特种兵，跟随目标为主角 -->
					</unitOrder>
					
					<unitOrder id="enemy1">
						<unit cnName="战斗僵尸" num="5" lifeMul="99999" dpsMul="1" skillArr="fastForward_enemy,State_AddMove,State_AddMove50,killMeTimeOver" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">1070,234,160,50</rect>
					<rect id="r_over">2946,363,71,130</rect>
					<rect id="r1">30,250,220,64</rect>
					<rect id="r2">2770,427,220,64</rect>
					<rect id="r3">1144,621,220,64</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">2286,300,74,44</rect>
					<rect label="addCharger">437,230,74,44</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
							<order>level;taskTimingB:false</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>level;taskTimingB:true</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e_ZangShiShow">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:we2; r2</order>
						</event>
						<event><!-- 距离小于200才开始对话 -->
							<condition>bodyGap:less_350; 我:文杰表哥</condition>
							<order>say; startList:s4</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>body:文杰表哥; ai:patrolRandom</order><!-- 随机巡逻 -->
							<order>say; startList:s5</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<![CDATA[死亡判断]]>
					<group>
						<event>
							<condition delay="0.1">bodyEvent:die; 本我</condition>
							<order>body:沃龙; ai:followBody:本我</order>
							<order>say; startList:s2</order>
							<order>level;taskTimingB:false</order>
							<order>setDiyString:speed</order>
						</event>
						<event>
							<condition>say:listOver</condition>
							<order>level; rebirthWeStruHero</order>
							<order>level;taskTimingB:true</order>
							<order>body:本我; addState:godHiding_things</order>
							<order>setDiyString:die</order>
						</event>
					</group>
					<![CDATA[再次死亡判断]]>
					<group>
						<event><condition delay="0.1">diyString:die</condition></event>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
		
			<level name="bwallShuiSheng">
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>ShuiSheng</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="25" dpsMul="8" skillArr="crazy_hero_7,murderous_hero_5,groupLight_hero_5" armsRange="pistol1,sniperRifle"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" dieGotoState="stru" />
					</unitOrder>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="4"  dpsMul="8" skillArr="State_AddMove,State_AddMove50" />
						<unit cnName="僵尸突击兵" num="1" dpsMul="8" skillArr="State_AddMove,State_AddMove50" />
						<unit cnName="僵尸炮兵总管" num="1"   dpsMul="8" skillArr="State_AddMove,State_AddMove50" />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="5" dpsMul="8" skillArr="State_AddMove,State_AddMove50" />
						<unit cnName="僵尸突击兵" num="2" dpsMul="8" skillArr="State_AddMove,State_AddMove50" />
						<unit cnName="僵尸炮兵总管" num="2"  dpsMul="8" skillArr="State_AddMove,State_AddMove50" />
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="6"  dpsMul="8" skillArr="State_AddMove,State_AddMove50" />
						<unit cnName="僵尸突击兵" num="3" dpsMul="8" skillArr="State_AddMove,State_AddMove50" />
						<unit cnName="僵尸炮兵总管" num="3"  dpsMul="8" skillArr="State_AddMove,State_AddMove50" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="赤鼬导弹发射器" unitType="boss" skillArr="noMoveSpeed,longBulletLife" lifeMul="0.1" dpsMul="0.05"/>
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">1801,994,126,48</rect>
					<rect id="r_over">3453,1077,48,84</rect>
					<rect id="r1">220,1075,430,80</rect>
					<rect id="r2">2700,1080,430,80</rect>
					<rect id="r3">2477,517,672,110</rect>
					<rect id="r4">300,-440,120,27</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">722,858,60,38</rect>
					<rect label="addCharger">2736,858,60,38</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
							<order>doBwall:doValueFun; setFGFun:0.6</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>
						<event id="e2_2">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						
						<event id="e2_5">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r4</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>body:沃龙; ai:followBody:本我</order>
							<order>say; startList:s4</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>say; startList:s5</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="bwallBaiZhang">
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>BaiZhang</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="10" dpsMul="99" skillArr="noArmsShoot" armsRange="pistol1,sniperRifle"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" dieGotoState="stru" />
					</unitOrder>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="肥胖僵尸" num="1" dpsMul="99" skillArr="" />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="肥胖僵尸" num="2" lifeMul="0.5" dpsMul="99" skillArr="" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">747,171,198,71</rect>
					<rect id="r_over">2948,-800,70,109</rect>
					<rect id="r1">15,353,274,71</rect>
					<rect id="r2">2703,353,274,71</rect>
					<rect id="r3">1346,-64,300,72</rect>
					<rect id="r_back">-300,300,20,71</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">1476,933,60,38</rect>
					<rect label="addCharger">1453,16,60,38</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
						</event>
						<event><condition delay="0.1"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>
						<event>
							<condition doNumber="10" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event>
							<condition doNumber="5" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>body:沃龙; ai:followBody:本我</order>
							<order>say; startList:s4</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
							<order>doBwall:doValueFun; setFGFun:0.1</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			
			<level name="bwallGreenLand1">
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>GreenLand1</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="10" dpsMul="99" skillArr="noArmsShoot" armsRange="pistol1,sniperRifle"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" aiOrder="followBody:本我" />
					</unitOrder>
					<unitOrder id="we4" camp="we">
						<unit cnName="玉晴" aiOrder="followBody:本我" />
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r1</order>
							<order>createUnit:we4; r_over</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
							<order>doBwall:doValueFun; setFGFun:0.2</order>
						</event>
						<event><condition>bodyGap:less_450; 本我:玉晴</condition><order>say; startList:s1</order></event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
							<order>body:沃龙; followPoint:r_birth</order>
							<order>body:玉晴; followPoint:r_over</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s2</order>
						</event>
					</group>
				</eventG>
			</level>
			
			
			<level name="bwallZhuTou">
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1" preSkillArr="through_enemy" />
				<sceneLabel>ZhuTou</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="30" dpsMul="6" skillArr="crazy_hero_7,murderous_hero_5,groupLight_hero_5" armsRange="firstRifle,pistol1,shotgun1,sniperRifle"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" dieGotoState="stru" />
					</unitOrder>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.2"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸突击兵" num="2" dpsMul="2" />
						<unit cnName="僵尸狙击兵" num="2" dpsMul="2"/>
						<unit cnName="僵尸暴枪兵" num="2" dpsMul="2"/>
						<unit cnName="僵尸炮兵总管" num="1" dpsMul="2"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="狂野收割者" unitType="boss" lifeMul="0.2" dpsMul="0.5" skillArr="" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">1426,1030,142,52</rect>
					<rect id="r_over">2958,976,70,109</rect>
					<rect id="r1">20,654,266,85</rect>
					<rect id="r2">2700,654,266,85</rect>
					<rect id="r3">1344,433,295,85</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">343,956,78,47</rect>
					<rect label="addCharger">2567,956,78,47</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
						</event>
						<event><condition delay="0.1"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>

						<event>
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>body:本我; addSkill:through_enemy</order>
						</event>
						<event>
							<condition doNumber="5" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>body:沃龙; ai:followBody:本我</order>
							<order>say; startList:s4</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			
			<level name="bwallShuangTa">
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1" preSkillArr="through_enemy" />
				<sceneLabel>ShuangTa</sceneLabel>
				<drop noB="1" />
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="16" dpsMul="6" skillArr="crazy_hero_7,groupLight_hero_5" armsRange="firstRifle,pistol1,shotgun1,sniperRifle"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" dieGotoState="stru" />
					</unitOrder>
					<!-- 敌方 -->
					<unitOrder id="enemy3">
						<unit cnName="文杰表哥" unitType="boss" lifeMul="1.3" skillArr="crazy_hero_7,tenacious_hero_5,feedback_hero_5" armsRange="shotgun1,rocket1" skillCloseB="1" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">813,678,179,70</rect>
					<rect id="r_over">1940,1000,56,111</rect>
					<rect id="r1">28,1030,270,71</rect>
					<rect id="r2">1800,1030,270,71</rect>
					<rect id="r3">600,-250,270,71</rect>
					<rect id="r4">1466,-250,270,71</rect>
					<rect id="r_hide">-300,1030,270,71</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">316,613,83,40</rect>
					<rect label="addCharger">1746,800,83,40</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
							<order>roWorld180</order>
						</event>
						<event><condition delay="0.1"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>
						<event>
							<condition></condition>
							<order>createUnit:enemy3; r1</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>body:沃龙; ai:followBody:本我</order>
							<order>say; startList:s4</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			
		</gather>
		<gather>
			<level name="bwallDaoTa">
				<!-- 关卡数据 -->
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1" overBackB="1" />
				<drop noB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>DaoTa</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="16" dpsMul="6" skillArr="crazy_hero_7,groupLight_hero_5" armsRange="firstRifle,pistol1,shotgun1,sniperRifle"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough"/>
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
							<order>dropDaoTa:addCoin_task</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event>
							<condition delay="0.01">task:state; bwallDaoTa:complete</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="bwallDaoTa2">
				<!-- 关卡数据 -->
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1" overBackB="1" />
				<drop noB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>DaoTa</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="16" dpsMul="6" skillArr="crazy_hero_7,groupLight_hero_5" armsRange="firstRifle,pistol1,shotgun1,sniperRifle"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough"/>
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
							<order>dropDaoTa2:addCoin_task</order>
							<order>roWorld180</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event>
							<condition delay="0.01">task:state; bwallDaoTa2:complete</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="bwallBeiDou">
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>BeiDou</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="30" dpsMul="6" skillArr="groupLight_hero_5,boobyBwall" armsRange="firstRifle,pistol1,shotgun1,sniperRifle"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" dieGotoState="stru" />
					</unitOrder>
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="肥胖僵尸" num="3" />
						<unit cnName="携弹僵尸" num="3" />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="3" lifeMul="3" dpsMul="5" />
						<unit cnName="屠刀僵尸" num="3" lifeMul="3"   dpsMul="5"/>
						<unit cnName="携弹僵尸" num="3" lifeMul="3"   dpsMul="5"/>
						<unit cnName="僵尸突击兵" num="0.3" lifeMul="3"  dpsMul="5"/>
						<unit cnName="僵尸狙击兵" num="0.3" lifeMul="3"  dpsMul="5"/>
						<unit cnName="僵尸暴枪兵" num="0.3" lifeMul="3"  dpsMul="5"/>
						<unit cnName="僵尸炮兵总管" num="0.3" lifeMul="3"  dpsMul="5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="斩之使者" num="1" unitType="boss" dpsMul="2" />
					</unitOrder>
				</unitG>
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">2942,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">120,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					<rect label="addCharger">2126,170,71,60</rect>
					<rect label="addCharger">1113,374,71,60</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event>
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
						</event>
						<event><condition delay="0.1"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>

						<event>
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event><condition delay="0.1">enemyNumber:less_1</condition></event>
						<event>
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event><condition delay="0.1">enemyNumber:less_1</condition></event>
						<event>
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>body:沃龙; ai:followBody:本我</order>
							<order>say; startList:s4</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="bwallDongShan">
				<info enemyLv="1" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>DongShan</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough" dieGotoState="stru" />
						<unit cnName="本我" lifeMul="10" dpsMul="4" skillArr="crazy_hero_7,murderous_hero_5,groupLight_hero_5,noBulletReduct,throughClose" armsRange="extremeRocket,extremeLightning"/>
					</unitOrder>
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="4" />
						<unit cnName="肥胖僵尸" num="4"/>
						<unit cnName="屠刀僵尸" num="4" />
						<unit cnName="无头自爆僵尸" num="1"/>
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸空降兵" num="1"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="4" />
						<unit cnName="肥胖僵尸" num="4"/>
						<unit cnName="屠刀僵尸" num="4" />
						<unit cnName="无头自爆僵尸" num="6"/>
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸空降兵" num="1"  />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="毒魔" unitType="boss" num="1" lifeMul="2" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1426,1030,142,52</rect>
					<rect id="r_over">2958,976,70,109</rect>
					<rect id="r1">20,754,266,85</rect>
					<rect id="r2">2700,754,266,85</rect>
					<rect id="r3">1344,433,295,85</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">332,898,78,47</rect>
					<rect label="addCharger">2554,916,78,47</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event>
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
						</event>
						<event><condition delay="0.1"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>

						<event>
							<condition delay="1" doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event>
							<condition delay="3">enemyNumber:less_1</condition>
						</event>
						<event>
							<condition delay="1" doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event>
							<condition delay="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>body:沃龙; ai:followBody:本我</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="bwallQingSha">
				<sceneLabel>QingMing</sceneLabel>
				<!-- 关卡数据 -->
				<info preBulletArr="bwallTaskFire" preSkillArr="bwallToBat" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<drop noB="1"/>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="16" dpsMul="6" skillArr="" armsRange="pistol1"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough"/>
					</unitOrder>
				</unitG>
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">756,341,330,106</rect>
					<rect id="r_over">-30,800,80,135</rect>
					<rect id="r1">20,50,330,106</rect>
					<rect id="r2">2768,192,330,106</rect>
					<rect id="r3">1280,46,330,106</rect>
					<rect label="addCharger">2072,310,84,48</rect>
					<rect label="addCharger">122,868,84,66</rect>
				</rectG>
				<eventG>
					<group>
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
							<order>dropBwallQingSha:addCoin_task</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition><order>body:本我; addSkill:bwallToBat</order></event>
						<event>
							<condition delay="0.01">task:state; bwallQingSha:complete</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="bwallQingMing">
				<sceneLabel>QingMing</sceneLabel>
				<!-- 关卡数据 -->
				<info diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<drop noB="1"/>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="25" dpsMul="6" skillArr="crazy_hero_7,murderous_hero_5,groupLight_hero_5" armsRange="firstRifle,pistol1,shotgun1,sniperRifle"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough"/>
					</unitOrder>
					<unitOrder id="enemy1">
						<unit cnName="无头自爆僵尸" num="3" />
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="肥胖僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸空军总管" unitType="boss" dpsMul="1.5" lifeMul="1.5" />
					</unitOrder>
				</unitG>
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">1850,820,330,106</rect>
					<rect id="r_over">-30,800,80,135</rect>
					<rect id="r1">20,50,330,106</rect>
					<rect id="r2">2768,192,330,106</rect>
					<rect id="r3">1280,46,330,106</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">2072,310,84,48</rect>
					<rect label="addCharger">122,868,84,66</rect>
				</rectG>
				<eventG>
					<group>
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
							<order>mapToBlack</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
						<event id="e1_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="1">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="1">enemyNumber:less_1</condition></event>
						
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition>enemyNumber:less_1</condition>
							<order>mapToNormal</order>
							<order>say; startList:s2</order>
						</event>
						<event id="e_9">
							<condition>say:listOver</condition>
							
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="bwallQingMing2">
				<sceneLabel>QingMing</sceneLabel>
				<!-- 关卡数据 -->
				<info diy="bwallTask"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<drop noB="1"/>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="50" dpsMul="6" skillArr="crazy_hero_7,murderous_hero_5,groupLight_hero_5,noBulletReduct" armsRange="firstRifle,pistol1,shotgun1,sniperRifle"/>
					</unitOrder>
					<unitOrder id="enemy1">
						<unit cnName="监狱僵尸" num="3" skillArr="invisibilityEver,bwallDiePoint" />
						<unit cnName="独眼僵尸" num="3"  skillArr="invisibilityEver,bwallDiePoint"/>
						<unit cnName="僵尸突击兵" num="0.3" skillArr="invisibilityEver,bwallDiePoint"/>
						<unit cnName="僵尸狙击兵" num="0.3" skillArr="invisibilityEver,bwallDiePoint"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="火炮尸狼" unitType="boss" lifeMul="0.4" skillArr="invisibilityEver,bwallDiePoint"/>
					</unitOrder>
				</unitG>
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">1850,820,330,106</rect>
					<rect id="r_over">-30,800,80,135</rect>
					<rect id="r1">20,50,330,106</rect>
					<rect id="r2">2768,192,330,106</rect>
					<rect id="r3">1280,46,330,106</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">2072,310,84,48</rect>
					<rect label="addCharger">122,868,84,66</rect>
				</rectG>
				<eventG>
					<group>
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
							<order>mapToBlack</order>
							<order>doBwall:meNoVisible</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
						<event id="e1_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="1">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="1">enemyNumber:less_1</condition></event>
						
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e_9">
							<condition>enemyNumber:less_1</condition>
							<order>mapToNormal</order>
							<order>doBwall:meVisible</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="bwallNanTang">
				<sceneLabel>NanTang</sceneLabel>
				<!-- 关卡数据 -->
				<info enemyLv="10" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<drop noB="1"/>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="40" dpsMul="10" skillArr="crazy_hero_7,groupLight_hero_5,hiding_hero_4,bwallAllHurt" armsRange="firstRifle,pistol1,shotgun1,sniperRifle"/>
						<unit cnName="沃龙" lifeMul="9999" dpsMul="1" skillArr="noAttackOrder,State_noAiFind,State_InvincibleThrough"/>
					</unitOrder>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="防毒僵尸" unitType="boss" />
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="古飙" unitType="boss" />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="看门狗" unitType="boss" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="末日坦克" unitType="boss"/>
					</unitOrder>
				</unitG>
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">3742,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">120,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					<rect id="r4">-290,-166,368,106</rect>
					<rect label="addCharger">2126,120,71,60</rect>
					<rect label="addCharger">1113,324,71,60</rect>
				</rectG>
				<eventG>
					<group>
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order> 
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order> 
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order> 
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r1</order> 
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<event id="e_9">
							<condition>enemyNumber:less_1</condition>
							<order>body:沃龙; ai:followBody:本我</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="bwallStoneSea">
				<sceneLabel>StoneSea1</sceneLabel>
				<!-- 关卡数据 -->
				<info enemyLv="10" diy="bwallTask" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<drop noB="1"/>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="10" dpsMul="5" skillArr="crazy_hero_7,groupLight_hero_5,hiding_hero_5,noBulletReduct" armsRange="firstRifle,pistol1,shotgun1,sniperRifle"/>
					</unitOrder>
					<unitOrder id="we4" camp="we">
						<unit cnName="沃龙" lifeMul="30" dpsMul="3" skillArr="scaleWolong" armsRange="shotgun1,pistol1" dieGotoState="stru" warningRange="9999" />
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>createUnit:we4; r2</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:本我</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>doBwall:woLongEnemy</order>
						</event>
						<event>
							<condition delay="0.1">bodyEvent:die; 沃龙</condition>
							<order>say; startList:s4</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>say; startList:s5</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event><condition delay="0.01">bodyEvent:die; 本我</condition></event>
						<event><condition delay="0.05"></condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.05"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
		</gather>
	</father>
</data>