<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father type="zombie" cnName="守望者">
		<bullet cnName="守望者-激光球">
			<name>WatchEagle_shoot</name>
			<cnName>守望者-激光球</cnName>
			
			<bulletNum>2</bulletNum><shootAngle>5</shootAngle>
			<shakeAngle>5</shakeAngle><bulletLife>8</bulletLife><bulletWidth>15</bulletWidth><hitType>rect</hitType>
			<attackGap>1.1</attackGap><attackDelay>0.6</attackDelay><bulletAngle>155</bulletAngle><bulletAngleRange>40</bulletAngleRange>
			<shootPoint>-36,-35</shootPoint><bulletSpeed>15</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->
			<penetrationGap>1000</penetrationGap>
			<followD value="0.5"/>	<!-- 跟踪 -->
			<skillArr>gas_WatchEagle</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">WatchEagle/bullet</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/hummer_hit">WatchEagle/hit</hitImgUrl>
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/spark_motion</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="守望者-飓风">
			<name>WatchEagle_wind</name>
			<cnName>守望者-飓风</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<hurtMul>0.2</hurtMul>
			<hurtRatio>0.0000001</hurtRatio>
			<noHurtEffectB>1</noHurtEffectB>
			<attackType>holy</attackType>
			<shakeAngle>1</shakeAngle><bulletLife>30</bulletLife><bulletWidth>80</bulletWidth><hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.6</attackGap>
			<attackDelay>0.3</attackDelay>
			<bulletAngle>180</bulletAngle>
			<bulletNum>1</bulletNum>				
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<twoHitGap>0.2</twoHitGap>
			<skillArr>clearGas_WatchEagle</skillArr>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-200,28</shootPoint>
			<bulletSpeed>0</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">WatchEagle/windEffect</bulletImgUrl>
			<hitImgUrl soundUrl="sound/fireHit2">boomEffect/boom2</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
	</father>
	
	<father type="zombie" cnName="异角龙">		
		<bullet cnName="异角龙-喷毒">
			<name>Triceratops_poison</name>
			<cnName>异角龙-喷毒</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>-170</bulletAngle>
			<shakeAngle>15</shakeAngle>
			<bulletAngleRange>20</bulletAngleRange>
			<bulletLife>3</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.8</attackGap>
			<attackDelay>0.4</attackDelay>
			<bulletNum>3</bulletNum>				
			<shootAngle>6</shootAngle>					
			<shootPoint>-124,-100</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>15</bulletSpeed>
			<speedD random="0.2" max="15" min="10" a="-15" />
			<gravity>0.1</gravity>
			<!--<followD value="0.5" maxTime="0.3" />	 跟踪 -->
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">skillEffect/poisonClaw_enemy</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/water_hit">boomEffect/posion1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">skillEffect/poisonClaw_enemy</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="异角龙-石头">
			<name>Triceratops_shoot</name>
			<cnName>异角龙-石头</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>-170</bulletAngle>
			<bulletAngleRange>10</bulletAngleRange>
			<bulletLife>2</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.8</attackGap>
			<attackDelay>0.53</attackDelay>
			<bulletNum>3</bulletNum>				
			<shootAngle>3</shootAngle>					
			<shootPoint>-174,-17</shootPoint>
			<speedD random="0.5" max="30" min="20"/>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>15</bulletSpeed>
			<gravity>0.2</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>skillEffect/imploding_enemy_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="boomSound/smallBoom">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="异角龙-巨石崩塌">
			<name>Triceratops_stone</name>
			<cnName>异角龙-巨石崩塌</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>90</bulletAngle>
			<bulletLife>15</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>1</bulletNum>				
			<speedD random="0.07" max="10" min="8"/>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<twoHitGap>0.5</twoHitGap>
			<twoHitSameNameB>1</twoHitSameNameB>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>9</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>skillEffect/imploding_enemy_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="boomSound/smallBoom">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
	</father>
	
	<father type="zombie" cnName="异祖龙">
		<bullet cnName="异祖龙-火球">
			<name>FlyDragon_fireball</name>
			<cnName>异角龙-火球</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>175</bulletAngle>
			<bulletAngleRange>20</bulletAngleRange>
			<bulletLife>4</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.6</attackGap>
			<attackDelay>0.3</attackDelay>
			<bulletNum>1</bulletNum>					
			<shootPoint>-97,-6</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>10</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">FlyDragon/fireball</bulletImgUrl>
			<hitImgUrl name="redFire_hitFloor"></hitImgUrl><!-- 子弹图像【必备】 -->
			
		</bullet>
		<bullet cnName="异祖龙-巨焰围剿">
			<name>FlyDragon_fireSurround</name>
			<cnName>异祖龙巨焰围剿</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.3</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>90</bulletAngle>
			<shakeAngle>2</shakeAngle>
			<bulletLife>0.62</bulletLife>
			<shootNum>150</shootNum>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--运动属性------------------------------------------------------------ -->	
			<speedD random="0.1" max="30" />
			<bulletSpeed>30</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->
			
			<penetrationGap>1000</penetrationGap>
			<twoHitGap>1</twoHitGap>
			<twoHitSameNameB>1</twoHitSameNameB>
			<!--图像动画属性------------------------------------------------------------ -->
			<shootSoundUrl></shootSoundUrl>
			<bulletImgUrl name="fireFlamer_bullet"/>
			<hitImgUrl raNum="30">bulletHitEffect/smoke_motion</hitImgUrl><!-- 子弹图像【必备】 -->
			<hitFloorImgUrl raNum="30">bulletHitEffect/smoke_motion</hitFloorImgUrl><!-- 子弹图像【必备】 -->
			<fireImgType>no</fireImgType>
		</bullet>
		
		<bullet cnName="异祖龙-巨焰围剿-喷烟">
			<name>FlyDragon_fireSurroundSmoke</name>
			<cnName>异祖龙巨焰喷烟</cnName>
			<noHitB>1</noHitB>
			<hurtRatio>0</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>2</shakeAngle>
			<bulletLife>0.62</bulletLife>
			<shootNum>15</shootNum>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--运动属性------------------------------------------------------------ -->	
			<speedD random="0.1" max="30" />
			<bulletSpeed>30</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<bulletImgUrl raNum="2" con="filter">fireEffect/smoke1</bulletImgUrl>
			<fireImgType>no</fireImgType>
		</bullet>
		
		<bullet cnName="异祖龙-金球">
			<name>FlyDragonBall</name>
			<cnName>异角龙-金球</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>-90</bulletAngle>
			<bulletAngleRange>180</bulletAngleRange>
			<bulletLife>10</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>1</bulletNum>					
			<shootPoint>0,0</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<skillArr>FlyDragon_petrifaction</skillArr>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>20</bulletSpeed>
			<followD value="1"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>FlyDragon/drop</bulletImgUrl>
			<hitImgUrl soundUrl="sound/fireHit1">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
	</father>
	<father type="zombie" cnName="异齿虎">
		<bullet index="0" name="异齿虎-散弹枪">
			<name>SaberTiger_machine</name>
			<cnName>异齿虎-散弹枪</cnName>
			<!--武器属性------------------------------------------------------------ -->
			<hurtRatio>0.2</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>0.001</bulletLife>
			<hitType>longLine</hitType>
			<bulletWidth>800</bulletWidth>
			<bulletShakeWidth>100</bulletShakeWidth>
			<!--攻击时的属性------------------------------------------------------------ -->
			<shootPoint>-92,-102</shootPoint>
			<bulletAngle>179</bulletAngle>
			<attackGap>1.4</attackGap>
			<attackDelay>1</attackDelay>
			<bulletNum>3</bulletNum>				
			<shootNum>4</shootNum>					
			<shootGap>0.07</shootGap>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootAngle>20</shootAngle>				
			<shakeAngle>3</shakeAngle>
			<bulletSpeed>0</bulletSpeed>
			<!--<bulletMaxV>10</bulletMaxV>-->
			<!--<bulletMaxVa>0</bulletMaxVa>-->
			<!-- <bulletVra>-1000</bulletVra>-->		<!-- 自转速度（默认值为0，标准值30，-1000代表永远不转方向）-->
			<!-- <gravity>0.8</gravity>-->			<!-- 重力系数（默认值为0）-->
			<!--跟踪------------------------------------------------------------ -->
			<bounceD floor="2" body="0" num="0"/>	<!-- 反弹 -->
			<!--特殊------------------------------------------------------------ -->
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<lineD lightColor="0x00CCFF" size="2" lightSize="6"/>
			<bulletImgUrl>longLine</bulletImgUrl>
			<hitImgUrl>bulletHitEffect/yellow_motion</hitImgUrl><!-- 子弹图像【必备】 -->
			<!-- <smokeImgUrl>sub/missile_bullet_smoke</smokeImgUrl>--><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="异齿虎-量子光束">
			<name>SaberTiger_laser</name>
			<cnName>骷髅-量子光束</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtMul>0.1</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>0.001</bulletLife>
			<hitType>longLine</hitType>
			<bulletWidth>320</bulletWidth>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>270</bulletAngle>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<imgClearDelay>0.49</imgClearDelay>
			<bulletImgUrl>SaberTiger/laserEffect</bulletImgUrl>
			<hitImgUrl con="add">bulletHitEffect/energy</hitImgUrl>
		</bullet>
		<bullet cnName="末日轰炸">
			<name>SaberTiger_missile</name>
			<cnName>末日轰炸</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.1</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletAngle>-135</bulletAngle>
			<bulletLife>99</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.3</attackGap>
			<attackDelay>0.4</attackDelay>
			<bulletNum>1</bulletNum>				
			<shootNum>3</shootNum>
			<shootGap>0.21</shootGap>
			<shootPoint>-42,-140</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<bulletSkillArr>SaberTiger_laser</bulletSkillArr>
			<boomD selfB="1" bodyB="1" radius="140" />
			<followD value="1" delay="2" />
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>10</bulletSpeed>
			<speedD max="30" min="1" a="-4" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">SaberTiger/bullet1</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom1"  soundVolume="0.3"  shake="3,0.4,13">boomEffect/boom2</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="add" >SaberTiger/smoke1</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
			
		</bullet>
	</father>	
	<father type="zombie" cnName="异猛象">
		<bullet cnName="异猛象-喷球">
			<name>Mammoth_shoot</name>
			<cnName>异猛象-喷球</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>179</bulletAngle>
			<shakeAngle>0</shakeAngle>
			<bulletAngleRange>10</bulletAngleRange>
			<bulletLife>9</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<shootNum>2</shootNum>
			<shootGap>0.13</shootGap>
			<attackGap>0.8</attackGap>
			<attackDelay>0.5</attackDelay>
			<bulletNum>1</bulletNum>				
			<shootPoint>-390,-216</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>10</bulletSpeed>
			<followD value="0.7" delay="0.5" maxTime="6" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="filter" raNum="15">Mammoth/ball</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/water_hit">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="异猛象-电磁风暴">
			<name>Mammoth_electricity</name>
			<cnName>异猛象-电磁风暴</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.1</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<attackGap>5</attackGap>
			<bulletAngle>179.9</bulletAngle>
			<shakeAngle>0</shakeAngle>
			<bulletLife>20</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>1</bulletNum>				
			<shootNum>4</shootNum>
			<shootGap>0.8</shootGap>
			<shootPoint>-390,-216</shootPoint>
			<twoHitGap>1</twoHitGap>
			<twoHitSameNameB>1</twoHitSameNameB>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>10</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="add">Mammoth/elecBullet</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/magicHit2">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="异猛象-洲际导弹">
			<name>Mammoth_missile</name>
			<cnName>洲际导弹</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.175</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>15</shakeAngle>
			<bulletAngle>-105</bulletAngle>
			<bulletLife>99</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<shootNum>2</shootNum>
			<shootGap>0.15</shootGap>
			<attackGap>0.8</attackGap>
			<attackDelay>0.43</attackDelay>
			<bulletNum>1</bulletNum>				
			<shootPoint>98,-263</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<bulletSkillArr>Mammoth_missile</bulletSkillArr>
			<boomD selfB="1" bodyB="1" radius="140" />
			<followD value="1" delay="10" />
			<bindingD cnName="空单位" lifeMul="0.006"  skillArr="Mammoth_missileChip" />
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>5</bulletSpeed>
			<speedD max="45" min="2" a="-2" random="0.1" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">Mammoth/missile</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2"  soundVolume="0.3"  shake="3,0.4,13">boomEffect/boom2</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="add" raNum="30">Mammoth/missileSmoke</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		
		<bullet cnName="异猛象-洲际导弹-爆炸后碎片">
			<name>Mammoth_missileChip</name>
			<cnName>洲际导弹碎片</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<sameCampB>0</sameCampB>
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.006</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletAngle>-90</bulletAngle>
			<bulletLife>4</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>3</bulletNum>				
			<shootAngle>10</shootAngle>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<gravity>1</gravity>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>3</bulletSpeed>
			<speedD max="30"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">Mammoth/missileChip</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom1"  soundVolume="0.3"  shake="3,0.4,13">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="机械核心-自爆">
			<name>Mammoth_core_die</name>
			<cnName>机械核心-自爆</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>1</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>15</shakeAngle>
			<bulletLife>0.0001</bulletLife>
			<bulletWidth>140</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>1</bulletNum>				
			<shootNum>5</shootNum>
			<shootPoint>0,90</shootPoint>
			<positionD>
				<point shootPoint="0,90"/>
				<point shootPoint="-50,18"/>
				<point shootPoint="-78,67" />
				<point shootPoint="102,59"/>
				<point shootPoint="89,-17"/>
			</positionD>
			<!--特殊属性------------------------------------------------------------ -->	
			<twoHitGap>0.1</twoHitGap>
			<twoHitSameNameB>1</twoHitSameNameB>
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>0</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<imgClearDelay>0.65</imgClearDelay>
			<bulletImgUrl>boomEffect/boom3</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2"  soundVolume="0.3"  shake="3,0.4,13">boomEffect/boom2</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
	</father>	
	<father type="zombie" cnName="虚空客">
		<bullet cnName="虚空客-射击">
			<name>VanityKer_shoot</name>
			<cnName>虚空客-射击</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>179.9</bulletAngle>
			<bulletAngleRange>5</bulletAngleRange>
			<bulletLife>3</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.6</attackGap>
			<attackDelay>0.3</attackDelay>
			<bulletNum>3</bulletNum>					
			<shootAngle>2</shootAngle>
			<shootPoint>-83,7</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>25</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" con="filter" >VanityKer/bullet1</bulletImgUrl>
			<hitImgUrl soundUrl="sound/fireHit1">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		<bullet cnName="虚空客-导弹">
			<name>VanityKer_missle</name>
			<cnName>虚空客-导弹</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>179.9</bulletAngle>
			<bulletAngleRange>5</bulletAngleRange>
			<bulletLife>5</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.5</attackGap>
			<attackDelay>0.2</attackDelay>
			<bulletNum>1</bulletNum>					
			<shootPoint>-48,75</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<boomD selfB="1" bodyB="1" radius="140" />
			<followD value="0.8" />
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>10</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">VanityKer/bullet2</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2">boomEffect/boom1</hitImgUrl>
		</bullet>
		
		<bullet cnName="虚空客-星际尘埃">
			<name>VanityKer_comet</name>
			<cnName>星际尘埃</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>90</bulletAngle>
			<bulletLife>30</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<skillArr>VanityKer_cometBuff</skillArr>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>5</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="add">VanityKer/comet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/fireHit1" con="add">bulletHitEffect/energy</hitImgUrl>
		</bullet>
		
		<bullet cnName="虚空客-虚幻镜像">
			<name>VanityKer_dreamland</name>
			<cnName>虚空客-虚幻镜像</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.00001</hurtRatio>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>270</bulletAngle>
			<bulletLife>8</bulletLife>
			<bulletWidth>440</bulletWidth>
			<hitType>longLine</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<shootPoint>-378,130</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<oneHitBodyB>1</oneHitBodyB>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>0</bulletSpeed>
			<skillArr>VanityKer_dreamlandUnit</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="add">VanityKer/lightWall</bulletImgUrl>
			<hitImgUrl soundUrl="sound/fireHit1" con="add">VanityKer/lightHit</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
	</father>		
	<father type="zombie" cnName="年兽">
		<bullet cnName="年兽-火球">
			<name>Nian_fireball</name>
			<cnName>年兽-火球</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.1</hurtMul>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>175</bulletAngle>
			<bulletAngleRange>10</bulletAngleRange>
			<bulletLife>10</bulletLife>
			<bulletWidth>25</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.7</attackGap>
			<attackDelay>0.3</attackDelay>
			<bulletNum>1</bulletNum>					
			<shootPoint>-198,-124</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<followD delay="2" maxTime="2.3" value="6" />
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>10</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">NianMonster/fireball</bulletImgUrl>
			<hitImgUrl soundUrl="sound/fireHit1">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="年兽-驾驭星火">
			<name>Nian_spark</name>
			<cnName>驾驭星火</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.2</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>10</shakeAngle>
			<bulletAngle>-90</bulletAngle>
			<bulletLife>10</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>1</bulletNum>				
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<bulletSkillArr>Mammoth_missile</bulletSkillArr>
			<followD value="2" delay="1" />
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>12</bulletSpeed>
			<speedD max="25" min="1" a="-10" random="0.2" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="add">NianMonster/sparkBullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/fireHit1"  soundVolume="0.3"  shake="3,0.4,13">boomEffect/boom2</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="45">bulletHitEffect/spark_motion</smokeImgUrl>
		</bullet>
		<bullet cnName="年兽-晶火刃">
			<name>Nian_darts</name>
			<cnName>年兽-晶火刃</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.1</hurtMul>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>160</bulletAngle>
			<bulletAngleRange>30</bulletAngleRange>
			<bulletLife>15</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.1</attackGap>
			<attackDelay>0.6</attackDelay>
			<bulletNum>3</bulletNum>					
			<shootAngle>20</shootAngle>
			<shootPoint>-103,-230</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<skillArr>Nian_dartsParalysis</skillArr>
			<bounceD glueFloorB="1" />
			<penetrationNum>999</penetrationNum>
			<oneHitBodyB>1</oneHitBodyB>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>40</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="16" con="add">NianMonster/dartsBullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/metal_hit1">bulletHitEffect/yellow_motion</hitImgUrl><!-- 子弹图像【必备】 -->
			
		</bullet>
	</father>	
	<father type="zombie" cnName="虚炎狼">
		<bullet cnName="虚炎狼-无名火">
			<name>FireWolf_noName</name>
			<cnName>虚炎狼-无名火</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.15</hurtMul>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>90</bulletAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>2</bulletNum>					
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>6</bulletSpeed>
			<speedD max="15" a="3" />
			<bulletSkillArr>spiralMove</bulletSkillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="add">FireWolf/noFire</bulletImgUrl>
			<hitImgUrl soundUrl="sound/fireHit1">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">FireWolf/noSmoke</smokeImgUrl>
		</bullet>
		<bullet cnName="虚炎狼-喷火">
			<name>FireWolf_shoot</name>
			<cnName>虚炎狼-喷火</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<attackGap>1.3</attackGap>
			<attackDelay>0.8</attackDelay>
			<bulletNum>1</bulletNum>					
			<shootNum>8</shootNum>
			<shootPoint>-195,-90</shootPoint>
			
			<bulletAngle>180</bulletAngle>
			<bulletAngleRange>70</bulletAngleRange>
			<bulletLife>0.6</bulletLife>
			<bulletWidth>25</bulletWidth>
			<hitType>rect</hitType>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<bulletSpeed>13</bulletSpeed>
			
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl name="fireFlamer_bullet"/>
			<hitImgUrl soundUrl="sound/fireHit1">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="墟洞岩火-地面特效">
			<name>FireWolf_rockFire</name>
			<cnName>墟洞岩火-地面特效</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.00001</hurtRatio>
			<noHitB>1</noHitB>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>270</bulletAngle>
			<bulletLife>0.4</bulletLife>
			<bulletSkillArr>FireWolf_rockFire</bulletSkillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<bulletImgUrl>FireWolf/rockFire</bulletImgUrl>
		</bullet>
		<![CDATA[
		<bullet cnName="墟洞岩火-火">
			<name>FireWolf_rockFireLink</name>
			<cnName>墟洞岩火-火球</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.1</hurtMul>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>-90</bulletAngle>
			<bulletLife>0.6</bulletLife>
			<bulletWidth>25</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>1</bulletNum>					
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<bulletSpeed>10</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl name="fireFlamer_bullet"/>
			<hitImgUrl soundUrl="sound/fireHit1">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		]]>
		<bullet cnName="墟洞岩火-火">
			<name>FireWolf_rockFireLink</name>
			<cnName>墟洞岩火-火</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.3</hurtMul>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>-90</bulletAngle>
			<bulletLife>10</bulletLife>
			<bulletWidth>25</bulletWidth>
			<hitType>rect</hitType>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<twoHitGap>1</twoHitGap>
			<twoHitSameNameB>1</twoHitSameNameB>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>20</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="filter" imgDieType="last">FireWolf/fire1</bulletImgUrl>
			<hitImgUrl soundUrl="sound/fireHit1">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			
		</bullet>
		<bullet cnName="稀有元素-黄">
			<name>elementsYellow</name><cnName>稀有元素-黄</cnName><hurtRatio>0</hurtRatio><attackType>holy</attackType>
			<bulletAngle>-90</bulletAngle><bulletAngleRange>180</bulletAngleRange><bulletLife>10</bulletLife><bulletWidth>30</bulletWidth><hitType>rect</hitType><penetrationGap>1000</penetrationGap><bulletSpeed>20</bulletSpeed>
			<hitImgUrl soundUrl="sound/fireHit1">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationNum>2</penetrationNum>
			<oneHitBodyB>1</oneHitBodyB>
			<skillArr>elementsYellow</skillArr><followD value="2"/>
			<bulletImgUrl con="filter" raNum="30">FireWolf/elementsYellowBullet</bulletImgUrl>
		</bullet>
		<bullet cnName="稀有元素-红">
			<name>elementsRed</name><cnName>稀有元素-红</cnName><hurtRatio>0</hurtRatio><attackType>holy</attackType><sameCampB>0</sameCampB>
			<bulletAngle>-90</bulletAngle><bulletAngleRange>180</bulletAngleRange><bulletLife>9999</bulletLife><bulletWidth>30</bulletWidth><hitType>rect</hitType><penetrationGap>1000</penetrationGap><bulletSpeed>20</bulletSpeed>
			<hitImgUrl soundUrl="sound/fireHit1">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<!--特殊属性------------------------------------------------------------ -->	
			<skillArr>elementsRed</skillArr><followD value="2"/>
			<bulletImgUrl con="filter" raNum="30">FireWolf/elementsRedBullet</bulletImgUrl>
		</bullet>
		<bullet cnName="稀有元素-绿">
			<name>elementsGreen</name><cnName>稀有元素-绿</cnName><hurtRatio>0</hurtRatio><attackType>holy</attackType>
			<bulletAngle>-90</bulletAngle><bulletAngleRange>180</bulletAngleRange><bulletLife>10</bulletLife><bulletWidth>30</bulletWidth><hitType>rect</hitType><penetrationGap>1000</penetrationGap><bulletSpeed>20</bulletSpeed>
			<hitImgUrl soundUrl="sound/fireHit1">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<!--特殊属性------------------------------------------------------------ -->	
			<skillArr>elementsGreen</skillArr><followD value="2"/>
			<bulletImgUrl con="filter" raNum="30">FireWolf/elementsGreenBullet</bulletImgUrl>
		</bullet>
		<bullet cnName="稀有元素-蓝">
			<name>elementsBlue</name><cnName>稀有元素-蓝</cnName><hurtRatio>0</hurtRatio><attackType>holy</attackType><sameCampB>0</sameCampB>
			<bulletAngle>-90</bulletAngle><bulletAngleRange>180</bulletAngleRange><bulletLife>9999</bulletLife><bulletWidth>30</bulletWidth><hitType>rect</hitType><penetrationGap>1000</penetrationGap><bulletSpeed>20</bulletSpeed>
			<hitImgUrl soundUrl="sound/fireHit1">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<!--特殊属性------------------------------------------------------------ -->	
			<skillArr>elementsBlue</skillArr><followD value="2"/>
			<bulletImgUrl con="filter" raNum="30">FireWolf/elementsPurpleBullet</bulletImgUrl>
		</bullet>
		<bullet cnName="稀有元素-紫">
			<name>elementsPurple</name><cnName>稀有元素-蓝</cnName><hurtRatio>0</hurtRatio><attackType>holy</attackType>
			<bulletAngle>-90</bulletAngle><bulletAngleRange>180</bulletAngleRange><bulletLife>10</bulletLife><bulletWidth>30</bulletWidth><hitType>rect</hitType><penetrationGap>1000</penetrationGap><bulletSpeed>20</bulletSpeed>
			<hitImgUrl soundUrl="sound/fireHit1">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationNum>2</penetrationNum>
			<oneHitBodyB>1</oneHitBodyB>
			<skillArr>elementsPurple</skillArr><followD value="2"/>
			<bulletImgUrl con="filter" raNum="30">FireWolf/elementsPurpleBullet</bulletImgUrl>
		</bullet>
	</father>	
	<father type="zombie" cnName="虚洪螈">
		<bullet cnName="虚洪螈-射击">
			<name>Salamander_shoot</name>
			<cnName>虚洪螈-射击</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>175</bulletAngle>
			<bulletAngleRange>30</bulletAngleRange>
			<bulletLife>4</bulletLife>
			<bulletWidth>25</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.6</attackGap>
			<attackDelay>0.4</attackDelay>
			<bulletNum>1</bulletNum>					
			<shootPoint>-101,-128</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<followD value="0.1" />
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>15</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">Salamander/bullet1</bulletImgUrl>
			<hitImgUrl soundUrl="sound/water_hit">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
			
		</bullet>
		
		<bullet cnName="虚洪螈-水柱">
			<name>Salamander_water</name>
			<cnName>虚洪螈-水柱</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>175</bulletAngle>
			<bulletAngleRange>30</bulletAngleRange>
			<bulletLife>10</bulletLife>
			<bulletWidth>25</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.9</attackGap>
			<attackDelay>0.6</attackDelay>
			<bulletNum>2</bulletNum>					
			<shootAngle>10</shootAngle>
			<shootPoint>-101,-128</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<followD value="0.5" />
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>30</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" con="filter">Salamander/waterBullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/water_hit">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		
		</bullet>
		
	</father>
	<father type="zombie" cnName="虚晶蝎">
		<bullet cnName="虚晶蝎-射击">
			<name>VirtualScorpion_shoot</name>
			<cnName>虚晶蝎-射击</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>179</bulletAngle>
			<bulletAngleRange>30</bulletAngleRange>
			<bulletLife>2</bulletLife>
			<bulletWidth>25</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.4</attackGap>
			<attackDelay>0.85</attackDelay>
			<bulletNum>1</bulletNum>
			<shootNum>4</shootNum>
			<shootGap>0</shootGap>
			<shootPoint>-171,-37</shootPoint>
			<positionD specialType="vertical"/>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<followD value="0.05" />
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>20</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">VirtualScorpion/bullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/body_hit">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
			
		</bullet>
		<bullet cnName="虚晶蝎-阴风">
			<name>VirtualScorpion_yinWind</name>
			<cnName>虚晶蝎-阴风</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>1</shakeAngle>
			<bulletLife>13</bulletLife>
			<bulletWidth>60</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.4</attackGap>
			<attackDelay>0.6</attackDelay>
			<bulletAngle>180</bulletAngle>
			<bulletNum>1</bulletNum>				
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationNum>999</penetrationNum>
			<penetrationGap>1000</penetrationGap>
			<twoHitGap>1</twoHitGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-103,-80</shootPoint>
			<bulletSpeed>5</bulletSpeed>
			<speedD max="5" min="2" a="-4" />
			<skillArr></skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">VirtualScorpion/wind1</bulletImgUrl>
			<hitImgUrl soundUrl="sound/water_hit">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="虚晶蝎-阳风">
			<name>VirtualScorpion_yangWind</name>
			<cnName>虚晶蝎-阳风</cnName>
			<noHurtEffectB>1</noHurtEffectB>
			<hurtRatio>0</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>1</shakeAngle>
			<bulletLife>13</bulletLife>
			<bulletWidth>60</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.4</attackGap>
			<attackDelay>0.8</attackDelay>
			<bulletAngle>0</bulletAngle>
			<bulletNum>1</bulletNum>				
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationNum>999</penetrationNum>
			<penetrationGap>1000</penetrationGap>
			<twoHitGap>1</twoHitGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-103,-80</shootPoint>
			<bulletSpeed>5</bulletSpeed>
			<speedD max="5" min="2" a="-4" />
			<skillArr>VirtualScorpion_windHurt</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">VirtualScorpion/wind2</bulletImgUrl>
			<hitImgUrl soundUrl="sound/water_hit">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
	</father>
	
	<father type="zombie" cnName="炸裂者">
		<bullet cnName="投弹">
			<name>DryFrog_shoot</name>
			<cnName>投弹</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>-170</bulletAngle>
			<bulletAngleRange>30</bulletAngleRange>
			<bulletLife>3</bulletLife>
			<bulletWidth>25</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.8</attackGap>
			<attackDelay>0.46</attackDelay>
			<bulletNum>1</bulletNum>					
			<shootPoint>-100,-136</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>20</bulletSpeed>
			<gravity>0.5</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">DryFrog/bullet</bulletImgUrl>
			<smokeImgUrl con="filter" raNum="15">DryFrog/bulletSmoke</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
			<hitImgUrl soundUrl="boomSound/microBoom">DryFrog/boom</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="旋转炸弹">
			<name>DryFrogRotate</name>
			<cnName>旋转炸弹</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>10</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.1</attackGap>
			<attackDelay>0.4</attackDelay>
			<bulletAngle>110</bulletAngle>		
			<shootNum>4</shootNum>					
			<shootGap>0.1</shootGap>						
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-45,-74</shootPoint>
			<bulletSpeed>20</bulletSpeed>
			
			<boomD  selfB="1" bodyB="1" radius="130"/>
			<bounceD glueFloorB="1" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">DryFrog/bullet</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/microBoom">DryFrog/boom</hitImgUrl>
			<hitFloorImgUrl>bulletHitEffect/yellow_motion</hitFloorImgUrl>
			<smokeImgUrl con="filter" raNum="30">skillEffect/smallFire</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		
		<bullet cnName="倒炸弹">
			<name>DryFrogPour</name>
			<cnName>倒炸弹</cnName>
			<hurtMul>0.12</hurtMul>
			<attackType>holy</attackType>
			<bulletSkillArr>DryFrogPourDieEvent</bulletSkillArr>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>10</shakeAngle>
			<bulletLife>3</bulletLife>
			<bulletWidth>1</bulletWidth>
			<hitType>rect</hitType>
			
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.7</attackGap>
			<attackDelay>0.6</attackDelay>
			<bulletAngle>95</bulletAngle>		
			<shootNum>3</shootNum>					
			<shootGap>0.15</shootGap>						
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-113,-120</shootPoint>
			<bulletSpeed>6</bulletSpeed>
			<speedD a="1" max="20"/>
			<skillArr>FightKing_hitParalysis</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">DryFrog/bullet</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2">boomEffect/boom2</hitImgUrl>
			<hitFloorImgUrl soundUrl="DryFrog/skill7" con="filter">bulletHitEffect/yellow_motion</hitFloorImgUrl>
			<smokeImgUrl con="filter" raNum="30">skillEffect/smallFire</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="倒炸弹-空弹">
			<name>DryFrogPour_zero</name>
			<cnName>倒炸弹-空弹</cnName>
			<hurtMul>0.12</hurtMul>
			<attackType>holy</attackType>
			<bulletSkillArr>DryFrogPour_zero</bulletSkillArr>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>30</bulletLife>
			<bulletWidth>70</bulletWidth>
			<hitType>rect</hitType>	
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>0</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<skillArr>FightKing_hitParalysis</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>DryFrog/noBullet</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2">boomEffect/boom2</hitImgUrl>
			<hitFloorImgUrl>generalEffect/headSmoke</hitFloorImgUrl>
		</bullet>
	</father>
	
	<father type="zombie" cnName="极速守卫">
		<bullet cnName="极速守卫-射击">
			<name>FastGuards_shoot</name>
			<cnName>射击</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>179.9</bulletAngle>
			<bulletAngleRange>20</bulletAngleRange>
			<bulletLife>3</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.9</attackGap>
			<attackDelay>0.6</attackDelay>
			<shootPoint>-53,-130</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>20</bulletSpeed>
			<followD value="0.5"  maxTime="0.5" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" con="filter" >FastGuards/bullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/fireHit1">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<![CDATA[
		
		]]>
		<bullet cnName="致命打击">
			<name>FastGuards_screen</name>
			<cnName>致命打击</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtMul>0.9</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>0.001</bulletLife>
			<bulletWidth>300</bulletWidth>
			<hitType>longLine</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletAngle>-90</bulletAngle>
			<bulletNum>1</bulletNum>				
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>0</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>1000</penetrationNum>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<imgClearDelay>0.36</imgClearDelay>
			<bulletImgUrl con="add" soundUrl="FastGuards/screenHit" shake="3,0.3,35,90">FastGuards/screenBullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/magicHit2">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		<bullet cnName="超级弹幕-母弹">
			<name>FastGuards_missile</name>
			<cnName>超级弹幕-母弹</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<hurtRatio>1</hurtRatio>
			<bulletSkillArr>FastGuards_missileDieEvent</bulletSkillArr>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>-150</bulletAngle>
			<bulletLife>0.8</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.1</attackGap>
			<attackDelay>0.7</attackDelay>
			<bulletNum>1</bulletNum>					
			<shootPoint>-2,-97</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<boomD selfB="1" bodyB="1" radius="40" />
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>8</bulletSpeed>
			<speedD a="-2" min="2" />
			<gravity>0.1</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">FastGuards/missile</bulletImgUrl>
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom1">boomEffect/boom2</hitImgUrl>
		</bullet>
		
		
		<bullet cnName="超级弹幕-子弹">
			<name>FastGuards_missileChild</name>
			<cnName>超级弹幕-子弹</cnName>
			<noMagneticB>1</noMagneticB>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<bulletSkillArr>FastGuards_missileChildDieEvent</bulletSkillArr>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>0</bulletAngle>
			<bulletLife>0.5</bulletLife>
			<lifeRandom>0.2</lifeRandom>
			<bulletWidth>35</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>7</bulletNum>
			<shootAngle>40</shootAngle>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<boomD selfB="1" bodyB="1" radius="40" />
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>14</bulletSpeed>
			<speedD a="-2" min="2" random="0.2" />
			<gravity>0.2</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">FastGuards/missile2</bulletImgUrl>
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2" soundVolume="0.3">boomEffect/boom1</hitImgUrl>
		</bullet>
		
		<bullet cnName="超级弹幕-孙弹">
			<name>FastGuards_missileSun</name>
			<cnName>超级弹幕-孙弹</cnName>
			<noMagneticB>1</noMagneticB>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>0</bulletAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>5</bulletNum>
			<shootAngle>40</shootAngle>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>20</bulletSpeed>
			<speedD random="0.2" />
			<gravity>0.1</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">FastGuards/missile3</bulletImgUrl>
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl>
			<hitImgUrl soundUrl="boomSound/microBoom2"  soundVolume="0.1">boomEffect/boom1</hitImgUrl>
		</bullet>
	</father>
	
	<father type="zombie" cnName="编织者">
		<bullet cnName="编织者-喷毒">
			<name>Weaver_shoot</name>
			<cnName>编织者-喷毒</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>-170</bulletAngle>
			<shakeAngle>15</shakeAngle>
			<bulletAngleRange>90</bulletAngleRange>
			<bulletLife>3</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.6</attackGap>
			<attackDelay>0.3</attackDelay>
			<bulletNum>5</bulletNum>				
			<shootNum>1</shootNum>					
			<shootGap>0</shootGap>					
			<shootAngle>10</shootAngle>					
			<shootPoint>-128,-75</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>20</bulletSpeed>
			<speedD random="0.5" max="25" min="15" a="-15" />
			<gravity>0.2</gravity>
			<!--<followD value="0.5" maxTime="0.3" />	 跟踪 -->
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">skillEffect/poisonClaw_enemy</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/water_hit">boomEffect/posion1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">skillEffect/poisonClaw_enemy</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="编织者-瘟疫">
			<name>Weaver_smoke</name>
			<cnName>编织者-瘟疫</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1.5</hurtRatio>
			<hitGap>0.5</hitGap>
			<noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>90</bulletAngle>
			<shakeAngle>180</shakeAngle>
			<bulletLife>10</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0.7</attackDelay>
			<bulletNum>1</bulletNum>				
			<shootNum>5</shootNum>					
			<shootGap>0</shootGap>					
			<shootPoint>0,-10</shootPoint>
			<positionD>
				<point shootPoint="0,-10"/>
				<point shootPoint="50,-20"/>
				<point shootPoint="-50,-20"/>
				<point shootPoint="100,-10"/>
				<point shootPoint="-100,-10"/>
			</positionD>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>0</bulletSpeed>
			<!--<followD value="0.5" maxTime="0.3" />	 跟踪 -->
			<!--图像动画属性------------------------------------------------------------ -->
			<bulletImgUrl raNum="5">Weaver/smoke</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/water_hit">boomEffect/posion1</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="编织者-织网">
			<name>Weaver_web</name>
			<cnName>编织者-织网</cnName>
			<sameCampB>0</sameCampB>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<noHurtEffectB>1</noHurtEffectB>
			<hitGap>2</hitGap>
			<noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>0</bulletAngle>
			<bulletLife>30</bulletLife>
			<bulletWidth>100</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>1</bulletNum>				
			<shootPoint>0,-26</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<skillArr>Weaver_web_hiding,Weaver_web_life</skillArr>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>0</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<bulletImgUrl>Weaver/web</bulletImgUrl>
		</bullet>
	</father>
	
	
	<father type="zombie" cnName="决斗者">
		<bullet cnName="决斗者-射击">
			<name>Duelist_shoot</name>
			<cnName>射击</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<twoHitGap>0.2</twoHitGap>
			<bulletSkillArr>followProducer</bulletSkillArr>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>179.9</bulletAngle>
			<bulletAngleRange>50</bulletAngleRange>
			<bulletLife>6</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.9</attackGap>
			<attackDelay>0.51</attackDelay>
			<shootPoint>-113,-54</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<skillArr>silence_wind</skillArr>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>15</bulletSpeed>
			<followD value="2"  delay="3"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="add">Duelist/bullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/body_hit">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
	</father>
</data>