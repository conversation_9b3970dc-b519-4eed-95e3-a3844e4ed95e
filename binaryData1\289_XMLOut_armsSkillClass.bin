<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="armsSkill" cnName="武器技能">
		<skill name="击中减速">
			<name>Hit_SlowMove_ArmsSkill</name>
			<cnName>击中减速</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<value>0</value>
			<mul>0.6</mul>
			<duration>2</duration>
			<range>0</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<stateEffectImg partType="2foot">skillEffect/disabled_enemy</stateEffectImg>
			<description>击中目标后减少其[1-mul]的移动速度，持续2秒。</description>
		</skill>
		
		<skill name="击毙补充弹药">
			<name>Kill_AddCharger_ArmsSkill</name>
			<cnName>击毙补充弹药</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>killTarget</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>charger</effectType>
			<value>0</value>
			<mul>0.05</mul>
			<!--附加属性------target对哪个武器加弹药：now当前、all所有 -->
			<obj>"target":"now","addType":"add"</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			<description>击毙单位后，补充当前武器5%的携弹量。</description>
		</skill>
		
		<skill name="击毙回复">
			<name>Kill_AddLifeMul_ArmsSkill</name>
			<cnName>击毙回复</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>killTarget</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>life</effectType>
			<value>0</value>
			<mul>0.04</mul>
			<!--附加属性------target对哪个武器加弹药：now当前、all所有 -->
			<obj>"target":"now","addType":"add"</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			<description>击毙单位后，补充自身4%的生命值。</description>
		</skill>
		
		<skill name="剧毒">
			<name>Hit_Poison_ArmsSkill</name>
			<cnName>剧毒</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType><noSkillDodgeB>1</noSkillDodgeB>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>poison</effectType>
			<extraValueType>hurtValue</extraValueType>
			<value>0</value>
			<mul>0.2</mul>
			<doGap>1</doGap>
			<duration>5</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<stateEffectImg partType="mouth" con="add">skillEffect/poisonousFog_hero</stateEffectImg>
			<description>击中目标后，使其每秒减少一定的生命值，减少量为武器当前造成伤害的20%，持续5秒。</description>
		</skill>
		
		<skill name="击毙溅射">
			<name>Kill_Spurting_ArmsSkill</name>
			<cnName>击毙溅射</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>killTarget</condition>
			<target>target,range,enemy</target><!-- 目标	me：自己，target：目标（攻击的目标、击毙的目标），target,random,enemy：被攻击目标中心范围内的随机目标，me,range,enemy：距离自己300范围内的敌方目标， -->
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>spurting</effectType>
			<extraValueType>nowArmsDps</extraValueType>
			<value>0</value>
			<mul>0.3</mul>
			<range>200</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			<description>击毙目标后，对其200范围内的敌方单位造成伤害，伤害值为武器战斗力的30%。</description>
		</skill>
		<skill name="振奋">
			<name>Kill_Crazy_ArmsSkill</name>
			<cnName>振奋</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>killTarget</condition>
			<target>me</target><!-- 目标	me：自己，target：目标（攻击的目标、击毙的目标），target,random,enemy：被攻击目标中心范围内的随机目标，me,range,enemy：距离自己300范围内的敌方目标， -->
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>hurtMul</effectType>
			<mul>1.2</mul>
			<duration>2</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<stateEffectImg partType="2hand" con="add" raNum="30" followPartRaB="1">skillEffect/trueshot_enemy</stateEffectImg>
			<description>击毙敌人后，自身攻击力提升20%，持续2秒。</description>
		</skill>
		
		<skill name="击中溅射">
			<name>Hit_Spurting_ArmsSkill</name>
			<cnName>击中溅射</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target,range,enemy,,,6</target><!-- 目标	me：自己，target：目标（攻击的目标、击毙的目标），target,random,enemy：被攻击目标中心范围内的随机目标，me,range,enemy：距离自己300范围内的敌方目标， -->
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>spurting</effectType>
			<extraValueType>hurtValue</extraValueType>
			<value>0</value>
			<mul>0.1</mul>
			<range>200</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			<description>击中目标后，对其200范围内的敌方单位造成伤害，伤害值为武器所造成伤害的10%。</description>
		</skill>
		
		<skill name="致残">
			<name>Hit_disabled_ArmsSkill</name>
			<cnName>致残</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>hurtMul</effectType>
			<mul>0.75</mul>
			<duration>2</duration>
			<stateEffectImg partType="2hand">skillEffect/disabled_enemy</stateEffectImg>
			<description>击中目标后减少其25%的攻击力，持续2秒。</description>
		</skill>
		
		
		<skill name="击中麻痹">
			<name>Hit_Paralysis_ArmsSkill</name>
			<cnName>击中麻痹</cnName>
			<createByArmsTypePro>effectProArr</createByArmsTypePro>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<effectProArr>0.03</effectProArr>
			<mul>0</mul>
			<duration>2</duration>
			<!--图像------------------------------------------------------------ --> 
			<targetEffectImg soundUrl="sound/paralysis_enemy_hit"></targetEffectImg>
			<stateEffectImg con="add" >skillEffect/paralysis_enemy</stateEffectImg>
			<description>击中目标后，有[effectProArr.0]的几率使其麻痹，持续[duration]秒。</description>
		</skill>
		
		<skill name="致盲">
			<name>Hit_blindness_ArmsSkill</name>
			<cnName>致盲</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>lostPro</effectType>
			<mul>0.25</mul>
			<duration>3</duration>
			<stateEffectImg partType="2eye">skillEffect/disabled_enemy</stateEffectImg>
			<description>击中目标后使其失去准心，使其攻击成功率降低[mul]，持续3秒。</description>
		</skill>
		
		
		<skill name="击中回复">
			<name>Hit_AddLifeMul_ArmsSkill</name>
			<cnName>击中回复</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>Hit_AddLifeMul</effectType>
			<extraValueType>hurtValue</extraValueType>
			<value>0.008</value>
			<mul>0</mul>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			<description>击中目标后，补充自身生命值，补充值为武器所造成伤害的1%。</description>
		</skill>
		<skill name="击中沉默">
			<name>Hit_silence_ArmsSkill</name>
			<cnName>击中沉默</cnName>
			<createByArmsTypePro>effectProArr</createByArmsTypePro>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>silenceBAndClearState</effectType>
			<effectProArr>0.015</effectProArr>
			<value>1</value>
			<duration>2</duration>
			<!--图像------------------------------------------------------------ --> 
			<targetEffectImg soundUrl="sound/silence_enemy"></targetEffectImg>
			<stateEffectImg partType="mouth" con="add">skillEffect/silence_enemy</stateEffectImg>
			<description>击中目标后，有[effectProArr.0]的几率使其无法释放技能，持续[duration]秒，同时清除目标身上一些状态。</description>
		</skill>
		<skill name="击中派生"><!-- dps-被动 -->
			<name>Hit_hitMissile_godArmsSkill</name>
			<cnName>击中派生</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<effectProArr>0.06</effectProArr>
			<extraValueType>nowArmsDps</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<!-- 子弹所需 -->
			<obj>"name":"hitMissile_hero","site":"shootPoint","flipB":false</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			<description>每次射击都有[effectProArr.0]的概率派生跟踪导弹。导弹伤害为当前武器战斗力值。</description>
		</skill>
	</father>
	<father name="godArmsSkill" cnName="神级武器技能">
		
		<skill name="共振">
			<name>resonateGod</name><![CDATA[ resonateGod 不能改名，需要在ArmsLauncher.setArms中修改 ]]>
			<cnName>共振</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>resonateGod</effectType>
			<description>随机选一把携带的同类武器，触发其神级技能。部分技能无效。</description>
		</skill>
		<skill name="老练">
			<name>seasonedGod</name>
			<cnName>老练</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>seasonedGod</effectType><changeHurtB>1</changeHurtB>
			<mul>0.05</mul>
			<secMul>3</secMul>
			<description>从武器获得之日起，每过1天，武器在战斗中的伤害就增加[mul]（最高[secMul]）。</description>
		</skill>
		<skill name="远射">
			<name>longShootGod</name>
			<cnName>远射</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>longShootGod</effectType>
			<extraValueType>armsShootRange</extraValueType>
			<value>500</value>
			<mul>0.0001</mul>
			<secMul>0.2</secMul>
			<duration>3</duration>
			<description>击中敌人后给自己附上增伤状态，增加伤害=(武器射程-[value])*0.0001，最高增加[secMul]，持续[duration]秒。</description>
			<stateEffectImg name="crazy_vehicle_state"/>
		</skill>
		<![CDATA[]]>
		
		
		<skill name="超重">
			<name>overweightGod</name>
			<cnName>超重</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>overweightGod</effectType>
			<extraValueType>armsBaseAttackGap</extraValueType>
			<duration>0.1</duration>
			<description>使敌人重力变大，持续时间是武器初始射击间隔的3倍。</description>
			<stateEffectImg name="stoneFoot"/>
		</skill>
		
		
		
		<skill name="瞬秒">
			<name>Hit_seckill_godArmsSkill</name>
			<cnName>瞬秒</cnName>
			<createByArmsTypePro>effectProArr</createByArmsTypePro>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>seckill</effectType>
			<extraValueType>nowArmsDps</extraValueType>
			<effectProArr>0.03</effectProArr>
			<mul>50</mul>
			<targetEffectImg></targetEffectImg>
			<description>当敌人的血量低于60%同时血量少于这把武器战斗力的50倍时，击中它就有[effectProArr.0]的几率直接瞬秒它！</description>
		</skill>
		<skill name="绝灭">
			<name>Hit_finalkill_godArmsSkill</name>
			<cnName>绝灭</cnName>
			<createByArmsTypePro>effectProArr</createByArmsTypePro>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>seckill</effectType>
			<extraValueType>nowArmsDps</extraValueType>
			<effectProArr>0.13</effectProArr>
			<mul>20</mul>
			<targetEffectImg></targetEffectImg>
			<description>当敌人的血量低于60%同时血量少于这把武器战斗力的20倍时，击中它就有[effectProArr.0]的几率直接击毙它！</description>
		</skill>
		<skill name="快感">
			<name>Hit_crazy_godArmsSkill</name>
			<cnName>快感</cnName>
			<createByArmsTypePro>effectProArr</createByArmsTypePro>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>crazyGun</effectType>
			<effectProArr>0.02</effectProArr>
			<mul>1.5</mul><!-- 非射击单位移动速度增加1.6倍 -->
			<duration>3</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/crazy_hero"></meEffectImg>
			<stateEffectImg partType="2eye" con="filter" raNum="30">skillEffect/smallFire</stateEffectImg>
			<stateEffectImg2 partType="2eye" con="add">skillEffect/crazy_hero_eye</stateEffectImg2>
			<description>击中目标有[effectProArr.0]的几率使自己进入狂暴状态，攻击速度加倍，不消耗子弹（非修罗模式），持续4秒。</description>
		</skill>
		<skill name="搏命">
			<name>Hit_atry_godArmsSkill</name>
			<cnName>搏命</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurt</effectType>
			<mul>0.25</mul>
			<!-- 修改伤害所需 -->
			<obj>"type":"atry","per":0.6</obj>
			<description>自身生命值低于[obj.per]的时候，生命值越低攻击力越高，最高加成150%的攻击力。</description>
		</skill>
		<skill name="七步毒"><!-- dps -->
			<name>Hit_posion7_godArmsSkill</name>
			<cnName>七步毒</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType><noSkillDodgeB>1</noSkillDodgeB>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>poison7</effectType>
			<extraValueType>hurtValue</extraValueType>
			<mul>0.2</mul>
			<doGap>0.2</doGap>
			<duration>3</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="2foot" con="filter" raNum="30">skillEffect/poisonClaw_enemy</stateEffectImg>
			<description>击中目标后，目标移动身体则每0.2秒都将受到伤害，伤害值为武器所造成伤害的[mul]。状态持续[duration]秒。</description>
		</skill>
		<skill cnName="爆石"><!-- dps -->
			<name>Hit_imploding_godArmsSkill</name>
			<cnName>爆石</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet_imploding</effectType>
			<effectProArr>0.08</effectProArr><!--  -->
			<extraValueType>noSkillAddHurt</extraValueType><!-- 附加值类型为单位dps系数 -->
			<!-- 子弹所需 -->
			<obj>"name":"imploding_godArmsSkill","site":"me","flipB":true</obj>
			<!--图像------------------------------------------------------------ -->
			<description>每次击中目标都有[effectProArr.0]的几率使自身爆发5颗石头，每颗石头伤害值为武器伤害的1倍。修罗模式下概率大幅降低。</description>
		</skill>
		<skill cnName="引爆"><!-- dps-主动 -->
			<name>Hit_pointBoom_godArmsSkill</name>
			<cnName>引爆</cnName>
			<createByArmsTypePro>effectProArr</createByArmsTypePro>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--英雄技能属性------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<effectProArr>0.03</effectProArr>
			<extraValueType>nowArmsDps</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<!-- 子弹所需 -->
			<obj>"name":"pointBoom_hero","site":"hitPosition","flipB":false</obj>
			<description>每次击中目标都有[effectProArr.0]的几率在目标处引爆炸弹，对周围100码内的敌人造成伤害，伤害值为武器战斗力的2倍。</description>
			
		</skill>
		<skill cnName="刷新"><!-- dps-主动 -->
			<name>Hit_fleshSkill_godArmsSkill</name>
			<cnName>刷新</cnName>
			<createByArmsTypePro>effectProArr</createByArmsTypePro>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--英雄技能属性------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>fleshLastSkill</effectType>
			<effectProArr>0.02</effectProArr>
			<mul>0.15</mul>
			<!-- 子弹所需 -->
			<meEffectImg soundUrl="sound/skillFlesh"></meEffectImg>
			<description>每次击中目标都有[effectProArr.0]的几率使最近使用的那一个主动技能的冷却时间回复20%。</description>
		</skill>
		
		
		<!-- 特殊技能，不参加随机列表 -->
		<skill name="击中闪电">
			<name>Hit_Lightning_godArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>击中闪电</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me,near,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>lightning_godArmsSkill</effectType>
			<effectProArr>0.05</effectProArr>
			<extraValueType>nowArmsDps</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<value>0</value>
			<mul>1</mul>
			<range>300</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<description>击中敌人后有[effectProArr.0]的几率产生高压电流，对[range]码内最近的敌人造成1倍于武器战斗力的伤害。</description>
		</skill>
		<skill index="2" name="击中眩晕">
			<name>Hit_Hammer_godArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>击中眩晕</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>dizziness</effectType>
			<effectProArr>0.07</effectProArr>
			<duration>2</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
			<description>击中目标有[effectProArr.0]的几率使目标陷入眩晕状态，持续[duration]秒。</description>
		</skill>
		<skill index="3" name="超级派生"><!-- dps-被动 -->
			<name>Hit_SuperMissile_godArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>超级派生</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<effectProArr>0.09</effectProArr>
			<mul>1</mul>
			<extraValueType>nowArmsDps</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<!-- 子弹所需 -->
			<obj>"name":"hitMissile_hero","site":"shootPoint","flipB":false</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			<description>每次射击都有9%的概率派生跟踪导弹。导弹伤害为当前武器战斗力值。</description>
		</skill>
	</father>	
	<father name="godArmsSkill" cnName="特殊武器神级技能-不参加随机">
		<skill name="超级减速">
			<name>superHitSlow</name><noRandomListB>1</noRandomListB>
			<cnName>超级减速</cnName><ignoreImmunityB>1</ignoreImmunityB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<value>0</value>
			<mul>0.5</mul>
			<duration>2</duration>
			<range>0</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<stateEffectImg partType="2foot">skillEffect/disabled_enemy</stateEffectImg>
			<description>击中目标后减少其[1-mul]的移动速度，持续2秒。无视技能免疫。</description>
		</skill>
		<skill name="超级麻痹">
			<name>Hit_Paralysis_ArmsSkill2</name><noRandomListB>1</noRandomListB>
			<cnName>超级麻痹</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum><ignoreImmunityB>1</ignoreImmunityB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<effectProArr>0.02</effectProArr>
			<mul>0</mul>
			<duration>4</duration>
			<!--图像------------------------------------------------------------ --> 
			<targetEffectImg soundUrl="sound/paralysis_enemy_hit"></targetEffectImg>
			<stateEffectImg con="add" >skillEffect/paralysis_enemy</stateEffectImg>
			<description>击中目标后，有[effectProArr.0]的几率使其麻痹，持续[duration]秒。</description>
		</skill>
		
		<skill name="炎爆">
			<name>Hit_burn_ArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>炎爆</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType><overlyingB>1</overlyingB>
			<effectType>burn_arms</effectType>
			<valueString>Hit_burn_ArmsSkill_link</valueString>
			<value>100</value>
			<duration>2</duration>
			<stateEffectImg bodyColor="0xFF0000"></stateEffectImg>
			<description>每次伤害都会给目标叠加1层“火焰”，当叠加到[value]层时对目标造成巨大的爆炸伤害。</description>
		</skill>
		<skill name="冷凝">
			<name>cold_ArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>冷凝</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType><overlyingB>1</overlyingB>
			<effectType>cold_arms</effectType>
			<value>40</value>
			<mul>0.005</mul>
			<duration>2</duration>
			<stateEffectImg bodyColor="0x00E0F5"></stateEffectImg>
			<description>每次伤害都会给目标叠加1层“霜冻”，每层降低目标[mul]的防御力，最高叠加[value]层。</description>
		</skill>
		<skill name="蚀骨">
			<name>erosion_ArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>蚀骨</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType><overlyingB>1</overlyingB>
			<effectType>cold_arms</effectType>
			<value>30</value>
			<mul>0.01</mul>
			<duration>2</duration>
			<stateEffectImg bodyColor="0xFF33FF"></stateEffectImg>
			<description>每次伤害都会给目标叠加1层“腐蚀”，每层降低目标[mul]的防御力，最高叠加[value]层。</description>
		</skill>
		
		<skill name="痛击">
			<name>beatBack_ArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>痛击</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType><overlyingB>1</overlyingB>
			<effectType>beatBack_arms</effectType>
			<value>6</value>
			<duration>2</duration>
			<stateEffectImg bodyColor="0xFF0000"></stateEffectImg>
			<description>击中同时推动目标一段距离，蓄力越久推动距离越远。</description>
		</skill>
		<skill name="连弩">
			<name>combo_ArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>连弩</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>heroOneShoot</condition><!-- cd结束后触发 -->
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_combo</effectType>
			<extraValueType>nowArmsTrueDps</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<value>10</value>
			<mul>0.04</mul>
			<duration>2</duration>
			<!-- 子弹所需 -->
			<obj>"name":"combo_crossbow"</obj>
			<!--图像------------------------------------------------------------ -->
			<description>每次射击都将发射连弩，蓄力越久连弩数量越多，单发连弩伤害为武器伤害的[mul]倍。</description>
		</skill>
		<skill cnName="粘性"><!-- dps -->
			<name>viscous_ArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>粘性</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>killTarget</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>no</effectType>
			<description>礼炮发射出的糖果会粘在地面或者墙壁上，持续30秒，怪物经过时会引爆糖果。</description>
		</skill>
		<skill name="超级粘性">
			<name>viscousSuper_ArmsSkill</name>
			<cnName>超级粘性</cnName><noRandomListB>1</noRandomListB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>lostPro</effectType>
			<mul>0.25</mul>
			<duration>3</duration>
			<stateEffectImg partType="2eye">skillEffect/disabled_enemy</stateEffectImg>
			<description>发射的糖果会粘在地面或墙上，敌人经过时会引爆糖果，同时降低敌人攻击成功率。</description>
		</skill>
		
		<skill name="火焰始祖"><!-- dps-被动 -->
			<name>flyDragonHead</name><noRandomListB>1</noRandomListB>
			<cnName>火焰始祖</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>loopClick</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<extraValueType>nowArmsTrueDps</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<!-- 子弹所需 -->
			<obj>"name":"flyDragonHead","site":"shootPoint","flipB":false</obj>
			<duration>0.4</duration>
			<!--图像------------------------------------------------------------ -->
			<description>连续射击时候，每隔一段时间喷射出异祖龙的火焰，追击敌方单位，无子弹时候也能触发。</description>
		</skill>
		<skill name="贯穿波"><!-- dps-被动 -->
			<name>bangerGunSkill</name><noRandomListB>1</noRandomListB>
			<cnName>贯穿波</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>loopClick</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<extraValueType>nowArmsTrueDps</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<!-- 子弹所需 -->
			<obj>"name":"bangerGunSkill","site":"shootPoint","flipB":false</obj>
			<duration>1</duration>
			<!--图像------------------------------------------------------------ -->
			<description>连续射击时，每隔一段时间产生一个缓慢移动的光波，贯穿所有敌方单位。</description>
		</skill>
		<skill name="无限火力"><!-- dps-被动 -->
			<name>infiniteFire</name><noRandomListB>1</noRandomListB>
			<cnName>无限火力</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>loopClick</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>noReduceCapacityB</effectType>
			<duration>0.5</duration>
			<!--图像------------------------------------------------------------ -->
			<description>连续射击超过1秒，武器将不消耗子弹。</description>
		</skill>
		<skill name="跳斩"><!-- dps -->
			<name>shotgunBlade_ArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>跳斩</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>killTarget</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>no</effectType>
			<description>使用副手时，会瞬移到鼠标位置，发起跳斩攻击。</description>
			<stateEffectImg con="add">Striker/bladeUpEffect</stateEffectImg>
			<stateEffectImg2 con="add">Striker/bladeDownEffect</stateEffectImg2>
		</skill>
		<skill name="上帝之杖">
			<name>godMace_ArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>上帝之杖</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>godMace_ArmsSkill</effectType>
			<extraValueType>nowArmsHurt</extraValueType>
			<effectProArr>0.05</effectProArr>
			<value>4</value>
			<range>250</range>
			<duration>0.2</duration>
			<targetEffectImg raNum="1" soundUrl="sound/laserShoot2">bulletHitEffect/purpleLaser</targetEffectImg>
			<description>击中目标后有[effectProArr.0]概率对目标造成范围伤害，敌人越多伤害越高。</description>
		</skill>
	</father>	
	<father name="godArmsSkill" cnName="黑色武器神级技能-不参加随机">
		<skill name="风雷">
			<name>windThunder_ArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>风雷</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>windThunder</effectType>
			<extraValueType>nowArmsTrueDps</extraValueType>
			<mul>0.12</mul>
			<value>33</value>
			<range>200</range>
			<duration>0.2</duration>
			<description>移动能够积蓄电能，击中目标后消耗电能，同时产生6条闪电随机轰击附近的敌方单位。</description>
			<stateEffectImg noShowB="1" noFollowB="1" randomRange="1" con="add">bullet/lightning1</stateEffectImg>
			<stateEffectImg2 noShowB="1" noFollowB="1" randomRange="1" con="add">bullet/lightning2</stateEffectImg2>
		</skill>
		<skill name="影灭"><!-- dps-被动 -->
			<name>laserKill_godArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>影灭</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>floorState</otherConditionArr>
			<conditionString>squat</conditionString>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<extraValueType>nowArmsHurt</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<!-- 子弹所需 -->
			<obj>"name":"laserKill_godArmsSkill","site":"shootPoint","flipB":false</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			<description>下蹲状态下，每次射击都会额外向目标发射激光炮，激光伤害为当前武器面板单伤的20%。</description>
		</skill>
		
		<skill cnName="爆沙"><!-- dps -->
			<name>imploding_blackArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>爆沙</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<effectProArr>0.1</effectProArr><!--  -->
			<extraValueType>noSkillAddHurt</extraValueType><!-- 附加值类型为单位dps系数 -->
			<!-- 子弹所需 -->
			<obj>"name":"imploding_blackArmsSkill","site":"me","flipB":true</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/imploding_enemy"></meEffectImg>
			<description>击中目标后有[effectProArr.0]概率向前方爆裂出多个火花，轰炸前方所有目标，每条火花伤害值为武器伤害的1倍。</description>
		</skill>
		
		<skill name="爆胆"><!-- dps-被动 -->
			<name>fear_godArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>爆胆</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>noState</otherConditionArr>
			<conditionString>fear_noBuff</conditionString>
			<target arenaB="0">target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>armsFear</effectType>
			<extraValueType>nowArmsHurt</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<effectProArr>0.5</effectProArr>
			<mul>0.6</mul>
			<duration>2</duration>
			<linkArr>fear_noBuff</linkArr>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="filter">skillEffect/screaming_hero_target</stateEffectImg>
			<targetEffectImg soundUrl="sound/magicHit1" partType="mouth" con="add">skillEffect/screaming_hero</targetEffectImg>
			<description>击中后有[effectProArr.0]概率恐惧目标并造成额外伤害(武器面板单伤x[mul])。</description>
		</skill>
		<skill name="爆震"><!-- dps-被动 -->
			<name>fear_godArmsSkill2</name><mustLv>2</mustLv>
			<cnName>爆震</cnName><noRandomListB>1</noRandomListB>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>noState</otherConditionArr>
			<conditionString>fear_noBuff</conditionString>
			<target arenaB="0">target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>armsFear</effectType>
			<extraValueType>nowArmsHurt</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<effectProArr>0.5</effectProArr>
			<mul>1</mul>
			<duration>2</duration>
			<linkArr>fear_noBuff</linkArr>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="filter">skillEffect/screaming_hero_target</stateEffectImg>
			<targetEffectImg soundUrl="sound/magicHit1" partType="mouth" con="add">skillEffect/screaming_hero</targetEffectImg>
			<description>击中后有[effectProArr.0]概率恐惧目标并造成额外伤害(武器面板单伤x[mul])，并减速目标。</description>
		</skill>
		
		
		<![CDATA[
		<skill name="溃防">
			<name>sickle_godArmsSkill</name><noBeClearB>1</noBeClearB>
			<cnName>破甲</cnName><noRandomListB>1</noRandomListB><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType><changeHurtB>1</changeHurtB>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>overwhelmed</effectType>
			<mul>1.2</mul>
			<description>对拥有技能防弹外壳、防弹钢甲的敌人造成额外[mul-1]的伤害。</description>
		</skill>
		<skill>
			<name>sickle_godArmsSkill2</name><noBeClearB>1</noBeClearB>
			<cnName>溃甲</cnName><noRandomListB>1</noRandomListB><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType><changeHurtB>1</changeHurtB>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>overwhelmed</effectType>
			<mul>1.4</mul>
			<description>对拥有技能防弹外壳、防弹钢甲的敌人造成额外[mul-1]的伤害。</description>
		</skill>
		]]>
		<skill name="飞镰"><!-- dps-被动 -->
			<name>sickle_godArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>飞镰</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<effectProArr>0.1</effectProArr><!--  -->
			<extraValueType>nowArmsHurt</extraValueType><!-- 附加值类型为单位dps系数 -->
			<mul>4</mul>
			
			<!-- 子弹所需 -->
			<obj>"name":"sickle_godArmsSkill","site":"hitPosition","flipB":false</obj>
			<!--图像------------------------------------------------------------ -->
			<description>击中目标后有[effectProArr.0]概率产生连续弹射的飞镰，飞镰最多弹射5次。</description>
		</skill>
		<skill name="跟踪飞镰"><!-- dps-被动 -->
			<name>sickle_godArmsSkill2</name><noRandomListB>1</noRandomListB>
			<cnName>跟踪飞镰</cnName>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<effectProArr>0.1</effectProArr><!--  -->
			<extraValueType>nowArmsHurt</extraValueType><!-- 附加值类型为单位dps系数 -->
			<mul>5</mul>
			<!-- 子弹所需 -->
			<obj>"name":"sickle_godArmsSkill2","site":"hitPosition","flipB":false</obj>
			<!--图像------------------------------------------------------------ -->
			<description>击中目标后有[effectProArr.0]概率产生连续弹射的跟踪飞镰，无视防弹外壳、防弹钢甲，并对此类敌人额外造成40%的伤害。</description>
		</skill>
		<skill name="战修罗">
			<name>demonAddHurt</name>
			<cnName>战修罗</cnName><noRandomListB>1</noRandomListB><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType><changeHurtB>1</changeHurtB>
			<condition>hit</condition>
			<otherConditionArr>isDemon</otherConditionArr>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurtNoCondition</effectType>
			<mul>1.20</mul>
			<description>修罗模式下伤害增加[mul-1]。</description>
		</skill>
		<![CDATA[
		<skill name="突袭">
			<name>noFoggyDef</name><noBeClearB>1</noBeClearB><ignoreNoSkillB>1</ignoreNoSkillB>
			<cnName>突袭</cnName><noRandomListB>1</noRandomListB><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>puMainPlayer,meHaveState</otherConditionArr>
			<conditionString>through_hero</conditionString>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>noFoggyDefB</effectType>
			<duration>0.5</duration>
			<description>主角处于金刚钻状态时，射击敌人使其“抵御”技能无法被触发。</description>
		</skill>
		]]>
		<skill name="突袭">
			<name>noFoggyDef</name><noBeClearB>1</noBeClearB><ignoreNoSkillB>1</ignoreNoSkillB>
			<cnName>突袭</cnName><noRandomListB>1</noRandomListB><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>loopClick</condition>
			<otherConditionArr></otherConditionArr>
			
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>noFoggyDefB</effectType>
			<duration>0.2</duration>
			<range>999999</range>
			<description>持续射击会封锁在场所有敌人的“抵御”技能。</description>
		</skill>
		<skill name="惩罚"><![CDATA[武器技能，修改伤害的，如果没有加changeHurtB，就无效，一定要记住！！]]>
			<name>immuneNemesis</name><noBeClearB>1</noBeClearB>
			<cnName>惩罚</cnName><noRandomListB>1</noRandomListB><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType><changeHurtB>1</changeHurtB>
			<condition>hit</condition>
			<otherConditionArr>haveSkillOr</otherConditionArr>
			<conditionString>State_SpellImmunity,immune,treater_knights,FoggyDefence</conditionString>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>immuneNemesis</effectType>
			<mul>1.6</mul>
			<description>对拥有技能净化器、免疫、技能免疫、抵御的敌人造成额外[mul-1]的伤害。</description>
		</skill>
		
		
		
		
		
		
		
		<skill name="打滑">
			<name>trackslipGod</name>
			<cnName>打滑</cnName><noRandomListB>1</noRandomListB>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>trackslipGod</effectType>
			<extraValueType>armsBaseAttackGap</extraValueType>
			<duration>0.1</duration>
			<description>使敌人走路很滑，持续时间是武器初始射击间隔的2倍。</description>
			<stateEffectImg name="waterFoot"/>
		</skill>
		<skill name="超级共振">
			<name>resonateGodSuper</name><![CDATA[ resonateGodSuper 不能改名，需要在ArmsLauncher.setArms中修改 ]]>
			<cnName>超级共振</cnName><noRandomListB>1</noRandomListB>
			<haveEffectBuletHitNum>1</haveEffectBuletHitNum>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>resonateGod</effectType>
			<description>随机选一把携带武器（步枪、手枪必须为同类武器），触发其神级技能。部分技能无效。</description>
		</skill>
		
		<skill cnName="子母弹"><!-- dps -->
			<name>lash_ArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>子母弹</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>killTarget</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>no</effectType>
			<description>母弹自爆后内部弹射出7颗更小的子弹，每颗伤害为母弹的90%。</description>
		</skill>
		
		<skill cnName="烟花"><!-- dps -->
			<name>beadCrossbow_ArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>烟花</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>killTarget</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>no</effectType>
			<description>这就是一把烟花炮！释放烟花后，坠落的烟花对敌人造成伤害。</description>
		</skill>
		<skill cnName="轨迹绘制"><!-- dps -->
			<name>editBulletPath</name><noRandomListB>1</noRandomListB>
			<cnName>轨迹编辑</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>no</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>no</effectType>
			<description>玩家可绘制子弹运动轨迹。</description>
		</skill>
	</father>	
	
	
	
	
	
	
	<father name="godArmsSkill_link" cnName="链接">
		<skill name="炎爆-爆">
			<name>Hit_burn_ArmsSkill_link</name>
			<cnName>炎爆-爆</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<extraValueType>nowArmsTrueDps</extraValueType>
			<effectType>crit</effectType>
			<value>1</value>
			<mul>7</mul>
			<targetEffectImg partType="body" soundUrl="boomSound/midBoom">boomEffect/boom3</targetEffectImg>
			<description>火焰爆炸伤害。</description>
		</skill>
		<skill name="爆胆-无法叠加得buff"><!-- dps-被动 -->
			<name>fear_noBuff</name><noRandomListB>1</noRandomListB>
			<cnName>爆胆-无法叠加得buff</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>3</duration>
		</skill>
		
		<skill>
			<name>sickle_godArmsSkill2_link</name><noBeClearB>1</noBeClearB>
			<cnName>超级飞镰-破甲</cnName><noRandomListB>1</noRandomListB><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType><changeHurtB>1</changeHurtB>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>overwhelmed</effectType>
			<mul>1.4</mul>
			<description>对拥有技能防弹外壳、防弹钢甲的敌人造成额外[mul-1]的伤害。</description>
		</skill>
		
	</father>		
</data>