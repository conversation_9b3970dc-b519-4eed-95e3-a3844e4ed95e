<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="normal"><![CDATA[设置成normal就可以直接fixed了，如果不这样做，就无法fixed]]>
		<gather name="派生训练日">
			<level name="missileTrain">
				<!-- 发兵集************************************************ -->
				<info enemyLv="86" noMoreB="1" diy="missileTrain"/>
				<fixed target="YangMei_4" info="no" drop="all" unitG="all" rectG="all" eventG="all"/>
				<!-- 基本属性 -->
				<sceneLabel>YangMei</sceneLabel>
			</level>
		</gather>
		<gather name="宠物训练场">
			<level name="petTrain">
				<!-- 发兵集************************************************ -->
				<info enemyLv="89" diy="petTrain"/>
				<fixed target="GongMu_1" info="no" drop="all" unitG="all" rectG="all" eventG="all"/>
				<!-- 基本属性 -->
				<sceneLabel>GongMu</sceneLabel>
			</level>
		</gather>
		
		<gather name="银狐挑战">
			<level name="pistolFoxTrain">
				<!-- 发兵集************************************************ -->
				<info enemyLv="90" diy="pistolFoxTrain"/>
				<fixed target="JungleOutside_1" info="no" drop="all" unitG="all" rectG="all" eventG="all"/>
				<!-- 基本属性 -->
				<sceneLabel>JungleOutside</sceneLabel>
			</level>
		</gather>
		<gather name="载具训练">
			<level name="vehicleTrain">
				<!-- 发兵集************************************************ -->
				<info enemyLv="92" noMoreB="1" diy="vehicleTrain"/>
				<fixed target="BaWang_1" info="no" drop="all" unitG="all" rectG="all" eventG="all"/>
				<!-- 基本属性 -->
				<sceneLabel>BaWang</sceneLabel>
			</level>
		</gather>
		<gather name="限弹挑战">
			<level name="bulletLimitTrain">
				<!-- 发兵集************************************************ -->
				<info enemyLv="92" noMoreB="1" diy="bulletLimitTrain" preBulletArr="christmasGun,shotgunBlade,beadCrossbow,rifleDragon" />
				<fixed target="LvSen_2" info="no" drop="all" unitG="all" rectG="all" eventG="all"/>
				<!-- 基本属性 -->
				<sceneLabel>LvSen</sceneLabel>
			</level>
		</gather>
		<gather name="青蜂训练">
			<level name="rifleHornetTrain">
				<!-- 发兵集************************************************ -->
				<info enemyLv="93" diy="rifleHornetTrain" preSkillArr="magneticField_enemy" />
				<fixed target="ShangSha_1" info="no" drop="all" unitG="all" rectG="all" eventG="all"/>
				<!-- 基本属性 -->
				<sceneLabel>ShangSha</sceneLabel>
			</level>
		</gather>
		<gather name="闪电塔训练">
			<level name="lightningTowerTrain">
				<!-- 发兵集************************************************ -->
				<info enemyLv="93" diy="lightningTowerTrain"/>
				<fixed target="DongFeng_1" info="no" drop="all" unitG="all" rectG="all" eventG="all"/>
				<!-- 基本属性 -->
				<sceneLabel>DongFeng</sceneLabel>
			</level>
		</gather>
	</father>
	
	<father name="deputyLevel">
		<gather name="荆棘之路">
			<level name="thornyRoad">
				<info enemyLv="84" diy="thornyRoad" noMoreB="1" dropSmallMapB="1" preBulletArr="thornyRoad" />
				<drop coin="0" exp="0" arms="0" equip="0" skillStone="0" taxStamp="0" />
				<sceneLabel>NanTang</sceneLabel>
				<rectG>
					<rect id="r_birth">63,741,51,75</rect>
					<rect id="r_over">3742,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">120,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					<rect label="addCharger">2126,120,71,60</rect>
					<rect label="addCharger">1113,324,71,60</rect>
				</rectG>
			</level>
		</gather>
		<gather name="保卫北斗">
			<level name="defendBeiDou">
				<info enemyLv="85" diy="defendBeiDou" noMoreB="1" preBulletArr="yearPig,defendBeiDou" />
				<drop coin="0" exp="0" arms="0" equip="0" skillStone="0" taxStamp="0" />
				<sceneLabel>BeiDou</sceneLabel>
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">2942,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">120,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					<rect label="addCharger">2126,170,71,60</rect>
					<rect label="addCharger">1113,374,71,60</rect>
				</rectG>
				<eventG>
					<group>
						<event id="e1_4">
							<condition delay="0.01">task:state; defendBeiDou:complete</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
				
			</level>
		</gather>	
		<gather name="雷霆的诞生">
			<level name="thunderboltBirth">
				<info enemyLv="87"/>
				<sceneLabel>PrisonDoor</sceneLabel>
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="监狱僵尸" num="4" />
						<unit cnName="战斗僵尸" num="4" />
						<unit cnName="鬼影战士" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="监狱僵尸" num="4" />
						<unit cnName="战斗僵尸" num="4" />
						<unit cnName="鬼影战士" num="2.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="雷霆" unitType="boss" num="1" dpsMul="0.3" lifeMul="5"/>
					</unitOrder>
				</unitG>
				<rectG>
					<rect id="r_birth">1600,1300,285,72</rect><rect id="r_over">3448,1264,60,132</rect><rect id="r1">52,800,286,72</rect><rect id="r2">1600,800,286,72</rect><rect id="r3">2825,800,286,72</rect>
					<rect label="addCharger">207,1256,68,68</rect><rect label="addCharger">3174,1256,68,68</rect>
				</rectG>
				<eventG>
					<group>
						<event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="2">enemyNumber:less_1</condition></event>
						<event id="e2_3">
							<condition doNumber="12" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1"><condition delay="2">enemyNumber:less_1</condition></event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_11"><condition delay="1">enemyNumber:less_1</condition><order>level; rebirthAllMore</order></event>	
						<event id="e2_11"><condition delay="1"></condition><order>say; startList:s1</order></event>	
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		
		<gather name="招募退役兵">
			<level name="threePartner">
				<info enemyLv="94" noMoreB="1"/>
				<sceneLabel>PrisonDeep</sceneLabel>
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="we_task" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="丛林特种兵" num="3" dpsMul="0.01" dpsSpecielLabel="heroLvDps" aiOrder="followBodyAttack:我" levelSetting="hero+0" lifeMul="999999" noUnderHurtB="1" /><!-- 某个单位的属性 -->
					</unitOrder>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="独眼僵尸" num="6"/>
						<unit cnName="监狱僵尸" num="9"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="独眼僵尸" num="6"/>
						<unit cnName="监狱僵尸" num="1.5"/>
						<unit cnName="鬼影战士" lifeMul="2" num="1.5"/>
						
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="独眼僵尸" num="4"/>
						<unit cnName="监狱僵尸" num="6"/>
						<unit cnName="屠刀僵尸" num="3"/>
						<unit cnName="鬼影战士" lifeMul="2" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="嗜血尸狼" unitType="boss" lifeMul="1.5" dpsMul="1.2"/><!-- 某个单位的属性 -->
						<unit cnName="火炮尸狼" unitType="boss" lifeMul="0.8" dpsMul="1.4"/><!-- 某个单位的属性 -->
					</unitOrder>
				</unitG>
				<rectG>
					<rect id="r_birth">41,2000,210,68</rect>
					<rect id="r_over">3450,1992,52,108</rect>
					<rect id="r1">41,1668,400,94</rect>
					<rect id="r2">1373,1448,340,72</rect>
					<rect id="r3">2060,900,436,74</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">167,1212,40,40</rect>
					<rect label="addCharger">3368,1450,40,40</rect>
				</rectG>
				<eventG>
					<group>
						<event id="e2_1">
							<condition doNumber="1"></condition>
							<order>createUnit:we_task; r_birth</order>
						</event>
						<event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order> 
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order> 
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order> 
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy4; r1</order> 
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<event id="e2_11"><condition>enemyNumber:less_1</condition><order>say; startList:s1</order></event>	
						<event id="e_win">
							<condition>say:listOver; s1</condition>
							<order>task:now; complete</order>
							<order>more; openPartner3</order>
							<order>alert:yes; 队友的上场数量增加！</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		
		
		<gather name="小美的身世">
			<level name="storyXiaoMei">
				<info enemyLv="88" noMoreB="1" diy="storyXiaoMei" />
				<sceneLabel>DiXia</sceneLabel>
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="天鹰小美" armsRange="scorpionCrossbow" num="1" lifeMul="0.3" dpsMul="40" dpsSpecielLabel="heroLvDps" aiOrder="followBodyAttack:我" dieGotoState="stru"/><!-- 某个单位的属性 -->
					</unitOrder>
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="橄榄僵尸" num="6" />
						<unit cnName="毒蛛" num="9"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="银锤" num="6"/>
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸空军总管" lifeMul="2" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="银锤" num="4"/>
						<unit cnName="毒蛛" num="6"/>
						<unit cnName="橄榄僵尸" num="3"/>
						<unit cnName="僵尸狙击兵" num="0.75"/>
						<unit cnName="僵尸空军总管" lifeMul="2" num="0.75"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="钢铁僵尸王" unitType="boss" lifeMul="1.4" dpsMul="1.3"/><!-- 某个单位的属性 -->
					</unitOrder>
				</unitG>
				<rectG>
					<rect id="r_birth">2910,356,158,168</rect><rect id="r_over">3751,1314,48,112</rect>
					<rect id="r1">104,1000,336,156</rect>
					<rect id="r2">1338,764,336,156</rect>
					<rect id="r3">2234,530,336,156</rect>
					<rect label="addCharger">508,1024,72,68</rect><rect label="addCharger">2412,1024,72,68</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e1_1"><order>createUnit:we2; r_birth</order></event>
						<event id="e1_1"><condition delay="1"></condition><order>say; startList:s1</order></event>
						<event id="e1_1"><condition>say:listOver; s1</condition></event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order> 
						</event>
						<event id="e2_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order> 
						</event>
						<event id="e2_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order> 
						</event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order> 
						</event>
						<event id="e2_11">
							<condition delay="1">enemyNumber:less_1</condition>
							<order>level; rebirthAllMore</order>
							<order>say; startList:s2</order>
						</event>	
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<!-- 小美死亡 任务失败 -->
						<event id="e_fail">
							<condition delay="0.5">bodyEvent:die; 天鹰小美</condition>
							<order>alert:yes; 小美倒下，任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.3"></condition>
							<order>level; fail</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		
		<gather name="失踪的亚瑟">
			<level name="missingArthur">
				<info enemyLv="90" noMoreB="1" diy="missingArthur"/>
				<sceneLabel>JungleDeep</sceneLabel>
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="鬼目游尸" num="6"/>
						<unit cnName="冥刃游尸" num="9"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="鬼目游尸" num="6"/>
						<unit cnName="冥刃游尸" num="1.5"/>
						<unit cnName="天鹰特种兵" lifeMul="2" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="鬼目游尸" num="4"/>
						<unit cnName="冥刃游尸" num="6"/>
						<unit cnName="天鹰特种兵" num="3"/>
						<unit cnName="天鹰空降兵" lifeMul="2" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="亚瑟" unitType="boss" num="1" dpsMul="1" lifeMul="4" dieGotoState="stru" armsRange="yearChicken,beadCrossbow"/>
					</unitOrder>
				</unitG>
				<rectG>
					<rect id="r_birth">608,788,212,72</rect>
					<rect id="r_over">3628,1216,64,112</rect>
					<rect id="r1">268,84,616,108</rect>
					<rect id="r2">1448,104,724,140</rect>
					<rect id="r3">2784,148,700,168</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">348,516,45,45</rect>
					<rect label="addCharger">3256,540,45,45</rect>
				</rectG>
				<eventG>
					<group>
						<event id="e1_1"><condition delay="2"></condition><order>say; startList:s1</order></event>
						<event id="e2_0"><condition>say:listOver; s1</condition></event><event id="e2_1"><condition delay="1"></condition></event>
						
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order> 
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order> 
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order> 
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r1</order> 
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						
						<event id="e2_11"><condition delay="1">bodyEvent:die; 亚瑟</condition><order>level; rebirthAllMore</order></event>	
						<event id="e2_11"><condition delay="0.1"></condition><order>say; startList:s2</order></event>	
						<event id="e_win">
							<condition delay="0.5">say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		
		<gather name="营救摩卡">
			<level name="rescueMocha">
				<info enemyLv="91" noMoreB="1"/>
				<sceneLabel>BingKuDeep</sceneLabel>
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="摩卡" num="1" lifeMul="0.3" dpsMul="40" dpsSpecielLabel="heroLvDps" aiOrder="followBodyAttack:我" dieGotoState="stru"/><!-- 某个单位的属性 -->
					</unitOrder>
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="童灵尸" num="4"/>
						<unit cnName="卫队尸" num="3"/>
						<unit cnName="关东尸" lifeMul="2" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="童灵尸" num="4"/>
						<unit cnName="卫队尸" num="3"/>
						<unit cnName="关东尸" lifeMul="3" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="童灵尸" num="4"/>
						<unit cnName="卫队尸" num="5"/>
						<unit cnName="关东尸" num="4" lifeMul="2" />
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="女爵尸" unitType="boss" lifeMul="2" dpsMul="2"/><!-- 某个单位的属性 -->
					</unitOrder>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e1_1"><order>createUnit:we2; r_birth</order></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 

						<event id="e2_11">
							<condition delay="1">enemyNumber:less_1</condition>
							<order>level; rebirthAllMore</order>
							<order>say; startList:s2</order>
						</event>	
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<!-- 小美死亡 任务失败 -->
						<event id="e_fail">
							<condition delay="0.5">bodyEvent:die; 摩卡</condition>
							<order>alert:yes; 摩卡倒下，任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.3"></condition>
							<order>level; fail</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		
		<gather name="追击财宝僵尸">
			<level name="killTreasure">
				<info enemyLv="91" noMoreB="1"/>
				<sceneLabel>BuDong</sceneLabel>
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="财宝僵尸" lifeMul="0.5" noSuperB="1" num="1" skillArr="noSkillHurt,onlyWeaponHurt,YouthWolfHeroAngerAdd" aiOrder="patrolRandomNoAttack"/>
					</unitOrder>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1"><condition doNumber="30" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_11">
							<condition delay="1">enemyNumber:less_1</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
		</gather>
		
		<gather name="制毒师的自尊">
			<level name="doctorEsteem">
				<info enemyLv="93" noMoreB="1" />
				<sceneLabel>BaoLun</sceneLabel>
				<unitG>
					<unitOrder id="we2" camp="we">
						<unit cnName="制毒师" num="1" lifeMul="0.05" dpsMul="160" dpsSpecielLabel="heroLvDps" aiOrder="followBodyAttack:我" dieGotoState="stru"/><!-- 某个单位的属性 -->
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="僵尸突击兵" num="3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="屠刀僵尸" num="7"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="僵尸炮兵总管" num="3"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>pro</numberType>
						<unit cnName="僵尸炮兵总管" num="2"/>
					</unitOrder>
					<unitOrder id="enemy5">
						<numberType>pro</numberType>
						<unit cnName="僵尸突击兵" num="2"/>
					</unitOrder>
					<unitOrder id="enemy6">
						<numberType>number</numberType>
						<unit cnName="屠刀僵尸" unitType="boss" lifeMul="2" dpsMul="3" /><!-- 某个单位的属性 -->
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">1429,571,188,62</rect>
					<rect id="r_over">2955,548,57,121</rect>
					<rect id="r_back">-300,548,57,121</rect>
					<rect id="r1">28,64,394,108</rect>
					<rect id="r2">2578,64,394,108</rect>
					<rect id="r3">1230,-40,394,108</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">2161,203,84,84</rect>
					<rect label="addCharger">931,203,84,84</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1"><order>createUnit:we2; r_birth</order></event>
						<event id="e1_1"><condition delay="1"></condition><order>say; startList:s1</order></event>
						<event id="e1_1"><condition>say:listOver; s1</condition></event>
						
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order> 
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order> 
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order> 
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_1"><condition doNumber="5">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r1</order> 
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy5; r3</order>
						</event>
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy6; r1</order> 
							<order>createUnit:enemy6; r2</order>
							<order>createUnit:enemy6; r3</order>
						</event>
						<event id="e2_show">
							<condition>enemyNumber:less_1</condition>
						</event>
						
						<![CDATA[  任务成功  ]]>
						<event id="e_taskWin">
							<condition>killEnemyNum:more; 制毒师:我</condition>
							<order>level; rebirthAllMore</order>
							<order>say; startList:s2</order>
						</event>
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					
					<![CDATA[  任务失败  ]]>
					<group>
						<event id="e2_5">
							<condition>affterDelLevelEvent:e2_show</condition>
						</event>
						<event id="e_task_fail">
							<condition>killEnemyNum:noMore; 制毒师:我</condition>
							<order>level; rebirthAllMore</order>
							<order>say; startList:s3</order>
						</event>
						<event id="e_fail">
							<condition delay="1">say:listOver; s3</condition>
							<order>level; fail</order>
						</event>
					</group>
					
					<![CDATA[  制毒师死亡  ]]>
					<group>
						<event id="e_fail2">
							<condition delay="0.5">bodyEvent:die; 制毒师</condition>
							<order>alert:yes; 制毒师倒下，任务失败！</order>
						</event>
						<event id="e_fail3">
							<condition delay="0.3"></condition>
							<order>level; fail</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		
		<![CDATA[六周年已添加]]>
		<gather name="训练女助手">
			<level name="xinLingTrain">
				<info enemyLv="93" noMoreB="1" />
				<sceneLabel>LuYu</sceneLabel>
				<unitG>
					<unitOrder id="we2" camp="we">
						<unit cnName="心零" num="1" lifeMul="0.2" dpsMul="450" dpsSpecielLabel="heroLvDps" aiOrder="followBodyAttack:我" dieGotoState="stru"/><!-- 某个单位的属性 -->
					</unitOrder>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="6"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="6"/>
						<unit cnName="僵尸突击兵" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="5"/>
						<unit cnName="僵尸突击兵" num="1"/>
						<unit cnName="肥胖僵尸" num="5"/>
						<unit cnName="僵尸突击兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="霸王毒蛛" unitType="boss" lifeMul="1"/>
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">1575,1230,278,101</rect>
					<rect id="r_over">2940,1222,62,125</rect>
					<rect id="r1">1386,422,94,112 </rect>
					<rect id="r2">2391,580,278,104</rect>
					<rect id="r3">20,700,278,104</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">2230,864,84,84</rect>
					<rect label="addCharger">916,842,84,84</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角和心零 -->
						<event id="e1_1"><order>createUnit:we2; r_birth</order></event>
						<event id="e1_1"><condition delay="1"></condition><order>say; startList:s1</order></event>
						<event id="e1_1"><condition>say:listOver; s1</condition></event>
						
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order> 
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order> 
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order> 
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy4; r1</order> 
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<event id="e2_show">
							<condition>enemyNumber:less_1</condition>
						</event>
						
						<!-- 任务成功 -->
						<event id="e_taskWin">
							<condition>killEnemyNum:more; 心零:我</condition>
							<order>level; rebirthAllMore</order>
							<order>say; startList:s2</order>
						</event>
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<!-- 任务失败 -->
					<group>
						<event id="e2_5">
							<condition>affterDelLevelEvent:e2_show</condition>
						</event>
						<event id="e_task_fail">
							<condition>killEnemyNum:noMore; 心零:我</condition>
							<order>level; rebirthAllMore</order>
							<order>say; startList:s3</order>
						</event>
						<event id="e_fail">
							<condition delay="1">say:listOver; s3</condition>
							<order>level; fail</order>
						</event>
					</group>
					
					<!-- 死亡 -->
					<group>
						<event id="e_fail2">
							<condition delay="0.5">bodyEvent:die; 心零</condition>
							<order>alert:yes; 心零倒下，任务失败！</order>
						</event>
						<event id="e_fail3">
							<condition delay="0.3"></condition>
							<order>level; fail</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		
		
		<gather name="回家">
			<level name="getXiaoMei">
				<info noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<sceneLabel>HospitalUnder</sceneLabel>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 人类 -->
					<unitOrder id="we1"  camp="we">
						<unit cnName="天鹰小美"/>
						<unit cnName="小樱" aiOrder="followBodyAttack:我" />
						<unit cnName="亚瑟"/>
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order>
							<order>heroEverParasitic:天鹰小美</order>
							<order>bodyHideInFloorMc:亚瑟; floorMc:Arthur</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; win</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
	</father>		
</data>