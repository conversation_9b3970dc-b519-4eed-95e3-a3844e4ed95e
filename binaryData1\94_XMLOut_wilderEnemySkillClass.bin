<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="enemy" cnName="守望者技能">
		
		<skill index="0" name="守望者-烈焰风暴-清除燃气">
			<name>clearGas_WatchEagle</name>
			<cnName>清除燃气</cnName>
			<noBeClearB>1</noBeClearB><changeHurtB>1</changeHurtB>
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>clearGas_WatchEagle</effectType>
			<valueString>gas_WatchEagle</valueString>
			<!--图像------------------------------------------------------------ -->
			<description></description>
		</skill>
		<skill index="0" name="守望者-冲刺-燃气">
			<name>gas_WatchEagle</name><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cnName>燃气</cnName>
			<noBeClearB>1</noBeClearB>
			
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>wind_WatchEagle</effectType>
			<mul>0.05</mul>
			<value>0.5</value>
			<duration>999999</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="body" con="add">generalEffect/fire</stateEffectImg>
			<description>对击中的目标附加“燃气”效果，使目标移动速度降低50%，每秒减少3%最大生命值，碰撞焦灼烈焰后消失。</description>
		</skill>
		
		
		<skill index="0" name="守望者-冲刺">
			<name>sprint_WatchEagle</name><wantDescripB>1</wantDescripB>
			<cnName>原力俯冲</cnName>
			<showInLifeBarB>1</showInLifeBarB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>20</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>400</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>3.1</duration>
			<!--图像------------------------------------------------------------ -->
			
			<meActionLabel>sprintAttack</meActionLabel>
			<description>隐匿行踪，从敌人头上快速俯冲而下，造成巨大伤害。</description>
		</skill>
		
		<skill index="0" name="守望者-烈焰风暴">
			<name>wind_WatchEagle</name><wantDescripB>1</wantDescripB>
			<cnName>焦灼烈焰</cnName>
			<showInLifeBarB>1</showInLifeBarB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>6</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>500</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<mul>1</mul>
			<value>5</value>
			<duration>2</duration>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>windAttack</meActionLabel>
			<description>释放不会消失的火焰旋风，对碰触到的敌人造成爆炸伤害，扣除目标20%的生命值，火焰最多存在10个。</description>
		</skill>
		
		
	 </father>
	 
	 <father name="enemy" cnName="异角龙">
		<skill cnName="巨石崩塌"><!-- dps -->
			<name>Triceratops_stone</name>
			<cnName>巨石崩塌</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cd>12</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>500</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_Triceratops</effectType>
			<mul>0.5</mul>
			<duration>7</duration>
			<!-- 子弹所需 -->
			<obj>"name":"Triceratops_stone"</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/fireHit2" noShowB="1"></meEffectImg>
			<meActionLabel>stoneAttack</meActionLabel>
			<description>首领用尾巴猛烈拍打地面，从空中间歇性地持续掉落3束巨石，时续[duration]秒。</description>
		</skill>
		<skill cnName="硬甲"><!-- dps -->
			<name>Triceratops_hard</name>
			<cnName>硬甲</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cd>50</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>500</conditionRange>
			<target>me</target>
			<delay>0.3</delay>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>15</duration>
			<passiveSkillArr>underHit_Paralysis_link</passiveSkillArr>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg bodyColor="0xFFCC00" bodyColorAlpha="0.5"></stateEffectImg>
			<stateEffectImg2 partType="body" con="add">generalEffect/crazy</stateEffectImg2>
			<meActionLabel>magicAttack</meActionLabel>
			<description>每隔[cd]秒释放一层护甲，效果期间受到伤害，使攻击者减少60%移动速度，持续4秒。</description>
		</skill>
		<skill cnName="蛋护"><!-- dps -->
			<name>Triceratops_egg</name>
			<cnName>蛋护</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>30</cd>
			<delay>0.68</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<obj>"cnName":"异龙蛋","num":1,"mulByFatherB":1,"lifeMul":0.02,"cx":-155,"cy":-23,"maxNum":1,"lifeBarB":1,"skillArr":["invincible_eeg"]</obj>
			<duration>30</duration>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>eggAttack</meActionLabel>
			<description>从嘴里吐出一枚异龙蛋，此时首领无敌，直到蛋被击碎。</description>
		</skill>
		<skill cnName="海市蜃楼"><!-- dps -->
			<name>Triceratops_oasis</name>
			<cnName>海市蜃楼</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>15</cd>
			<summonedUnitsB>1</summonedUnitsB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>Triceratops_oasis</effectType>
			<obj>"cnName":"绿洲","num":3,"lifeMul":1000,"maxNum":100,"noUnderHurtB":1,"noAiFindB":1,"noUnderHitB":1,"skillArr":["State_SpellImmunity","DesertOasis_thirst"]</obj>
			<duration>15</duration>
			<!--图像------------------------------------------------------------ -->
			<description>。</description>
		</skill>
		<skill cnName="饥荒"><!-- dps -->
			<name>Triceratops_deserted</name>
			<cnName>饥荒</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<noBeClearB>1</noBeClearB>
			<noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<otherConditionArr>noState</otherConditionArr>
			<conditionString>DesertOasis_thirst</conditionString>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>hurtMul</effectType>
			<mul>0.1</mul>
			<duration>0.3</duration>
			<range>99999</range>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">generalEffect/headSmoke</stateEffectImg>
			<stateEffectImg2 partType="mouth" con="filter" raNum="30">bulletHitEffect/smoke_small</stateEffectImg2>
			<description>降低所有敌人[1-mul]的攻击力，对绿洲周围的敌人无效。</description>
		</skill>
		<skill cnName="远古基因"><!-- dps -->
			<name>noDegradation</name>
			<cnName>远古基因</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>underHurtMulMax</effectType>
			<value>1</value>
			<duration>9999999</duration>
			<!--图像------------------------------------------------------------ -->
			<description>敌人无法通过任何技能来降低首领的防御。</description>
		</skill>
		<![CDATA[绿洲]]>
		<skill cnName="解渴"><!-- dps -->
			<name>DesertOasis_thirst</name>
			<cnName>解渴</cnName>
			<noBeClearB>1</noBeClearB>
			<noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<intervalT>0.1</intervalT>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>3</duration>
			<range>150</range>
			<!--图像------------------------------------------------------------ -->
			<description>。</description>
		</skill>
	</father>
	
	 <father name="enemy" cnName="异祖龙">
		<skill cnName="巨焰围剿"><!-- dps -->
			<name>FlyDragon_fireSurround</name>
			<cnName>巨焰围剿</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_fireSurround</effectType>
			<duration>9999999</duration>
			<mul>0.3</mul>
			<!-- 子弹所需 -->
			<obj>"name":"FlyDragon_fireSurround","nameArr":["FlyDragon_fireSurroundSmoke"]</obj>
			<!--图像------------------------------------------------------------ -->
			<description>首领引来飞龙群，在直线路径上持续喷射火柱，对击中的敌人每秒造成[mul]最大生命值的伤害。</description>
		</skill>
		<skill cnName="月石"><!-- dps -->
			<name>FlyDragonBall</name><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cnName>月石</cnName>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB><noCopyB>1</noCopyB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>5</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>FlyDragonBall</effectType>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg>FlyDragon/drop</stateEffectImg>
			<description>收集地图上遗落的6个月石后，首领将会完全石化，并且矫捷姿态消失。</description>
		</skill>
		<skill cnName="石化"><!-- dps -->
			<name>FlyDragon_petrifaction</name><overlyingB>1</overlyingB>
			<cnName>石化</cnName>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<noSkillDodgeB>1</noSkillDodgeB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>3</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>FlyDragon_petrifaction</effectType>
			<value>5</value>
			<duration>9999999</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg bodyColor="0x58564B"></stateEffectImg>
			<description>。</description>
		</skill>
		<skill index="0" cnName="矫捷姿态">
			<name>FlyDragon_dodgePro</name><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cnName>矫捷姿态</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>dodgePro</effectType>
			<value>0.9</value>
			<duration>999999</duration>
			<description>闪避率增加90%，石化后闪避状态消失。</description>
		</skill>
		<skill index="0" cnName="召唤蝙蝠"><!-- 限制 -->
			<name>FlyDragon_summoned</name>
			<cnName>召唤蝙蝠</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0.1</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<!-- 子弹所需 -->
			<obj>"cnName":"吸血蝙蝠","num":10,"lifeMul":0.05,"dpsMul":0.05,"lifeTime":10,"mulByFatherB":1,"maxNum":10,"skillArr":[]</obj>
			<!--图像------------------------------------------------------------ -->
			<description>蝙蝠。</description>
		</skill>
		<skill index="0" cnName="龙甲"><!-- 生存-被动 -->
			<name>FlyDragon_likeMissle</name><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cnName>龙甲</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurt</effectType>
			<mul>0.05</mul>
			<!-- 修改伤害所需 -->
			<obj>"type":"followBullet"</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			<description>受到跟踪型子弹的伤害减少[1-mul]。</description>
		</skill>
		<skill cnName="近战伤害减免"><!-- dps -->
			<name>fightReduct</name>
			<cnName>近战防御</cnName>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>fightDedut</effectType>
			<mul>0.90</mul>
			<duration>9999999</duration>
			<!--图像------------------------------------------------------------ -->
			<description>受到近战伤害减少90%。</description>
		</skill>
		<skill cnName="近战伤害减免"><!-- dps -->
			<name>fightReduct7</name>
			<cnName>近战防御</cnName>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>fightDedut</effectType>
			<mul>0.75</mul>
			<duration>9999999</duration>
			<!--图像------------------------------------------------------------ -->
			<description>受到近战伤害减少[mul]。</description>
		</skill>
		<skill index="0" cnName="石化眩晕"><!-- 限制 -->
			<name>FlyDragon_hammer</name>
			<cnName>石化眩晕</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>dizziness</effectType>
			<duration>2</duration>
			<range>100</range>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
			<description>击中目标有[effectProArr.0]的几率使目标陷入眩晕状态，持续[duration]秒，对机械体无效。</description>
		</skill>
	</father>	 
	<father name="enemy" cnName="异齿虎">
		<skill cnName="量子光束"><!-- dps -->
			<name>SaberTiger_laser</name>
			<cnName>量子光束</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<cd>5</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>500</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_SaberTiger_laser</effectType>
			<duration>1.5</duration>
			<mul>1</mul>
			<!-- 子弹所需 -->
			<obj>"name":"SaberTiger_laser"</obj>
			<!--图像------------------------------------------------------------ -->
			<pointEffectImg con="add">skillEffect/aperture</pointEffectImg>
			<meActionLabel>laserAttack</meActionLabel>
			<description>猛击地面，在敌人脚下引爆量子光束，对目标造成最大生命值20%的伤害。</description>
		</skill>
		<skill cnName="末日轰炸"><!-- dps -->
			<name>SaberTiger_missile</name>
			<cnName>末日轰炸</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<cd>6</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>700</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>no</effectType>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>missileAttack</meActionLabel>
			<description>释放3个巡航导弹，轰炸前方区域内的所有敌人，每颗导弹造成目标最大生命值20%的伤害</description>
		</skill>
		<skill cnName="召唤生化罐"><!-- dps -->
			<name>SaberTiger_ChemicalTank</name>
			<cnName>召唤生化罐</cnName>
			<ignoreSilenceB>1</ignoreSilenceB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<firstCd>9999999</firstCd>
			<cd>9999999</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<obj>"cnName":"生化罐","num":1,"mulByFatherB":1,"lifeMul":0.18,"maxNum":1,"position":"randomMap","lifeBarB":1</obj>
			<!--图像------------------------------------------------------------ -->
			<description>。</description>
		</skill>
		<skill index="0" cnName="超导电幕"><!-- 生存-被动 -->
			<name>SaberTiger_shield</name>
			<cnName>超导电幕</cnName>
			<wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>SaberTiger_shield</effectType>
			<linkArr>SaberTiger_shield_first</linkArr>
			<duration>9999999</duration>
			<!--图像------------------------------------------------------------ -->
			<pointEffectImg con="add" noShowB="1">SaberTiger/angerEffect</pointEffectImg>
			<stateEffectImg con="add" noShowB="1">SaberTiger/shield1</stateEffectImg>
			<stateEffectImg2 con="add" noShowB="1">SaberTiger/shield2</stateEffectImg2>
			<description>通电的超导体产生巨大的磁力场，阻挡一切远程攻击。在机械重组之后，超导电幕消失。</description>
		</skill>
		<skill index="0" cnName="机械重组"><!-- 生存 -->
			<name>SaberTiger_rebirth</name>
			<cnName>机械重组</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>allDie</condition>
			<otherConditionArr>rebirthNumLess</otherConditionArr>
			<conditionRange>1</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>SaberTiger_rebirth</effectType>
			<mul>1</mul><!-- 重生后生命值 -->
			<!--图像------------------------------------------------------------ -->
			<addSkillEffectImg partType="mouth" con="add">skillEffect/rune_green_r</addSkillEffectImg>
			<meEffectImg soundUrl="sound/groupLight_hero" con="add">skillEffect/groupLight_hero</meEffectImg>
			<description>激活体内的记忆零件，使机体重组复生。</description>
		</skill>
		<skill index="0" cnName="超导电幕-第一阶段"><!-- 生存-被动 -->
			<name>SaberTiger_shield_first</name>
			<cnName>超导电幕-第一阶段</cnName><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
			<conditionType>passive</conditionType>
			<target>me</target>
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>9999999</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="body" con="add">SaberTiger/shield1</stateEffectImg>
		</skill>
		<skill index="0" cnName="超导电幕-第二阶段"><!-- 生存-被动 -->
			<name>SaberTiger_shield_second</name>
			<cnName>超导电幕-第二阶段</cnName><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
			<conditionType>passive</conditionType>
			<target>me</target>
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>9999999</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="body" con="add">SaberTiger/shield2</stateEffectImg>
		</skill>
		
		<skill index="0" cnName="超导电幕-怒气"><!-- 生存-被动 -->
			<name>SaberTiger_shield_anger</name>
			<cnName>超导电幕-怒气</cnName><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>angerAddMul</effectType>
			<mul>20</mul>
			<duration>1</duration>
			<range>99999</range>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">SaberTiger/angerEffect</stateEffectImg>
		</skill>
		<skill index="0" cnName="超导电幕-防御"><!-- 生存-被动 -->
			<name>SaberTiger_shield_defence</name>
			<cnName>超导电幕-防御</cnName><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurt_SaberTiger_shield</effectType>
			<mul>0</mul>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			<description>只接受副手和异角龙的攻击伤害。</description>
		</skill>
		
		<skill index="0" cnName="异角龙百分比伤害"><!-- 生存-被动 -->
			<name>TriceratopsHurt</name>
			<cnName>异角龙百分比伤害</cnName><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurt_TriceratopsHurt</effectType>
			<mul>0.01</mul>
			<description>异角龙百分比伤害。</description>
		</skill>
		<skill cnName="召唤异角龙"><!-- dps -->
			<name>ChemicalTank_Triceratops</name>
			<cnName>召唤异角龙</cnName>
			<ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>die</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<obj>"cnName":"异角龙","num":1,"lifeMul":1000,"maxNum":1,"lifeBarB":1,"camp":"enemy","noUnderHurtB":1,"skillArr":["TriceratopsHurt"]</obj>
			<!--图像------------------------------------------------------------ -->
			<description>。</description>
		</skill>
	</father>	 	
	<father name="enemy" cnName="异猛象">
		<skill name="死后清除所有召唤单位">
			<name>Mammoth_die</name>
			<cnName>死后清除所有召唤单位</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>passive</conditionType><condition>allDie</condition>
			<target>me</target>
			<addType>instant</addType><effectType>clearSummoned</effectType>
		</skill>
		<skill cnName="电磁风暴"><!-- dps -->
			<name>Mammoth_electricity</name>
			<cnName>电磁风暴</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<cd>15</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.7</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet_Mammoth_electricity</effectType>
			<!-- 子弹所需 -->
			<obj>"name":"Mammoth_electricity"</obj>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>shakeAttack</meActionLabel>
			<description>释放静电洪流，对大范围区域内的敌人造成伤害。</description>
		</skill>
		<skill cnName="洲际导弹"><!-- dps -->
			<name>Mammoth_missile</name>
			<cnName>洲际导弹</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<cd>6</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>700</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>no</effectType>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>missileAttack</meActionLabel>
			<description>发射全球精确制导的洲际导弹，轰炸一定范围内的敌人（中途可被破坏）。</description>
		</skill>
		<skill name="洲际导弹-自爆">
			<name>Mammoth_missileChip</name>
			<cnName>洲际导弹-自爆</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>die</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<!-- 子弹所需 -->
			<obj>"name":"Mammoth_missileChip","site":"me"</obj>
			<stateEffectImg con="add">Mammoth/blueElec</stateEffectImg>
			<stateEffectImg2 con="add">Mammoth/redElec</stateEffectImg2>
		</skill>
		
		<skill cnName="机械核心"><!-- 生存-被动 -->
			<name>Mammoth_core</name>
			<cnName>机械核心</cnName>
			<wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>Mammoth_core</effectType>
			<value>500</value>
			<duration>9999999</duration>
			<!--图像------------------------------------------------------------ -->
			<description>能量过载，导致腹部弱点暴露，消失后产生爆炸伤害。</description>
		</skill>
		<skill name="钛钢甲">
			<name>Mammoth_hurt</name><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cnName>钛钢甲</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType><noCopyB>1</noCopyB>
			<condition>underHit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>Mammoth_hurt</effectType>
			<description>全身被厚厚的钛钢包裹，一般子弹难以穿透。</description>
		</skill>
		
		<![CDATA[//机械核心所需]]>
		<skill cnName="召唤机械核心"><!-- dps -->
			<name>Mammoth_coreShow</name>
			<cnName>召唤机械核心</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>no</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<obj>"cnName":"空单位","num":1,"lifeMul":99999999,"maxNum":1,"skillArr":["fightReduct","State_SpellImmunity","Mammoth_core_die","Mammoth_core_attached","Mammoth_core_hurt"]</obj>
			<duration>10</duration>
			<!--图像------------------------------------------------------------ -->
			<description>。</description>
		</skill>
		<skill name="机械核心-死亡自爆">
			<name>Mammoth_core_die</name>
			<cnName>机械核心-死亡</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>passive</conditionType><condition>allDie</condition>
			<target>me</target>
			<addType>instant</addType><effectType>bullet_Mammoth_core_die</effectType>
			<obj>"name":"Mammoth_core_die","site":"me","launcherB":true</obj>
		</skill>
		<skill name="机械核心-附着">
			<name>Mammoth_core_attached</name>
			<cnName>机械核心-附着</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>passive</conditionType><condition>add</condition>
			<target>me</target>
			<addType>state</addType><duration>999999</duration>
			<effectType>Mammoth_core_attached</effectType>
		</skill>
		<skill name="机械核心-蓝闪电">
			<name>Mammoth_core_blueElec</name>
			<cnName>机械核心-蓝闪电</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>passive</conditionType><condition>no</condition>
			<target>me</target>
			<addType>state</addType><duration>999999</duration>
			<effectType>no</effectType>
			<stateEffectImg con="add">Mammoth/blueElec</stateEffectImg>
		</skill>
		<skill name="机械核心-红闪电">
			<name>Mammoth_core_redElec</name>
			<cnName>机械核心-红闪电</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>passive</conditionType><condition>no</condition>
			<target>me</target>
			<addType>state</addType><duration>999999</duration>
			<effectType>no</effectType>
			<stateEffectImg con="add">Mammoth/redElec</stateEffectImg>
		</skill>
		<skill name="机械核心-受伤">
			<name>Mammoth_core_hurt</name>
			<cnName>机械核心-受伤</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHurt</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>Mammoth_core_hurt</effectType>
		</skill>
		
		
		<skill cnName="狼狈为奸"><!-- dps -->
			<name>Mammoth_wolf</name>
			<cnName>狼狈为奸</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cd>0</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType><noCopyB>1</noCopyB>
			<condition>no</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<obj>"cnName":"嗜血尸狼","num":1,"lifeMul":0.012,"dpsMul":0.025,"maxNum":35,"position":"randomTopMap","noAttackArr":["shootAttack"],"skillArr":["State_AddMove","State_SpellImmunity","Mammoth_wolf_die"]</obj>
			<duration>1000</duration>
			<!--图像-------------------------------------------------------------->
			<description>集中全身能量，咆哮引来狼群。</description>
		</skill>
		
		<skill name="尸狼-死亡">
			<name>Mammoth_wolf_die</name>
			<cnName>尸狼-死亡</cnName>
			<conditionType>passive</conditionType><condition>allDie</condition>
			<target>me</target>
			<addType>instant</addType><effectType>Mammoth_wolf_die</effectType>
		</skill>
	</father>	
	<father name="enemy" cnName="异龙蛋">
		<skill cnName="异龙蛋-无敌光环"><!-- dps -->
			<name>invincible_eeg</name>
			<cnName>无敌光环</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target>meSummonedFather</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>invincibleAndThrough</effectType>
			<duration>0.1</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="body" con="add">Triceratops/energyShield</stateEffectImg>
			<description>对召唤者施加无敌光环</description>
		</skill>
		<skill index="0" cnName="无敌异龙蛋-磁力场"><!-- 生存-主动 -->
			<name>magneticField_egg</name>
			<cnName>磁力场</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<intervalT>1</intervalT>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>magneticB</effectType>
			<value>1</value>
			<mul>0.2</mul>
			<duration>1</duration>
			<range>200</range>
			<!--技能链接或者需要添加的被动技能------------------------------------------------------------ --> 
			<passiveSkillArr>magneticField_enemy_link</passiveSkillArr>
			<!--图像------------------------------------------------------------ --> 
			<description>每过[intervalT]秒向外释放磁力场，使700码的范围内的所有敌方子弹偏离轨道，持续[duration]秒。</description>
		</skill>
		<skill index="0" cnName="无敌异龙蛋-墙"><!-- 生存-主动 -->
			<name>block_egg</name>
			<cnName>墙</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<intervalT>0.1</intervalT>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>block</effectType>
			<duration>0.2</duration>
			<range>70</range>
			<!--图像------------------------------------------------------------ --> 
			<description>每过[intervalT]秒向外释放磁力场，使700码的范围内的所有敌方子弹偏离轨道，持续[duration]秒。</description>
		</skill>
		<skill index="0" name="trueshot_eeg" cnName="无敌异龙蛋-强击光环"><!-- dps -->
			<name>trueshot_eeg</name>
			<cnName>强击光环</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target>me,range,we</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>hurtMul</effectType>
			<mul>1.10</mul>
			<duration>0.1</duration>
			<range>300</range>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="2hand" con="add" raNum="30" followPartRaB="1">skillEffect/trueshot_enemy</stateEffectImg>
			<description>周围400码以内的我方单位被动提升[mul-1]的攻击力。</description>
		</skill>
		<skill index="8" name="被击中麻痹">
			<name>underHit_Paralysis_link</name>
			<cnName>被击中麻痹</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<mul>0.4</mul>
			<duration>4</duration>
			<!--图像------------------------------------------------------------ --> 
			<targetEffectImg soundUrl="sound/paralysis_enemy_hit"></targetEffectImg>
			<stateEffectImg con="add" >skillEffect/paralysis_enemy</stateEffectImg>
			<description>。</description>
		</skill>
	</father>	 
	
	<father name="enemy" cnName="虚空客">
		<skill cnName="星际尘埃"><!-- dps -->
			<name>VanityKer_comet</name>
			<cnName>星际尘埃</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_VanityKer_comet</effectType>
			<duration>9999999</duration>
			<mul>0.3</mul>
			<!-- 子弹所需 -->
			<obj>"name":"VanityKer_comet"</obj>
			<!--图像------------------------------------------------------------ -->
			<description>从天空掉落星际尘埃，玩家拾取后可以暂时消除解析射线的影响。</description>
		</skill>
		
		<skill cnName="星辰buff"><!-- dps -->
			<name>VanityKer_cometBuff</name>
			<cnName>星辰buff</cnName>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>VanityKer_cometBuff</effectType>
			<duration>5</duration>
			<!--图像------------------------------------------------------------ -->
			<description>玩家吃到后，获得星辰buff，星辰buff存在时，解析射线buff消失且不出现。</description>
		</skill>
		<skill cnName="解析射线"><!-- dps -->
			<name>VanityKer_rayBuff</name>
			<cnName>解析射线</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<condition>interval</condition>
			<otherConditionArr>noState</otherConditionArr>
			<conditionString>VanityKer_cometBuff</conditionString>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>VanityKer_rayBuff</effectType>
			<duration>1</duration>
			<mul>0.4</mul>
			<value>1.3</value>
			<range>9999999</range>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="body" con="add">VanityKer/rayBuff</stateEffectImg>
			<description>用射线锁定单位，被锁定的单位降低一定攻击并大幅提升移动速度。</description>
		</skill>
		
		
		<skill cnName="虚幻镜像"><!-- dps -->
			<name>VanityKer_dreamland</name>
			<cnName>虚幻镜像</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>10</cd>
			<delay>0.33</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<obj>"name":"VanityKer_dreamland","flipB":true,"site":"VanityKer_cometBuff"</obj>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>lightAttack</meActionLabel>
			<description>发射镜像激光，命中带有解析射线的单位时，随机召唤1只仆从，仆从殒命后恢复首领一定生命值。</description>
		</skill>
		<skill index="0" cnName="召唤随从"><!-- 限制 -->
			<name>VanityKer_dreamlandUnit</name>
			<cnName>召唤随从</cnName>
			<delay>0.5</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<summonedUnitsB>1</summonedUnitsB>
			<effectType>VanityKer_dreamlandUnit</effectType>
			<!-- 子弹所需 -->
			<obj>"cnName":"无头自爆僵尸","randomCnArr":["吸血蝙蝠","毒蛛"],"num":1,"lifeMul":0.03,"dpsMul":0.1,"lifeTime":20,"mulByFatherB":1,"maxNum":10,"skillArr":["VanityKer_feeding","State_SpellImmunity"],"position":"hurtPoint"</obj>
			<duration>30</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/cloned_enemy"></meEffectImg>
			<description>蝙蝠。</description>
		</skill>
		
		<skill index="0" cnName="反哺"><!-- 限制 -->
			<name>VanityKer_feeding</name>
			<cnName>反哺</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>allDie</condition>
			<target targetMustLiveB="1">meSummonedFather</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>life</effectType>
			<extraValueType>targetMaxLife</extraValueType>
			<value>0.02</value>
			<mul>0</mul>
			<!--图像------------------------------------------------------------ -->
			<targetEffectImg con="add" soundUrl="sound/groupLight_hero" soundVolume="0.3">skillEffect/groupLight_hero</targetEffectImg>
			<description>单位殒命后，回复召唤主一定的生命值，回复量为单位的最大生命值。</description>
		</skill>
		
		<skill index="0" cnName="召唤反物质"><!-- 限制 -->
			<name>VanityKer_antimatter</name>
			<cnName>反物质</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cd>20</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<!-- 子弹所需 -->
			<obj>"cnName":"反物质","num":1,"lifeMul":0.03,"dpsMul":0.1,"lifeTime":60,"mulByFatherB":1,"maxNum":10,"skillArr":["Antimatter_hammer","State_SpellImmunity"],"position":"randomTopMap"</obj>
			<duration>30</duration>
			<!--图像------------------------------------------------------------ -->
			<description>随机召唤不稳定的暗物质在地图上弹跳，对碰触的单位造成0.5秒昏迷。</description>
		</skill>
		<skill cnName="击中眩晕"><!-- 限制 -->
			<name>Antimatter_hammer</name>
			<cnName>击中眩晕</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<conditionString>Antimatter_hammer</conditionString>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>Antimatter_hammer</effectType>
			<mul>0.5</mul>
			<duration>0.5</duration>
			<!--图像------------------------------------------------------------ -->
			<addSkillEffectImg con="filter">Antimatter/effect</addSkillEffectImg>
			<stateEffectImg partType="mouth" con="add" soundUrl="sound/vehicle_hit1">skillEffect/dizziness</stateEffectImg>
			<description>击中使目标陷入眩晕状态，持续[duration]秒。</description>
		</skill>
		
		<skill cnName="击中死亡"><!-- 限制 -->
			<name>Antimatter_die</name>
			<cnName>击中死亡</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>toDie</effectType>
			
		</skill>
		
	</father>	 
	<father name="enemy" cnName="年兽">
		<skill index="0" cnName="恶魔火"><!-- 限制 -->
			<name>Nian_change</name>
			<cnName>恶魔火</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cd>30</cd>
			<delay>0.3</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>Nian_change</effectType>
			<passiveSkillArr>Nian_change_link</passiveSkillArr>
			<meActionLabel>changeAttack</meActionLabel>
			<duration>10</duration>
			<stateEffectImg partType="2eye,2hand,2foot,arm_right_0,arm_right_1,arm_left_0,arm_left_1,leg_right_0,leg_right_1,leg_left_0,leg_left_1" con="filter">NianMonster/sparkBullet</stateEffectImg>
			<description>年兽朝天怒吼，全身燃起恶魔火，不断燃烧周围的敌人。</description>
		</skill>
		<skill index="0" cnName="自燃-链接"><!-- dps -->
			<name>Nian_change_link</name>
			<cnName>自燃-链接</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target>me,random,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>normal_hurt</effectType>
			<extraValueType>nowArmsDpsOrNormal</extraValueType>
			<mul>0.1</mul>
			<duration>0.1</duration>
			<range>150</range>
			<!--图像------------------------------------------------------------ -->
			<targetEffectImg con="add" randomRange="40">bulletHitEffect/spark_motion2</targetEffectImg>
			<description>单位全身燃起熊熊烈火，对附近[range]范围内的敌人造成伤害，伤害值为当前武器战斗力的[mul]。</description>
		</skill>
		<skill index="0" cnName="驾驭星火"><!-- 限制 -->
			<name>Nian_spark</name>
			<cnName>驾驭星火</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cd>10</cd>
			<delay>0.76</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>500</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet_Nian_spark</effectType>
			<obj>"name":"Nian_spark"</obj>
			<meActionLabel>sparkAttack</meActionLabel>
			<description>年兽利用恶火之力砸向地面，驾驭溅起的火星对敌人发起攻击。</description>
		</skill>
		<skill cnName="击中麻痹"><!-- 限制 -->
			<name>Nian_dartsParalysis</name>
			<cnName>击中麻痹</cnName>
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<mul>0</mul>
			<duration>0.5</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg con="add" soundUrl="sound/paralysis_enemy_hit">skillEffect/paralysis_enemy</stateEffectImg>
		</skill>
		
	</father>	 
	
	<father name="enemy" cnName="虚炎狼">
		<skill cnName="墟洞岩火"><!-- 限制 -->
			<name>FireWolf_rockFire</name>
			<cnName>墟洞岩火</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cd>1</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>noState</otherConditionArr>
			<conditionString>FireWolf_noRockFire,elementsYellow,elementsPurple</conditionString>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_FireWolf_rockFire</effectType>
			<meActionLabel>rockFireAttack</meActionLabel>
			<range>999999</range>
			<duration>1.5</duration>
			<obj>"name":"FireWolf_rockFire","nameArr":["FireWolf_rockFireLink"]</obj>
			<description>首领持续嘶吼，巨大的嘶吼声震裂墟洞，并不断从地下喷涌出岩火。</description>
		</skill>
		<skill cnName="无名火"><!-- 限制 -->
			<name>FireWolf_noFire</name>
			<cnName>无名火</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cd>5</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_FireWolf_noFire</effectType>
			<duration>1</duration>
			<range>999999</range>
			<obj>"name":"FireWolf_noName"</obj>
			<pointEffectImg con="add">FireWolf/fireShow</pointEffectImg>
			<description>每过5秒出现一团无名火对玩家造成伤害。</description>
		</skill>
		<skill cnName="稀有元素"><!-- dps -->
			<name>FireWolf_elements</name><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cnName>稀有元素</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>5</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType><noCopyB>1</noCopyB>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet_FireWolf_elements</effectType>
			<obj>"name":"elementsYellow","nameArr":["elementsGreen","elementsRed","elementsBlue","elementsPurple"]</obj>
			<description>拾取散落的稀有元素，能立刻打断首领的嘶吼，但也有一定概率对自身造成负面效果。</description>
		</skill>
		
		<skill name="孪生之力">
			<name>FireWolf_cloned</name>
			<cnName>孪生之力</cnName><noCopyB>1</noCopyB>
			<cd>1</cd>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>noState</otherConditionArr>
			<conditionString>FireWolf_noRockFire,elementsYellow,elementsPurple</conditionString>
			<target>me</target>
			<!--效果------------------------------------------------------------ --> 
			<addType>instant</addType>
			<effectType>FireWolf_cloned</effectType>
			<duration>30</duration>
			<obj>"cnName":"虚炎狼","num":1,"lifeMul":0.1,"dpsMul":1,"lifeTime":9999999,"mulByFatherB":1,"maxNum":1,"skillArr":["fightReduct","FireWolf_noRockFire","State_SpellImmunity"]</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/cloned_enemy" con="add">skillEffect/hiding_hero</meEffectImg>
			<description>首领嘶吼期间，分裂出孪生体协助攻击，消灭孪生体能打断首领嘶吼。</description>
		</skill>
		<skill cnName="无法释放墟洞岩火buff"><!-- dps -->
			<name>FireWolf_noRockFire</name>
			<cnName>无法释放墟洞岩火buff</cnName><showInLifeBarB>1</showInLifeBarB>
			<noBeClearB>1</noBeClearB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>allDie</condition>
			<target>meSummonedFather</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>dizziness</effectType>
			<duration>5</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
		</skill>
		
		<![CDATA[元素伤害]]>
		<skill cnName="打断墟洞岩火"><!-- dps -->
			<name>elementsYellow</name>
			<cnName>打断墟洞岩火</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>elementsYellow</effectType>
			<duration>5</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
		</skill>
		<skill cnName="对玩家造成伤害"><!-- dps -->
			<name>elementsRed</name>
			<cnName>对玩家造成伤害</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>normal_hurt</effectType>
			<extraValueType>targetMaxLife</extraValueType>
			<mul>0.2</mul>
		</skill>
		<skill cnName="对分身造成伤害"><!-- dps -->
			<name>elementsGreen</name>
			<cnName>对分身造成伤害</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>noTrueBody</otherConditionArr>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>normal_hurt</effectType>
			<extraValueType>targetMaxLife</extraValueType>
			<mul>0.1</mul>
		</skill>
		
		<skill cnName="使自身恐惧"><!-- 生存-主动 -->
			<name>elementsBlue</name>
			<cnName>使自身恐惧</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>screaming_hero</effectType>
			<duration>5</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="mouth" con="filter">skillEffect/screaming_hero_target</stateEffectImg>
		</skill>
		<skill cnName="使首领恐惧"><!-- 生存-主动 -->
			<name>elementsPurple</name>
			<cnName>使首领恐惧</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>screaming_hero</effectType>
			<duration>10</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="mouth" con="filter">skillEffect/screaming_hero_target</stateEffectImg>
		</skill>
	</father>	 
		
	
	<father name="enemy" cnName="虚洪螈">
		<skill index="0" name="水卷风">
			<name>Salamander_wather</name>
			<cnName>水卷风</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>6</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>300</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>1.1</duration>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>windAttack</meActionLabel>
		</skill>
		
		<skill index="0" name="水汽溅腾">
			<name>Salamander_wather</name><wantDescripB>1</wantDescripB>
			<cnName>水汽溅腾</cnName>
			<showInLifeBarB>1</showInLifeBarB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>5</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerMore</otherConditionArr>
			<conditionRange>0</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>0.6</duration>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>waterAttack</meActionLabel>
			<description>压缩全身气流，向目标喷射污水柱，并造成巨大伤害。</description>
		</skill>
		<skill index="0" name="地精">
			<name>Salamander_burrow</name><wantDescripB>1</wantDescripB>
			<cnName>地精</cnName>
			<showInLifeBarB>1</showInLifeBarB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>5</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.5</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>no</effectType>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>inAttack</meActionLabel>
			<description>当生命低于50%时，发动高频率的钻地技能，碰撞气泡后会停止行动。</description>
		</skill>
		<skill index="0" cnName="融弹回血"><!-- 生存-主动 -->
			<name>Salamander_back</name>
			<cnName>融弹回血</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underRos</condition>
			<otherConditionArr>noState</otherConditionArr>
			<conditionString>Salamander_back</conditionString>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>stateAndInstant</addType>
			<effectType>Salamander_back</effectType>
			<value>0.5</value>
			<duration>999999</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="2hand,2foot,arm_right_0,arm_right_1,arm_left_0,arm_left_1,leg_right_0,leg_right_1,leg_left_0,leg_left_1" con="add">Salamander/bubbling</stateEffectImg>
			<description>当减少一定生命后，首领会溶解射进身体的子弹，并回复一定生命值。</description>
		</skill>
		<skill index="0" cnName="反转气泡"><!-- 限制 -->
			<name>Salamander_bubbles</name><noCopyB>1</noCopyB>
			<cnName>反转气泡</cnName><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cd>2</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>Salamander_bubbles</effectType>
			<summonedUnitsB>1</summonedUnitsB>
			<!-- 子弹所需 -->
			<obj>"cnName":"气泡","num":1,"lifeMul":0.15,"dpsMul":0,"lifeTime":20,"mulByFatherB":1,"maxNum":10,"skillArr":["BubblesJump","bubblesDie","bubblesGotoCamp","Bubbles_blue","Bubbles_green","State_SpellImmunity","bubbles_hammer"],"position":"randomMap"</obj>
			<duration>20</duration>
			<!--图像------------------------------------------------------------ -->
			<description>每隔一段时间，随机出现气泡，可以抵挡首领的某些攻击，同时可以削弱首领的能力。</description>
		</skill>
	</father>
	
	<father name="enemy" cnName="气泡">
		<skill index="0" name="hammer_enemy" cnName="碰到气泡眩晕"><!-- 限制 -->
			<name>bubbles_hammer</name>
			<cnName>眩晕</cnName>
			<ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>active</conditionType>
			<condition>no</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>dizziness</effectType>
			<duration>3</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
		</skill>
		<skill index="0" cnName="转变颜色"><!-- 生存-主动 -->
			<name>bubblesGotoCamp</name>
			<cnName>转变颜色</cnName>
			<noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>no</condition>
			<doCondition></doCondition>
			<target bodyName="Bubbles" targetMustLiveB="1">me,range,all</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bubblesGotoCamp</effectType>
			<duration>0.4</duration>
			<range>999999</range>
			<!--图像------------------------------------------------------------ --> 
			<targetEffectImg partType="body" soundUrl="specialGun/christmasGunShoot">Bubbles/change</targetEffectImg>
		</skill>
		
		<skill index="0" cnName="死亡"><!-- 生存-主动 -->
			<name>bubblesDie</name>
			<cnName>死亡</cnName>
			<noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>allDie</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bubblesDie</effectType>
		</skill>
		<skill index="0" cnName="蓝"><!-- 生存-主动 -->
			<name>Bubbles_blue</name>
			<cnName>蓝</cnName>
			<noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>no</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>999999</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="body" con="add">Bubbles/blue</stateEffectImg>
		</skill>
		<skill index="0" cnName="绿"><!-- 生存-主动 -->
			<name>Bubbles_green</name>
			<cnName>绿</cnName>
			<noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>no</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>999999</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="body" con="add">Bubbles/green</stateEffectImg>
		</skill>
		<skill index="0" cnName="弹跳"><!-- 生存-主动 -->
			<name>BubblesJump</name>
			<cnName>弹跳</cnName>
			<noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>jump</effectType>
			<duration>999999</duration>
		</skill>
	</father>	
	
	<father name="enemy" cnName="虚晶蝎">
		<skill index="0" name="泰山压顶">
			<name>VirtualScorpion_press</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>泰山压顶</cnName><ignoreSilenceB>1</ignoreSilenceB>
			<cd>10</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition><target>me</target>
			<addType>instant</addType><effectType>no</effectType>
			<meActionLabel>pressAttack</meActionLabel>
			<description>跳至高空，凝聚巨钳之力快速砸向地面，产生巨大的范围伤害。</description>
		</skill>
		<skill index="0" name="蝎舞风暴">
			<name>VirtualScorpion_wind</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>蝎舞风暴</cnName><ignoreSilenceB>1</ignoreSilenceB>
			<cd>14</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition><target>me</target>
			<addType>instant</addType><effectType>no</effectType>
			<meActionLabel>windAttack</meActionLabel>
			<description>制造2波阴阳风暴，阴暴具有极强的破坏力，而阳暴能激发肾上腺素，提高玩家的战斗力！</description>
		</skill>
		<skill index="0" name="战栗光">
			<name>VirtualScorpion_light</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>战栗光</cnName><ignoreSilenceB>1</ignoreSilenceB>
			<cd>19</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget,lifePerLess50</otherConditionArr>
			<conditionRange>500</conditionRange><target>me</target>
			<addType>instant</addType><effectType>no</effectType>
			<meActionLabel>lightAttack</meActionLabel>
			<description>持续从尾部喷射剧毒光,对大范围内的敌人造成战栗伤害。</description>
		</skill>
		<skill index="0" name="潮汐护甲">
			<name>VirtualScorpion_defence</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>潮汐护甲</cnName><ignoreSilenceB>1</ignoreSilenceB><noBeClearB>1</noBeClearB>
			<conditionType>passive</conditionType><condition>add</condition>
			<target>me</target>
			<addType>state</addType><effectType>VirtualScorpion_defence</effectType>
			<duration>9999999</duration>
			<description>紫晶护甲如潮汐般涌动，随着时间不断增长降低。</description>
		</skill>
		
		<skill cnName="阳风增加攻击力"><!-- dps -->
			<name>VirtualScorpion_windHurt</name>
			<cnName>阳风增加攻击力</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>hurtMul</effectType>
			<mul>2</mul>
			<duration>5</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="2hand" con="add" raNum="15" followPartRaB="1">skillEffect/murderous_enemy</stateEffectImg>
		</skill>
		
	</father>	
	
	
	<father name="enemy" cnName="炸裂者">
		<skill name="毒舌钩">
			<name>DryFrogPull</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>毒舌钩</cnName><iconUrl36>SkillIcon/DryFrogPull_36</iconUrl36>
			<cd>9</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>360</conditionRange><target>me</target>
			<addType>no</addType>
			<meActionLabel>pullAttack</meActionLabel>
			<description>炸裂者伸出毒蛇，把目标勾到自己面前，同时对目标释放七步毒。</description>	
		</skill>
		<skill index="0" name="炸裂者-舌头钩-击中"><!-- dps -->
			<name>DryFrogPullHit</name>
			<cnName>炸裂者-舌头钩-击中</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>SnowGirlPullHit</effectType>
			<mul>19</mul>
			<secMul>23</secMul>
			<valueString>belt</valueString>
			<duration>0.2</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
		</skill>
		<skill index="0" name="舌头钩-击中后移动则受到伤害"><!-- dps -->
			<name>DryFrogPullPosion</name>
			<cnName>舌头钩-击中后移动则受到伤害</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>poison7</effectType>
			<extraValueType>producterDpsFactor</extraValueType>
			<mul>0.2</mul>
			<doGap>0.15</doGap>
			<duration>3</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="2foot" con="filter" raNum="30">skillEffect/poisonClaw_enemy</stateEffectImg>
			<description>被七步毒感染的目标，如果移动将持续受到伤害。同样，如果静止不动，将不会受到伤害。</description>
		</skill>
		
		
		<skill index="0" name="雷阵风暴">
			<name>DryFrogRotate</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>布雷风暴</cnName><iconUrl36>SkillIcon/DryFrogRotate_36</iconUrl36>
			<cd>11</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>500</conditionRange><target>me</target>
			
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>1.1</duration>
			<meActionLabel>rotateAttack</meActionLabel>
			<description>炸裂者一跃而起，向地面投射数颗粘性地雷。</description>
		</skill>
		<skill index="0" name="地雷陷阱">
			<name>DryFrogPour</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>地雷陷阱</cnName><iconUrl36>SkillIcon/DryFrogPour_36</iconUrl36>
			<cd>15</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>900</conditionRange><target>me</target>
			
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>1.7</duration>
			<meActionLabel>pourAttack</meActionLabel>
			<description>炸裂者向地面倾倒隐形地雷，隐形地雷会持续长达30秒。</description>
			
		</skill>
		
		<skill name="暗影突袭">
			<name>DryFrogJump</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>暗影突袭</cnName><iconUrl36>SkillIcon/DryFrogJump_36</iconUrl36>
			<cd>5</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.4</conditionRange>
			<target>me</target>
			<effectType>invincible</effectType>
			<duration>0.9</duration>
			<meActionLabel>leapAttack</meActionLabel>
			<pointEffectImg con="add">skillEffect/aperture</pointEffectImg>
			<description>当炸裂者生命值少于一定值时，炸裂者会不断的闪烁到目标身上，造成巨大伤害，同时清空目标子弹。</description>	
		</skill>
	</father>
	
	<father name="enemy" cnName="极速守卫">
		<skill cnName="静电场"><!-- 限制 -->
			<name>eleField_FastGuards</name>
			<cnName>静电场</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>6</cd>
			<delay>0.4</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>150</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>1.3</duration>
			<!--技能链接或者需要添加的被动技能------------------------------------------------------------ --> 
			<linkArr>eleField_FastGuards_link</linkArr>
			<!--图像------------------------------------------------------------ --> 
			<meActionLabel>eleAttack</meActionLabel>
			<meEffectImg soundUrl="sound/magneticField"></meEffectImg>
			<description>极速守卫向外释放强大电流，吸附周围的敌人，距离越近的敌方受到的伤害就越高。</description>
		</skill>
		<skill index="0" cnName="静电场-吸附"><!-- 生存-主动 -->
			<name>eleField_FastGuards_link</name>
			<cnName>静电场-吸附</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>eleField_FastGuards_link</effectType>
			<extraValueType>producterDpsFactor</extraValueType>
			<value>0.3</value>
			<mul>0.35</mul>
			<range>400</range>
			<duration>1.2</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="body" con="add">FastGuards/eleHit</stateEffectImg>
			<description></description>
		</skill>
		
		
		<skill cnName="致命打击"><!-- 限制 -->
			<name>FastGuards_screen</name>
			<cnName>致命打击</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB><ignoreSilenceB>1</ignoreSilenceB><noBeClearB>1</noBeClearB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>15</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>1000</conditionRange>
			<target targetMustLiveB="1" limitNum="5">me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>FastGuards_screen</effectType>
			<valueString>FastGuards_screen</valueString>
			<duration>1.2</duration>
			<range>1000</range>
			<!--图像------------------------------------------------------------ --> 
			<meActionLabel>screenAttack</meActionLabel>
			<pointEffectImg con="add">skillEffect/aperture</pointEffectImg>
			<description>向空中发射镭射炮，对目标造成大量伤害。</description>
		</skill>
		
		<skill cnName="超级弹幕"><!-- 限制 -->
			<name>FastGuards_missile</name>
			<cnName>超级弹幕</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>10</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>600</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>no</effectType>
			<!--图像------------------------------------------------------------ --> 
			<meActionLabel>missileAttack</meActionLabel>
			<description>向空中发射三级子母弹，第一个导弹炸开后分裂成若干个小导弹，小导弹飞行一段距离会继续炸开分裂成更多的导弹。</description>
		</skill>
		
		<skill cnName="极速伤害"><!-- 限制 -->
			<name>FastGuards_spring</name>
			<cnName>极速伤害</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>14</cd>
			<delay>0.33</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget,lifePerLess30</otherConditionArr>
			<conditionRange>700</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>FastGuards_spring</effectType>
			<mul>0.2</mul>
			<duration>6</duration>
			
			<!--图像------------------------------------------------------------ --> 
			<meActionLabel>springAttack</meActionLabel>
			<stateEffectImg con="filter">FastGuards/springEffect</stateEffectImg>
			<description>在极速守卫生命值低于一定值时会进入冲刺模式，此时防御力大幅提高，并对碰撞到的敌人造成巨大伤害。</description>
		</skill>
	</father>
	<father name="enemy" cnName="编织者">
		<skill cnName="瘟疫"><!-- 限制 -->
			<name>Weaver_smoke</name>
			<cnName>瘟疫</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>10</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>800</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>1.3</duration>
			<!--图像------------------------------------------------------------ --> 
			<meActionLabel>smokeAttack</meActionLabel>
			<description>在周围产生持续一段时间的致命瘟疫，持续对感染者产生伤害。</description>
		</skill>
		<skill cnName="织网"><!-- ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇-->
			<name>Weaver_web</name>
			<cnName>织网</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>16</cd>
			<delay>0.57</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>650</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<obj>"name":"Weaver_web","flipB":false,"site":"me"</obj>
			<!--图像------------------------------------------------------------ --> 
			<meActionLabel>webAttack</meActionLabel>
			<description>编织一张蜘蛛网，编织者在网内可隐身、增加移动速度并且不断回复生命值。</description>
		</skill>
		<skill index="0" cnName="织网-隐身"><!-- 限制 -->
			<name>Weaver_web_hiding</name>
			<cnName>织网-隐身</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>Weaver_web_hidingB</effectType>
			<mul>2</mul>
			<duration>2.1</duration>
			<!--图像 ------------------------------------------------------------ -->
			<description>进入织网后进入隐身状态。</description>
		</skill>
		<skill index="0" cnName="织网-回血"><!-- 限制 -->
			<name>Weaver_web_life</name>
			<cnName>织网-回血</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>life</effectType>
			<mul>0.01</mul>
			<value>0</value>
			<!--图像 ------------------------------------------------------------ -->
			<description>进入织网后回复生命。</description>
		</skill>
		<!-- ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇-->
		
		<skill index="0" cnName="繁殖"><!-- 限制 -->
			<name>Weaver_summoned</name>
			<cnName>繁殖</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>22</cd>
			<delay>0.8</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>700</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<!-- 子弹所需 -->
			<obj>"cnName":"毒蛛","num":10,"lifeMul":2,"maxNum":10,"cx":92,"cy":-31,"lifeTime":40,"skillArr":[]</obj>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>summonedAttack</meActionLabel>
			<description>从尾部生产出5只毒蛛参加战斗。</description>
		</skill>
		
		<skill cnName="复仇之刺"><!-- ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ -->
			<name>Weaver_thorn</name>
			<cnName>复仇之刺</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>5</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget,lifePerLess30</otherConditionArr>
			<conditionRange>500</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>1.3</duration>
			<!--图像------------------------------------------------------------ --> 
			<meActionLabel>thornAttack</meActionLabel>
			<description>编织者生命低于一定值时，会不断地使用尾刺对敌人进行攻击。</description>
		</skill>
		<skill index="0" cnName="复仇之刺-击中被拖行"><!-- 限制 -->
			<name>Weaver_thorn_hit</name>
			<cnName>复仇之刺-击中被拖行</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>Weaver_thorn_hit</effectType>
			<duration>2</duration>
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
			<!--图像 ------------------------------------------------------------ -->
			<description></description>
		</skill>
		<!-- ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇-->
		
	</father>
	<father name="enemy" cnName="决斗者">
		<skill index="0" cnName="决斗者-特效预处理"><!-- 限制 -->
			<name>DuelistEffect</name>
			<cnName>决斗者-特效预处理</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>no</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>no</effectType>
			<stateEffectImg partType="" con="filter">Duelist/kickEffect</stateEffectImg>
			<!--图像 ------------------------------------------------------------ -->
			<description></description>
		</skill>
		
		<skill name="旋风斩">
			<name>DuelistShake</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>旋风斩</cnName>
			<cd>5</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<iconUrl36>SkillIcon/IceManRotate_36</iconUrl36>
			<target>me</target>
			<effectType>invincible</effectType>
			<duration>1</duration>
			<meActionLabel>shakeAttack</meActionLabel>
			<pointEffectImg con="add">skillEffect/aperture</pointEffectImg>
			<description>决斗者一跃而起，倾注全身力量到双脚，向敌人劈砍而去！</description>	
		</skill>
		<skill name="旋风斩-减速">
			<name>DuelistShakeSlow</name>
			<cnName>旋风斩-减速</cnName>
			<conditionType>passive</conditionType><condition>hit</condition>
			<target>target</target>
			
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<value>0</value>
			<mul>0.2</mul>
			<duration>4</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="2foot">skillEffect/disabled_enemy</stateEffectImg>
			<description>击中目标后降低其80%的移动速度，持续2秒。</description>
		</skill>
		
		<skill name="旋风波">
			<name>DuelistShoot</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>旋风波</cnName><iconUrl36>SkillIcon/HookWitchShake_36</iconUrl36>
			<cd>7</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<effectType>no</effectType>
			<meActionLabel>shootAttack</meActionLabel>
			<description>决斗者向前方投掷一个能量回旋镖，3秒后回旋镖返回。</description>	
		</skill>
		
		<skill name="决斗术">
			<name>DuelistCombo</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>决斗术</cnName><iconUrl36>SkillIcon/GasDefenseBeat_36</iconUrl36>
			<cd>8</cd>
			<conditionType>active</conditionType><condition>hit</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.4</conditionRange>
			<target>me</target>
			<addType>state</addType>
			<effectType>DuelistCombo</effectType>
			<duration>1</duration>
			<meActionLabel>comboAttack</meActionLabel>
			<description>当决斗者生命值少于40%时会进入狂热状态，每次击中敌人都有可能触发决斗术三连招。</description>	
		</skill>
		<skill name="决斗术-击晕目标">
			<name>DuelistComboHit</name>
			<cnName>决斗术-击晕目标</cnName>
			<noBeClearB>1</noBeClearB>
			<conditionType>passive</conditionType><condition>hit</condition>
			<target>target</target>
			<addType>instantAndState</addType>
			<effectType>dizziness</effectType>
			<duration>1</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
		</skill>
		
		<skill>
			<name>DuelistCloned</name>
			<cnName>分身术</cnName><iconUrl36>SkillIcon/cloned_36</iconUrl36>
			<cd>50</cd>
			<firstCd>45</firstCd>
			<showInLifeBarB>1</showInLifeBarB><noInClonedB>1</noInClonedB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>700</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>cloned</effectType>
			<value>3</value>
			<mul>1</mul>
			<secMul>2</secMul>
			<duration>15</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/cloned_enemy" con="add">skillEffect/hiding_hero</meEffectImg>
			<description>产生[value]个分身，攻击力与自身同等，血量为自身的[mul]，持续[duration]秒。</description>
		</skill>
		<skill index="0" cnName="召唤古惑"><!-- 限制 -->
			<name>DuelistSummoned</name>
			<cnName>召唤古惑</cnName><iconUrl36>IconGather/FoggyZombie</iconUrl36>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>80</cd>
			<firstCd>79</firstCd><summonedUnitsB>1</summonedUnitsB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<!-- 子弹所需 -->
			<obj>"cnName":"古惑僵尸","nextTimeRan":1,"num":2,"lifeMul":100,"dpsMul":3,"lifeTime":25,"mulByFatherB":1,"maxNum":2,"skillArr":["State_SpellImmunity","offAllSkill","offBossAtten","State_AddMove50","FoggyZombieShake","FoggyZombieImpact","noSpeedReduce"]</obj>
			<!--图像------------------------------------------------------------ -->
			<description></description>
		</skill>
	</father>
</data>