<?xml version="1.0" encoding="utf-8" ?>
<data>
	
	<father name="wilder">
		
		
		<body shell="compound">
			
			<name>DiggingBeast</name>
			<cnName>掘地兽</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/enemy/DiggingBeast.swf</swfUrl>
			<!-- 基本系数 -->
			<rosRatio>1</rosRatio>
			<headHurtMul>1</headHurtMul>
			<lifeRatio>1.8</lifeRatio>
			<showLevel>999</showLevel>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<imgArr>
				stand,move
				,normalAttack,rainAttack,drillAttack,airAttack
				,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
			</imgArr>
			<lifeBarExtraHeight>-40</lifeBarExtraHeight>
			<preBulletArr>DiggingRainBullet,DiggingRainBullet_red</preBulletArr>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>6</maxVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>DiggingBeast_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr>KingRabbitTreater,DiggingHammer,DiggingDrill,addDiggingBox,DiggingRain,fightReduct2,defenceBounce_enemy</bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt cd="5">
					<imgLabel>airAttack</imgLabel>
					<grapMinLen>0</grapMinLen><grapMaxLen>600</grapMaxLen>
					<hurtRatio>1</hurtRatio>
					<attackType>holy</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>rainAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-400,-111,100,105</grapRect>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>drillAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.0000000001</hurtRatio>
					<hurtMul>0.12</hurtMul>
					<attackType>holy</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
					
	</father>
	<father name="enemy">	
		
		
		<bullet>
			<name>DiggingRainBullet</name>
			<cnName>锥雨-蓝色</cnName><bulletSkillArr>DiggingRainBulletDieEvent</bulletSkillArr>
			<attackType>holy</attackType><hurtRatio>0</hurtRatio><hurtMul>0.5</hurtMul>
			<bulletAngle>90</bulletAngle><bulletLife>10</bulletLife><bulletWidth>50</bulletWidth><hitType>rect</hitType>	
			<penetrationGap>0</penetrationGap><noMagneticB>1</noMagneticB>
			<bindingD cnName="空单位" lifeMul="0.003" skillArr="rigidBody_enemy" />	
			<bulletSpeed>7</bulletSpeed><speedD random="0.5" />
			<flipX>1</flipX><bulletImgUrl raNum="1">DiggingBeast/bullet</bulletImgUrl>
			<hitImgUrl soundVolume="0.2" soundUrl="boomSound/midBoom2">boomEffect/boom2</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet>
			<name>DiggingRainBullet_red</name>
			<cnName>锥雨-红色</cnName><bulletSkillArr>DiggingRainBulletDieEvent</bulletSkillArr>
			<attackType>holy</attackType><hurtRatio>0</hurtRatio><hurtMul>0.8</hurtMul>
			<bulletAngle>90</bulletAngle><bulletLife>10</bulletLife><bulletWidth>50</bulletWidth><hitType>rect</hitType>	
			<penetrationGap>0</penetrationGap><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<bindingD cnName="空单位" lifeMul="0.006" skillArr="rigidBody_enemy"/>	
			<bulletSpeed>5</bulletSpeed><speedD random="0.1" />
			<flipX>1</flipX><bulletImgUrl raNum="1">DiggingBeast/redBullet</bulletImgUrl>
			<hitImgUrl soundVolume="0.2" soundUrl="boomSound/midBoom2">boomEffect/boom2</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		
		
		
		
		
		
		<skill>
			<name>DiggingRain</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>锥雨</cnName>
			<cd>10</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.5</conditionRange>
			<target>me</target>
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>0.1</duration>
			<meActionLabel>rainAttack</meActionLabel>
			<description>生命值少于50%时，掘地兽对敌人发起锥雨攻击，自己进入无敌状态。打爆10个锥炮或者1个红锥炮才能让锥雨停止。</description>
		</skill>
					<skill>
						<name>DiggingRainBulletDie</name>
						<cnName>锥炮-死亡</cnName>
						<conditionType>passive</conditionType><condition>die</condition>
						<target>me</target>
						<addType>instant</addType>
						<effectType>DiggingRainBulletDie</effectType>
					</skill>
					<skill>
						<name>DiggingRainBulletDie_red</name>
						<cnName>红色锥炮-死亡</cnName>
						<conditionType>passive</conditionType><condition>die</condition>
						<target>me</target>
						<addType>instant</addType>
						<effectType>DiggingRainBulletDie_red</effectType>
					</skill>
					
					
					
		<skill>
			<name>DiggingDrill</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>钻地</cnName>
			<cd>12</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>0.1</duration>
			<meActionLabel>drillAttack</meActionLabel>
			<description>不停的钻入地面，钻出地面时对敌人造成巨大伤害。如果钻出时碰到铁箱或者异龙蛋，则钻地动作停止，碰到铁箱还会被眩晕3秒。</description>
		</skill>	
					<skill><!-- 限制 -->
						<name>DiggingHammer</name>
						<cnName>自己眩晕</cnName><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB><noBeClearB>1</noBeClearB>
						<cd>9999999</cd>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>active</conditionType>
						<target>me</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instantAndState</addType>
						<effectType>dizziness</effectType>
						<duration>3</duration>
						<!--图像------------------------------------------------------------ -->
						<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
						<pointEffectImgUrl soundUrl="boomSound/bigBoom">boomEffect/boom3</pointEffectImgUrl>
					</skill>
					<skill><!-- dps -->
						<name>addDiggingBox</name>
						<cnName>产生铁箱</cnName><ignoreSilenceB>1</ignoreSilenceB>
						<!--英雄技能属性------------------------------------------------------------ -->
						<summonedUnitsB>1</summonedUnitsB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>interval</condition>
						<intervalT>20</intervalT>
						<target>me</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instant</addType>
						<effectType>addDiggingBox</effectType>
						<obj>"cnName":"掘地铁箱","num":1,"lifeMul":1000,"maxNum":1,"noUnderHurtB":1,"noAiFindB":1,"noUnderHitB":1,"skillArr":["State_SpellImmunity","DiggingBoxHole"]</obj>
						<duration>20</duration>
						<!--图像------------------------------------------------------------ -->
						<description>。</description>
					</skill>
					<skill><!-- dps -->
						<name>DiggingBoxHole</name>
						<cnName>铁箱周围射速变慢</cnName>
						<noBeClearB>1</noBeClearB>
						<noSkillDodgeB>1</noSkillDodgeB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>interval</condition>
						<intervalT>0.1</intervalT>
						<target>me,range,enemy</target>
						<!--效果------------------------------------------------------------ -->
						<addType>state</addType>
						<effectType>attackGapMul</effectType>
						<mul>0.2</mul>
						<stateEffectImg partType="shootPoint" raNum="25" followPartRaB="1">bulletHitEffect/smoke_black</stateEffectImg>
						<duration>1</duration>
						<range>150</range>
						<!--图像------------------------------------------------------------ -->
						<description>。</description>
					</skill>
	</father>
	
	<father name="other">
		<body shell="normal">
			
			<name>DiggingBox</name>
			<cnName>掘地铁箱</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/enemy/DiggingBox.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>0.7</lifeRatio>
			<dieJumpMul>0</dieJumpMul>
			<showLevel>999</showLevel>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<imgArr>
				stand,birthAttack,die1
			</imgArr>
			<hitRect>-14,-30,28,30</hitRect>
			<maxJumpNum>0</maxJumpNum>
			<maxVx>0</maxVx>
			<defaultAiOrder>no</defaultAiOrder>
			<nextAttackTime>0</nextAttackTime>
			<skillArr>State_SpellImmunity</skillArr>
			<avtiveSkillCdOverT>0</avtiveSkillCdOverT>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>stand</imgLabel>
					<hurtRatio>0</hurtRatio>
					<attackType>direct</attackType>
					<grapRect>-50,-111,100,105</grapRect>
				</hurt>
				
			</hurtArr>
		</body>	
	</father>
</data>