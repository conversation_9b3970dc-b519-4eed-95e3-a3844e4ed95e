<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="pet" cnName="尸宠">
		<body shell="compound">
		
			<name>PetBoomSkullS</name>
			<cnName>爆骷S</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/pet/PetBoomSkullS.swf</swfUrl>
			<headIconUrl>IconGather/PetBoomSkullS</headIconUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgType>normal</imgType>
			<imgArr>
				stand,move,die1
				,shootAttack1,shootAttack2
			</imgArr>
			<lifeBarExtraHeight>-50</lifeBarExtraHeight>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<maxVx>7</maxVx>
			<motionState>fly</motionState>
			<flyUseSpiderB>1</flyUseSpiderB>
			<flyType>tween</flyType>
			<!-- 技能 -->
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>shootAttack1</imgLabel>
					<bulletLabel>PetBoomSkullS_shoot1</bulletLabel>
					<grapRect>-700,-200,600,400</grapRect>
					<hurtRatio>3.5</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt>
					<imgLabel>shootAttack2</imgLabel>
					<bulletLabel>PetBoomSkullS_machine</bulletLabel>
					<grapRect>-450,-200,200,400</grapRect>
					<hurtRatio>0.2</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
			</hurtArr>
		</body>
		
		
		<bullet cnName="爆骷S-跟踪导弹">
			<name>PetBoomSkullS_shoot1</name>
			<cnName>爆骷S-跟踪导弹</cnName>
			<hurtRatio>0.3</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.5</attackGap>
			<attackDelay>0.3</attackDelay>
			<bulletAngle>179</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
			<!--特殊属性------------------------------------------------------------ -->	
			<followD value="1"/>	<!-- 跟踪 -->
			<penetrationGap>1000</penetrationGap>
			<boomD selfB="1" bodyB="1" radius="250" />
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-160,-67</shootPoint>
			<bulletSpeed>35</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">PetBoomSkullS/bullet1</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2"  shake="3,0.4,13">boomEffect/boom1</hitImgUrl>
			<smokeImgUrl con="filter" raNum="30">bullet/fireballSmoke</smokeImgUrl>
		</bullet>
		<bullet index="0" name="爆骷S-机关枪">
			<name>PetBoomSkullS_machine</name>
			<cnName>爆骷S-机关枪</cnName>
			<!--武器属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>0.001</bulletLife>
			<hitType>longLine</hitType>
			<bulletWidth>1200</bulletWidth>
			<bulletShakeWidth>100</bulletShakeWidth>
			<!--攻击时的属性------------------------------------------------------------ -->
			<shootPoint>0,-60</shootPoint>
			<bulletAngle>175</bulletAngle>
			<attackGap>1.6</attackGap>
			<attackDelay>0.23</attackDelay>
			<bulletNum>12</bulletNum>				
			<shootNum>21</shootNum>					
			<shootGap>0.066</shootGap>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootAngle>170</shootAngle>				
			<shakeAngle>20</shakeAngle>
			<bulletSpeed>0</bulletSpeed>
			<positionD specialType="PetBoomSkullS_machine"/>
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<lineD type="one" lightColor="0x00FFFF" size="3" lightSize="7"/>
			<bulletImgUrl>longLine</bulletImgUrl>
			<hitImgUrl>bulletHitEffect/yellow_motion</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		
		
		
		<bullet cnName="爆骷-婴儿潮">
			<name>babyBoom_skull</name>
			<cnName>爆骷-婴儿潮</cnName>
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>3</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<hitGap>0.3</hitGap>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletAngle>0</bulletAngle>
			<bulletNum>1</bulletNum>				
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<noMagneticB>1</noMagneticB>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>25</bulletSpeed>
			<speedD random="0.2"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>PetBoomSkullS/babyBullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/vehicle_hit1">boomEffect/boom2</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="add">generalEffect/crazy</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
	</father>
	
	
	<father name="petBodySkill">
		<skill>
			<name>babyBoom_skull</name>
			<cnName>婴儿潮</cnName>
			<cd>150</cd>
			<iconUrl>SkillIcon/babyBoom_skull</iconUrl>
			<changeText>技能冷却时间：[cd]秒</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition><!-- cd结束后触发 -->
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>600</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_babyBoom</effectType>
			<extraValueType>nowArmsDps</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<duration>2.2</duration>
			<mul>1.8</mul>
			<!-- 子弹所需 -->
			<obj>"name":"babyBoom_skull"</obj>
			<!--图像------------------------------------------------------------ -->
			<description>爆骷召唤出成群结队的婴儿飞碟攻击敌人，每架飞碟的伤害为爆骷当前战斗力的[mul]。</description>
			<growth>
				<skill><cd>150</cd></skill>
				<skill><cd>131</cd></skill>
				<skill><cd>113</cd></skill>
				<skill><cd>94</cd></skill>
				<skill><cd>80</cd></skill>
				<skill><cd>65</cd></skill>
				<skill><cd>50</cd></skill>
				<skill><cd>50</cd><mul>2.1</mul><changeText>伤害倍数[mul]</changeText></skill>
				<skill><cd>50</cd><mul>2.4</mul><changeText>伤害倍数[mul]</changeText></skill>
			</growth>
		</skill>
	</father>
</data>