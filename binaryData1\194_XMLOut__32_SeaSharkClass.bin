<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="aircraft" cnName="战车定义">
		<equip name="SeaShark" cnName="潜行者" evolutionLabel="Lurker">
			<canComposeB>1</canComposeB>
			<main label="DesertTank_main" dpsMul="0.9" len="75" minRa="-180" maxRa="-179.9"/>
			<sub label="RedReaper_sub" dpsMul="1.3" len="45" minRa="179.9" maxRa="180"/>
			<lifeMul>0.6</lifeMul>
			<attackMul>0</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>100</mustCash>
			<addObjJson>{'dpsAll':0.04,'lifeAll':0.04}</addObjJson>
		</equip>
		<equip name="Lurker" cnName="潜伏者" evolutionLabel="LurkerThird">
			<evolutionLv>2</evolutionLv>
			<main label="DesertTank_main" dpsMul="1.2" len="75" minRa="-180" maxRa="-179.9"/>
			<sub label="RedReaper_sub" dpsMul="1.8" len="45" minRa="179.9" maxRa="180"/>
			<lifeMul>0.6</lifeMul>
			<attackMul>0</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>100</mustCash>
			<addObjJson>{'dpsAll':0.07,'lifeAll':0.07}</addObjJson>
			<skillArr>vehicleFit_fly</skillArr>
		</equip>
		<equip name="LurkerThird" cnName="潜影者">
			<evolutionLv>4</evolutionLv>
			<main label="DesertTank_main" dpsMul="1.4" len="75" minRa="-180" maxRa="-179.9"/>
			<sub label="RedReaper_sub" dpsMul="2.4" len="45" minRa="179.9" maxRa="180"/>
			<lifeMul>1</lifeMul>
			<attackMul>0</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>130</mustCash>
			<addObjJson>{'dpsAll':0.10,'lifeAll':0.10}</addObjJson>
			<skillArr>vehicleFit_fly</skillArr>
		</equip>
		<![CDATA[
		<equip name="LurkerFour" cnName="潜匿者">
			<evolutionLv>5</evolutionLv>
			<main label="DesertTank_main" dpsMul="1.8" len="68" minRa="-180" maxRa="-179.9"/>
			<sub label="RedReaper_sub" dpsMul="2.8" len="90" minRa="179.9" maxRa="180"/>
			<lifeMul>2</lifeMul>
			<attackMul>0</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>130</mustCash>
			<addObjJson>{'dpsAll':0.16,'lifeAll':0.16}</addObjJson>
			<skillArr>vehicleFit_fly</skillArr>
		</equip>
		]]>
	</father>
	
	
	
	
	
	
	<father name="vehicle" cnName="战车body">
		<body index="0" name="潜行者" shell="metal">
			
			<name>SeaShark</name>
			<cnName>潜行者</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/vehicle/SeaShark.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<rotateBySlopeB>1</rotateBySlopeB>
			<imgClass>CarImage</imgClass>
			<imgType>normal</imgType>
			<flipCtrlBy>mouse</flipCtrlBy>
			<imgArr>
				stand,move,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<motionClass>AircraftGroundMotion</motionClass>
			<motionState>fly</motionState>
			<flyType>space</flyType>
			<motionD F_G="0.8" jumpDelayT="0.15" F_I="0.3" F_F="0.9" moveWhenVB="1" F_AIR="3"/>
			<maxVx>12</maxVx>
			<maxJumpNum>1</maxJumpNum>
			<!-- 技能 -->
			<attackAIClass>CarAttack_AI</attackAIClass>
			<keyClass>CarBodyKey</keyClass>
			<bulletLauncherClass>CarBulletLauncher</bulletLauncherClass>
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
			</hurtArr>
		</body>
		
		<body name="潜伏者" fixed="SeaShark" shell="metal">
			<name>Lurker</name>
			<cnName>潜伏者</cnName>
			<swfUrl>swf/vehicle/Lurker.swf</swfUrl>
			<bmpUrl>BodyImg/Lurker</bmpUrl>
		</body>
		<body name="潜影者" fixed="SeaShark" shell="metal">
			<name>LurkerThird</name>
			<cnName>潜影者</cnName>
			<swfUrl>swf/vehicle/LurkerThird.swf</swfUrl>
			<bmpUrl>BodyImg/LurkerThird</bmpUrl>
		</body>
		<body name="潜匿者" fixed="SeaShark" shell="metal">
			<name>LurkerFour</name>
			<cnName>潜匿者</cnName>
			<swfUrl>swf/vehicle/LurkerFour.swf</swfUrl>
			<bmpUrl>BodyImg/LurkerFour</bmpUrl>
			<maxVx>14</maxVx>
		</body>
	</father>
</data>