<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="pumpkin" cnName="桂花糕" priceType="pumpkin" labelArr="all"><!-- 2023年中秋 -->
		<goods dataType="parts" defineLabel="mooncakeParts_1" price="30" buyLimitNum="1" />
		<goods defineLabel="demonSpreadCard" price="3" 			buyLimitNum="5" />
		<goods defineLabel="superSpreadCard" price="3" 			buyLimitNum="5" />
		<goods defineLabel="bigHurtCard" price="3" 			buyLimitNum="5" />
		<goods defineLabel="profiStamp" price="10" 			buyLimitNum="5" />
		
		<goods defineLabel="doubleArmsCard" price="3" 			buyLimitNum="10" />
		<goods defineLabel="doubleEquipCard" price="3" 			buyLimitNum="10" />
		<goods defineLabel="doubleMaterialsCard" price="3" 			buyLimitNum="10" />
		
		<goods defineLabel="madheart" price="2" buyLimitNum="20" />
		<goods defineLabel="demStone" price="2" buyLimitNum="25" />
		<goods defineLabel="arenaChest" price="4" 			buyLimitNum="10" />
		<goods defineLabel="zodiacCash" price="2" 			buyLimitNum="20" />
		
		<goods defineLabel="partsChest87" price="20" buyLimitNum="3" />
		<goods defineLabel="partsChest84" price="7" buyLimitNum="4" />
		
		<goods defineLabel="highVehicleCard" price="3" buyLimitNum="5" />
		<goods defineLabel="highPetCard" price="3" buyLimitNum="5" />
		
		<goods defineLabel="pianoGun" price="2" buyLimitNum="20" />
		<goods defineLabel="yearSnake" price="1" buyLimitNum="20" />
		<goods defineLabel="yearPig" price="1" buyLimitNum="20" />
		<goods defineLabel="yearMonkey" price="1" buyLimitNum="20" />
		<goods defineLabel="yearRabbit" price="1" buyLimitNum="20" />
		<goods defineLabel="yearMouse" price="1" buyLimitNum="20" />
		<goods defineLabel="yearCattle" price="1" buyLimitNum="20" />
		<goods defineLabel="yearDog" price="1" buyLimitNum="20" />
		<goods defineLabel="allBlackCash" price="1" num="10" buyLimitNum="50" />
		<goods defineLabel="allBlackEquipCash" price="1" num="10" buyLimitNum="50" />
		
	</father>
	
	
	<![CDATA[
	<father name="pumpkin" cnName="粽子" priceType="pumpkin" labelArr="all" type="other">
		<goods dataType="parts" defineLabel="balloonParts_1" price="30" buyLimitNum="1" />
		<goods defineLabel="doubleArmsCard" price="3" 			buyLimitNum="10" />
		<goods defineLabel="doubleEquipCard" price="3" 			buyLimitNum="10" />
		<goods defineLabel="doubleMaterialsCard" price="3" 			buyLimitNum="10" />
		
		<goods defineLabel="strengthenStone" num="10" price="1" 			buyLimitNum="200" />
		<goods defineLabel="zodiacCash" price="2" 			buyLimitNum="30" />
		<goods defineLabel="madheart" price="2" buyLimitNum="30" />
		<goods defineLabel="demStone" price="2" buyLimitNum="30" />
		
		<goods defineLabel="lightCone" price="2" buyLimitNum="25" />
		<goods defineLabel="partsChest87" price="15" buyLimitNum="4" />
		<goods defineLabel="partsChest84" price="5" buyLimitNum="4" />
		
		<goods defineLabel="highVehicleCard" price="3" buyLimitNum="5" />
		<goods defineLabel="highPetCard" price="3" buyLimitNum="5" />
		
		<goods defineLabel="pianoGun" price="2" buyLimitNum="20" />
		<goods defineLabel="yearSnake" price="2" buyLimitNum="15" />
		<goods defineLabel="yearPig" price="2" buyLimitNum="15" />
		<goods defineLabel="yearMonkey" price="2" buyLimitNum="15" />
		<goods defineLabel="yearRabbit" price="2" buyLimitNum="15" />
		<goods defineLabel="yearMouse" price="2" buyLimitNum="15" />
		<goods defineLabel="yearCattle" price="2" buyLimitNum="15" />
		<goods defineLabel="yearDog" price="1" buyLimitNum="15" />
		<goods dataType="parts" defineLabel="huntParts_1" price="2" buyLimitNum="50" />
		<goods dataType="parts" defineLabel="acidicParts_1" price="2" buyLimitNum="50" />
		<goods defineLabel="allBlackCash" price="1" num="5" buyLimitNum="30" />
		<goods defineLabel="allBlackEquipCash" price="1" num="5" buyLimitNum="30" />
	</father>
	
	
	<father name="pumpkin" cnName="小南瓜" priceType="pumpkin" labelArr="all" >
		<goods dataType="fashion" inBagType="equip" defineLabel="pumpkinHead" price="10" buyLimitNum="2" />
		<goods defineLabel="intensDrug" price="3" buyLimitNum="10" />
		<goods defineLabel="madheart" price="2" buyLimitNum="20" />
		<goods defineLabel="demStone" price="2" buyLimitNum="30" />
		<goods defineLabel="arenaChest" price="3" 			buyLimitNum="10" />
		<goods defineLabel="zodiacCash" price="2" 			buyLimitNum="10" />
		<goods defineLabel="partsChest87" price="20" buyLimitNum="3" />
		<goods defineLabel="partsChest84" price="7" buyLimitNum="4" />
		
		<goods defineLabel="highVehicleCard" price="3" buyLimitNum="5" />
		<goods defineLabel="highPetCard" price="3" buyLimitNum="5" />
		
		
		<goods defineLabel="pianoGun" price="1" buyLimitNum="20" />
		<goods defineLabel="yearSnake" price="1" buyLimitNum="15" />
		<goods defineLabel="yearPig" price="1" buyLimitNum="15" />
		<goods defineLabel="yearMonkey" price="1" buyLimitNum="15" />
		<goods defineLabel="yearRabbit" price="1" buyLimitNum="15" />
		<goods defineLabel="yearMouse" price="1" buyLimitNum="15" />
		<goods defineLabel="yearCattle" price="1" buyLimitNum="15" />
		<goods defineLabel="yearDog" price="1" buyLimitNum="15" />
		<goods dataType="parts" defineLabel="huntParts_1" price="2" buyLimitNum="20" />
		<goods dataType="parts" defineLabel="acidicParts_1" price="2" buyLimitNum="20" />
		<goods defineLabel="allBlackCash" price="2" num="5" buyLimitNum="15" />
		<goods defineLabel="allBlackEquipCash" price="2" num="5" buyLimitNum="15" />
	</father>
	
	
	
	
	
	
	
	
	
	<father name="pumpkin" cnName="糖果" priceType="pumpkin" labelArr="all">
		
	<!-- 去掉type="other"就可以显示了 -->
		<goods defineLabel="allBlackCash" num="5" price="1"    buyLimitNum="30" />
<goods defineLabel="allBlackEquipCash" num="5" price="1"    buyLimitNum="30" />
<goods defineLabel="PetIronChiefBook" price="1"    buyLimitNum="50" />
<goods defineLabel="PetBoomSkullBook" price="1"    buyLimitNum="50" />
<goods defineLabel="PetLakeBook" price="1"    buyLimitNum="50" />
<goods defineLabel="partsChest87" price="10"    buyLimitNum="3" />
<goods defineLabel="demStone" price="3"    buyLimitNum="10" />
<goods defineLabel="zodiacCash" price="2"    buyLimitNum="20" />
<goods defineLabel="armsGemChest" price="1"    buyLimitNum="30" />
<goods defineLabel="equipGemChest" price="1"    buyLimitNum="30" />
<goods defineLabel="yearSnake" price="1"    buyLimitNum="20" />
<goods defineLabel="yearPig" price="1"    buyLimitNum="20" />
<goods defineLabel="yearMonkey" price="1"    buyLimitNum="20" />
<goods defineLabel="yearRabbit" price="1"    buyLimitNum="20" />
<goods defineLabel="yearMouse" price="1"    buyLimitNum="20" />
<goods defineLabel="yearCattle" price="1"    buyLimitNum="20" />
<goods defineLabel="pianoGun" price="1"    buyLimitNum="20" />
<goods defineLabel="lightCone" price="1"    buyLimitNum="30" />
<goods dataType="parts" defineLabel="huntParts_1" price="1"    buyLimitNum="30" />
<goods dataType="parts" defineLabel="acidicParts_1" price="1"    buyLimitNum="30" />


	</father>
	
	
	]]>
	<![CDATA[
	
	]]>
	
	
	
</data>