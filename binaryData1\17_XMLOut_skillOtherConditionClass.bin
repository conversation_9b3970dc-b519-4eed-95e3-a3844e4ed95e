<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father cnName="目标判断">
		<body cnName="" name="targetIsHero" tip="目标是持枪单位"/>
		<body cnName="" name="targetFitVehicle" tip="目标是聚合载具"/>
		<body cnName="" name="tergetName" tip="目标的单位名称" conditionString="单位名称" stringType="bodyName" />
		<body cnName="" name="targetUnitType" tip="目标的单位类型" conditionString="单位类型" stringType="unitType"/>
		<body cnName="" name="targetBossOrSuper" tip="目标是精英怪或者首领"/>
		<body cnName="" name="noMainPlayer" tip="目标不是主角"/>
		<body cnName="" name="puMainPlayer" tip="释放者是主角"/>
		<body cnName="" name="raceType" tip="目标的种族类型" conditionString="种族类型" stringType="raceType"/>
		<body cnName="" name="isTrueBody" tip="目标不是召唤单位"/>
		<body cnName="" name="noTrueBody" tip="目标是召唤单位"/>
		<body cnName="" name="noMadboss" tip="目标不是战神"/>
	</father>
	<father>
		<body cnName="" name="canFoggyDef" tip="可以触发自身抵御技能"/>
		<body cnName="" name="targetIsInvincible" tip="目标处于无敌状态"/>
		<body cnName="" name="hurtUnderIsInvincible" tip="伤害的目标处于无敌状态"/>
		<body cnName="" name="producerNoInvincible" tip="释放者不处于无敌状态"/>
		<body cnName="" name="skillSpecialNumLess" tip="技能特殊参数少于" conditionRange="数量" />
		<body cnName="" name="haveSkillOr" tip="拥有其中某个技能" conditionString="技能组" />
		<body cnName="" name="noState" tip="目标没有某个状态" conditionString="状态组" />
		<body cnName="" name="haveStateOr" tip="目标有某个状态" conditionString="状态组" />
		<body cnName="" name="meHaveStateOr" tip="释放者有某个状态" conditionString="状态组" />
		<body cnName="" name="meHaveState" tip="释放者有状态" conditionString="状态" />
		<body cnName="" name="floorState" tip="释放者的下蹲状态" conditionString="下蹲状态" />
		<body cnName="" name="targetFloorState" tip="目标的下蹲状态" conditionString="下蹲状态" />
		<body cnName="" name="isStand" tip="目标处于站立"/>
		<body cnName="" name="isFlyB" tip="目标是飞行单位"/>
		<body cnName="" name="isFlyState" tip="目标当前是飞行单位"/>
		<body cnName="" name="isAirB" tip="目标处于空中"/>
		<body cnName="" name="isGroundB" tip="目标处于地面"/>
		<body cnName="" name="isHero" tip="目标是持枪"/>
	</father>
	<father cnName="动画">
		<body cnName="" name="nowImgLabel" tip="释放者当前动画(组)" conditionString="动画标签组" />
		<body cnName="" name="targetNoImgLabel" tip="释放者当前动画不是" conditionString="动画标签" />
		<body cnName="" name="standOrRun" tip="释放者当前处于站立或奔跑状态"/>
		<body cnName="" name="producerImgLabel" tip="释放者当前动画" conditionString="动画标签"/>
		<body cnName="" name="noAttackImg" tip="释放者当前不处于攻击动作"/>
	</father>
	<father cnName="敌我关系">
		<body cnName="" name="nearEnemy" tip="附近多少码内有敌人" conditionRange="距离" />
		<body cnName="" name="followingTarget" tip="释放者处于跟踪状态" />
		<body cnName="" name="awayTarget" tip="距离目标远于" conditionRange="距离" />
		<body cnName="" name="nearAttackTarget" tip="距离索敌" conditionRange="距离" />
		<body cnName="" name="attackTargetUnitType" tip="索敌的单位类型" conditionString="单位类型"/>
		<body cnName="" name="gapLess" tip="与目标的距离小于" conditionRange="距离"/>
		<body cnName="" name="upAttackTargetByRange" tip="索敌距离我头顶多高" conditionRange="距离"/>
	</father>
	<father>
		<body cnName="" name="weLifePerLess" tip="范围内队友血量少于" range="范围" conditionRange="血量" />
		<body cnName="" name="invisibility_hero" tip="范围内队友血量少于(小樱专用)" range="范围" conditionRange="血量" />
		<body cnName="" name="lifePerLess" tip="自身血量少于" conditionRange="血量" />
		
		<body cnName="" name="hiding_hero" tip="距离目标(隐身专用)" conditionRange="距离" />
		<body cnName="" name="producerNoNowBuff" tip="释放者没有当前技能buff"/>
		<body cnName="" name="charm_hero" tip="索敌血量大于50%且距离800码"/>
		<body cnName="" name="canFoggyDef" tip="自身可以释放抵御时"/>
		
	</father>
</data>