<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="enemy">
		
		<body index="0" name="童灵尸">
			
			<name>SnowThin</name>
			<cnName>童灵尸</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/SnowThin.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<headHurtMul>1.3</headHurtMul>
			<defenceRatio>0.2</defenceRatio>
			<showLevel>90</showLevel>
			<!-- 图像 -->
			<imgArr>
				stand,move,dragMove
				,normalAttack,shootAttack,rotateAttack,hurt1,hurt2,die1,die2
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,__fill2_Up,fill2_Up,fill2_Up__fill2_Down,fill2_Down,fill2_Down__die2
			</imgArr>
			<bossImgArr>rotateAttack,dragMove</bossImgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-76,28,76</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>7</maxVx>
			<!-- AI属性 -->
			<nextAttackTime>1</nextAttackTime>
			<extraAIClassLabel>SnowThin_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<skillArr>rigidBody_enemy</skillArr>
			<bossSkillArr>fightReduct2,rigidBody_enemy,groupSpeedUp_enemy,silence_enemy,SnowThinRuin</bossSkillArr>
			<bossSkillArrCn>70%近战防御、刚体、群体加速、沉默</bossSkillArrCn>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/bodyHit">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel>
					<bulletLabel>SnowThinShoot</bulletLabel>
					<grapRect>-300,-111,250,105</grapRect>
					<hurtRatio>1.5</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt  info="不加入ai选择"><noAiChooseB>1</noAiChooseB>
					<imgLabel>rotateAttack</imgLabel>
					<bulletLabel>SnowThinRotate</bulletLabel>
					<hurtRatio>2.5</hurtRatio>
					<grapRect>-300,-111,250,105</grapRect>
				</hurt>
			</hurtArr>
		</body>	
		<body index="0" name="关东尸">
			
			<name>SnowFatty</name>
			<cnName>关东尸</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/SnowFatty.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<showLevel>90</showLevel>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<imgArr>
				stand,move,dragMove
				,normalAttack,shootAttack,sprintShootAttack,sprintAttack,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<bossImgArr>sprintShootAttack,sprintAttack,dragMove</bossImgArr>
			<lifeBarExtraHeight>-30</lifeBarExtraHeight>
			<!-- 碰撞体积 -->
			
			<hitRect>-18,-86,36,86</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>6</maxVx>
			<!-- AI属性 -->
			<nextAttackTime>1</nextAttackTime>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr>fightReduct2,SnowFattySprint</bossSkillArr>
			<bossSkillArrCn>70%近战防御、95%防毒</bossSkillArrCn>
			<extraAIClassLabel>SnowFatty_AIExtra</extraAIClassLabel>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel>
					<hurtRatio>4.5</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<bulletLabel>SnowFattyShoot</bulletLabel>
					<grapRect>-400,-111,350,105</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt  info="不加入ai选择"><noAiChooseB>1</noAiChooseB>
					<imgLabel>sprintShootAttack</imgLabel>
					<bulletLabel>SnowFattySprint</bulletLabel>
					<grapRect>-400,-111,350,105</grapRect>
					<hurtRatio>2.7</hurtRatio>
					<attackType>holy</attackType>
				</hurt>
				<hurt  info="不加入ai选择"><noAiChooseB>1</noAiChooseB>
					<imgLabel>sprintAttack</imgLabel>
					<skillArr>hitBloodWhite</skillArr>
					<hurtRatio>2.7</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/hand_hit">bladeHitEffect/blood</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
		
		<body index="0" name="卫队尸">
			
			<name>SnowSoldiers</name>
			<cnName>卫队尸</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/SnowSoldiers.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<headHurtMul>1.4</headHurtMul>
			<showLevel>90</showLevel>
			<!-- 图像 -->
			<imgArr>
				stand,move,dragMove
				,normalAttack,hookAttack,rotateAttack,hurt1,hurt2,die1,die2
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,__fill2_Up,fill2_Up,fill2_Up__fill2_Down,fill2_Down,fill2_Down__die2
			</imgArr>
			<bossImgArr>rotateAttack,dragMove</bossImgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-76,28,76</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>8</maxVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<!-- 技能 -->
			<skillArr>rigidBody_enemy,silence_SnowSoldiers</skillArr>
			<bossSkillArr>fightReduct2,treater_knights,defenceBounce_enemy,desertedHalo_enemy,clonedSnowSoldiers,teleport_SnowSoldiers,underCrossbow_SnowSoldiers</bossSkillArr>
			<bossSkillArrCn>70%近战防御、净化器、荒芜光环、胶性表皮、分身</bossSkillArrCn>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel>
					<hurtRatio>1.7</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>hookAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>4</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt  info="不加入ai选择"><noAiChooseB>1</noAiChooseB>
					<imgLabel>rotateAttack</imgLabel>
					<hurtRatio>0.8</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2">bulletHitEffect/energy</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
		<body index="0" name="女爵尸" shell="normal">
			
			<name>SnowGirl</name>
			<cnName>女爵尸</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/SnowGirl.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>0.7</lifeRatio>
			<showLevel>90</showLevel>
			<!-- 图像 -->
			<imgArr>
				stand,move
				,normalAttack,shootAttack,hookAttack,pullAttack,pullAfterAttack,hurt1,hurt2,die1,die2
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,__fill2_Up,fill2_Up,fill2_Up__fill2_Down,fill2_Down,fill2_Down__die2
			</imgArr>
			<bossImgArr>pullAttack,pullAfterAttack</bossImgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-76,28,76</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>10</maxVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<!-- 技能 -->
			<skillArr>rigidBody_enemy</skillArr>
			<bossSkillArr>fightReduct2,desertedHalo_enemy,defenceBounce_enemy,addMove_Crawler,SnowGirlHookHit,SnowGirlPull,IceManCorrosion</bossSkillArr>
			<bossSkillArrCn>70%近战防御、荒芜光环、胶性表皮、狂躁、腐蚀</bossSkillArrCn>
			<extraAIClassLabel>SnowGirl_AIExtra</extraAIClassLabel>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel>
					<hurtRatio>1.6</hurtRatio>
					<skillArr>silence_SnowSoldiers</skillArr>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/body_hit">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>2</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/body_hit">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>hookAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>1</hurtRatio>
					<attackType>holy</attackType>
					<skillArr>SnowGirlHook</skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/body_hit">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt  info="不加入ai选择"><noAiChooseB>1</noAiChooseB>
					<imgLabel>pullAttack</imgLabel>
					<hurtRatio>0.001</hurtRatio>
					<skillArr>SnowGirlPullHit</skillArr>
					<attackType>holy</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/body_hit">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt  info="不加入ai选择"><noAiChooseB>1</noAiChooseB><noUseOtherSkillB>1</noUseOtherSkillB>
					<imgLabel>pullAfterAttack</imgLabel>
					<hurtRatio>0.5</hurtRatio>
					<skillArr>SnowGirlAfterPullHit</skillArr>
					<attackType>holy</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/body_hit">bladeHitEffect/blood</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
		
		<body index="0" name="野帝" shell="normal">
			
			<name>IceMan</name>
			<cnName>野帝</cnName><headIconUrl>IconGather/IceMan</headIconUrl>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/IceMan.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.8</lifeRatio>
			<rateRatio>0.05</rateRatio>
			<showLevel>999</showLevel>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<imgArr>
				stand,move
				,normalAttack1,normalAttack2,shootAttack1,shootAttack2
				,shakeAttack,kickAttack,rotateAttack,smashAttack,strikeAttack,shakeAttack
				,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<lifeBarExtraHeight>-50</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>12</maxVx>
			<runStartVx>12</runStartVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>IceMan_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr>fightReduct2,defenceBounce_enemy,IceManRotate,IceManRotateLink,IceManStrike,IceManKick,IceManShake,IceManSnowman</bossSkillArr>
			<bossSkillArrCn>70%近战防御、胶性表皮</bossSkillArrCn>
			<extraDropArmsB>1</extraDropArmsB>
			<extraG label="ExtraTaskAI" bossSkillArr="cmldef_enemy,fightReduct2,defenceBounce_enemy,IceManRotate,IceManRotateLink,IceManStrike,IceManKick,IceManShake,IceManSnowman">
				<extra lifeMin="0.7" skillArr=""/>
				<extra lifeMin="0.4" skillArr=""/>
				<extra lifeMin="0" skillArr=""/>
			</extraG>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack1</imgLabel><cn>抱拳</cn>
					<hurtRatio>1.5</hurtRatio>
					<attackType>direct</attackType>
					<skillArr>IceManCorrosion</skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>normalAttack2</imgLabel><cn>直拳</cn>
					<hurtRatio>1.5</hurtRatio>
					<attackType>direct</attackType>
					<skillArr>IceManCorrosion</skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack1</imgLabel><cn>射击</cn>
					<bulletLabel>IceManShoot1</bulletLabel>
					<grapRect>-518,-147,150,127</grapRect>
					<hurtRatio>1.5</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt>
					<imgLabel>shootAttack2</imgLabel><cn>连射</cn>
					<bulletLabel>IceManShoot2</bulletLabel>
					<grapRect>-518,-147,150,127</grapRect>
					<hurtRatio>1.5</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				
				<hurt info="爆怒一击"><noAiChooseB>1</noAiChooseB>
					<imgLabel>shakeAttack</imgLabel>
					<grapRect>-518,-147,150,127</grapRect>
					<hurtMul>0.5</hurtMul>
					<attackType>holy</attackType>
					<noUseOtherSkillB>1</noUseOtherSkillB>
				</hurt>
				<hurt info="踢爆"><noAiChooseB>1</noAiChooseB>
					<imgLabel>kickAttack</imgLabel>
					<bulletLabel>IceManKick</bulletLabel>
					<hurtRatio>0.5</hurtRatio>
					<attackType>direct</attackType>
					<skillArr>IceManKickHit</skillArr>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt info="冲锋"><noAiChooseB>1</noAiChooseB>
					<imgLabel>strikeAttack</imgLabel>
					<hurtRatio>0.4</hurtRatio>
					<attackType>accurate</attackType>
					<skillArr>IceManStrikeHit</skillArr>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				
				<hurt info="飞雪连舞-砸"><noAiChooseB>1</noAiChooseB>
					<imgLabel>smashAttack</imgLabel>
					<hurtRatio>2.3</hurtRatio>
					<attackType>direct</attackType>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt info="飞雪连舞"><noAiChooseB>1</noAiChooseB>
					<imgLabel>rotateAttack</imgLabel>
					<hurtRatio>0.5</hurtRatio>
					<attackType>direct</attackType>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				
			</hurtArr>
		</body>	
	</father>
	
	<father name="other" cnName="其他">
		<body index="0" name="雪人" shell="normal">
		
			<name>Snowman</name><headIconUrl>IconGather/Snowman</headIconUrl>
			<cnName>雪人</cnName>
			<raceType>snow</raceType>
			<swfUrl>swf/enemy/Snowman.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>0.7</lifeRatio>
			<dieJumpMul>0</dieJumpMul>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<imgArr>
				stand,birthAttack,die1
			</imgArr>
			<dieImg soundUrl="sound/snowHit"></dieImg>
			<!-- 碰撞体积 -->
			<hitRect>-14,-30,28,30</hitRect>
			<!-- 运动 -->
			<maxJumpNum>0</maxJumpNum>
			<maxVx>0</maxVx>
			<!-- AI属性 -->
			<defaultAiOrder>no</defaultAiOrder>
			<nextAttackTime>0</nextAttackTime>
			<!-- 技能 -->
			<skillArr>State_SpellImmunity</skillArr>
			<avtiveSkillCdOverT>0</avtiveSkillCdOverT>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>stand</imgLabel>
					<hurtRatio>0</hurtRatio>
					<shakeValue>4</shakeValue>
					<attackType>direct</attackType>
					<grapRect>-50,-111,100,105</grapRect>
				</hurt>
			</hurtArr>
		</body>	
		<body index="0" name="赤鼬导弹发射器" shell="metal">
		
			<name>RifleHornetShooter</name><headIconUrl>IconGather/RifleHornetShooter</headIconUrl>
			<cnName>赤鼬导弹发射器</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/enemy/RifleHornetShooter.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>0.7</lifeRatio>
			<dieJumpMul>0</dieJumpMul>
			<!-- 图像 -->
			<imgArr>
				stand,birthAttack,shootAttack,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-30,28,30</hitRect>
			<!-- 运动 -->
			<maxJumpNum>0</maxJumpNum>
			<maxVx>10</maxVx>
			<motionState>fly</motionState>
			<flipCtrlBy>target</flipCtrlBy>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<!-- 技能 -->
			<skillArr>State_SpellImmunity</skillArr>
			<avtiveSkillCdOverT>0</avtiveSkillCdOverT>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>shootAttack</imgLabel>
					<hurtRatio>1</hurtRatio>
					<bulletLabel>RifleHornetShooter</bulletLabel>
					<attackType>direct</attackType>
					<grapRect>-9999,-9999,19999,19999</grapRect>
				</hurt>
			</hurtArr>
		</body>	
	</father>
		
</data>