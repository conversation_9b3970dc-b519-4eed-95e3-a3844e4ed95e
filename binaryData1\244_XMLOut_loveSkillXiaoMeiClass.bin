<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="loveSkill" cnName="忠诚度技能">
		<skill>
			<name>cdMulMei</name><cnName>技能回复速度提升20%</cnName><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
			<conditionType>passive</conditionType><condition>add</condition><target>me</target><addType>state</addType><duration>99999</duration>
			<mul>1.2</mul><effectType>cdSpeed</effectType>
			<description>技能回复速度提升[mul-1]。</description>
		</skill>
		
		<skill>
			<name>cdHoleMei</name>
			<cnName>技能回复光环</cnName>
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target noMeB="1">me,range,we</target>
			<addType>state</addType>
			<effectType>cdSpeed</effectType>
			<mul>1.3</mul>
			<duration>0.2</duration>
			<range>200</range>
			<stateEffectImg name="cdHole"/>
			<description>提升周围[range]码内队友[mul-1]的技能回复速度。</description>
		</skill>
		
		<skill>
			<name>enemyCdMei</name>
			<cnName>降低敌人技能回复</cnName><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target>attackTarget</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>cdSpeed</effectType>
			<mul>0.8</mul>
			<duration>0.2</duration>
			<stateEffectImg name="cdDown"/>
			<description>减少目标敌人[1-mul]的技能回复速度。</description>
		</skill>
		
		<skill>
			<name>angerHoleMei</name>
			<cnName>怒气回复光环</cnName><noBeClearB>1</noBeClearB>
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target noMeB="1">me,range,we</target>
			<addType>state</addType>
			<effectType>angerAddMul</effectType>
			<mul>1.4</mul>
			<duration>0.2</duration>
			<range>130</range>
			<stateEffectImg name="angerHole"/>
			<description>提升周围[range]码内队友[mul-1]的怒气回复速度。</description>
		</skill>
		
		
		<skill cnName="封锁">
			<name>dieOffMei</name>
			<cnName>死后封锁凶手</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>die</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>noAllSkill</effectType>
			<duration>10</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="mouth" con="add">skillEffect/silenceMore</stateEffectImg>
			<description>死后将封锁凶手[duration]秒。</description>
		</skill>
		<skill cnName="无敌">
			<name>dieNiuBiMei</name>
			<cnName>死后队友无敌</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>die</condition>
			<target noMeB="1">me,range,we</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>5</duration>
			<range>99999</range>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg name="FoggyDefence_state"/>
			<description>死后所有队友将无敌[duration]秒。</description>
		</skill>
		
		<skill>
			<name>degradationMei</name>
			<cnName>退化光环</cnName><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>underHurtMul</effectType>
			<mul>1.13</mul>
			<duration>1</duration>
			<range>999999</range>
			<stateEffectImg name="outfit_blood_state"/>
			<description>降低所有敌人的[mul-1]的防御力。</description>
		</skill>
	</father>
</data>