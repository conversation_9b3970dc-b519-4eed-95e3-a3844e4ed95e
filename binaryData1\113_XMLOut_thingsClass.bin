<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="props" cnName="银币消费类" smeltType="coin">
		<!-- 银币商品 -->
		<things cnName="生命药瓶">
			<name>lifeBottle</name>
			<cnName>生命药瓶</cnName>
			<zuobiNum>20000000</zuobiNum>
			<btnList>use</btnList>
			<iconUrl>dropEffect/addLifeMul</iconUrl><!-- 技能图标 -->
			<description>在正常关卡中，使用后补充主角100%的生命值。在竞技场中，使用后补充单位50%的生命值。</description>
			<effectD type="addLifeMul" value="1" useInLevelB="1" levelUseLimitNum="2" effectLabel="skillEffect/groupLight_hero" effectSound="sound/groupLight_hero" />
			<loveD num="30"/>
			<smeltD grade="0" addType="" price="1"/>
		</things>
		<things cnName="队友生命药瓶">
			<name>teamLifeBottle</name>
			<cnName>队友生命药瓶</cnName>
			<zuobiNum>20000000</zuobiNum>
			<btnList>use</btnList>
			<iconUrl>ThingsIcon/teamLifeBottle</iconUrl><!-- 技能图标 -->
			<description>在正常关卡中，使用后补充所有队友以及尸宠100%的生命值。在竞技场中，使用后补充所有队友以及尸宠50%的生命值。</description>
			<effectD type="addTeamLifeMul" value="1" useInLevelB="1" levelUseLimitNum="2" moreLimitB="1" effectLabel="skillEffect/groupLight_hero" effectSound="sound/groupLight_hero"/>
			<loveD num="30"/>
			<smeltD grade="0" addType="" price="1"/>
		</things>
		<things cnName="弹药箱">
			<name>caisson</name>
			<cnName>弹药箱</cnName>
			<zuobiNum>20000000</zuobiNum>
			<btnList>use</btnList>
			<iconUrl>dropEffect/addCharger</iconUrl><!-- 技能图标 -->
			<description>使用后补充所有友方单位所有枪支类型的携弹量。</description>
			<effectD type="addAllChargerMul" value="1" useInLevelB="1"  levelUseLimitNum="4" effectLabel="skillEffect/greenLight" effectSound="sound/groupLight_hero"/>
			<loveD num="30"/>
			<smeltD grade="0" addType="" price="1"/>
		</things>
	</father>
	<father name="props" cnName="其他">	
		<!-- 其他道具 -->
		<things cnName="手机">
			<name>mobilePhone</name>
			<cnName>一部手机</cnName>
			<noOverlayB>1</noOverlayB>
			<btnList>uncall</btnList>
			<iconUrl>ThingsIcon/mobilePhone</iconUrl><!-- 技能图标 -->
			<description>一部只能接听无法拨打的手机。</description>
		</things>
	</father>
	<father name="materials" cnName="金币道具">
		<!-- 黄金商品 -->    
		<things cnName="重生石">
			<name>rebirthStone</name>
			<cnName>重生石</cnName>
			<zuobiNum>1000</zuobiNum>
			<iconUrl>ThingsIcon/rebirthStone</iconUrl><!-- 技能图标 -->
			<description>来自外星的另外一种奇特石头，可用于复活你的生命。</description>
			<effectD type="rebirth" value="1" useInLevelB="1" levelUseLimitNum="4" arenaLevelUseLimitNum="1" effectLabel="skillEffect/groupLight_hero" effectSound="sound/groupLight_hero"/>
			<smeltD addType="stone" price="9" />
		</things>
	</father>
	
	<father name="props" cnName="消耗型卡片">	
		<things cnName="队友重生卡">
			<name>teamRebirthCard</name>
			<cnName>队友重生卡</cnName>
			<zuobiNum>2000</zuobiNum>
			<btnList>use</btnList>
			<iconUrl>ThingsIcon/teamRebirthCard</iconUrl><!-- 技能图标 -->
			<description>使用后复活关卡中已倒下的队友。</description>
			<effectD type="rebirthTeam" value="1" useInLevelB="1" moreLimitB="1" levelUseLimitNum="2" arenaLevelUseLimitNum="1" effectLabel="skillEffect/groupLight_hero" effectSound="sound/groupLight_hero"/>
			<smeltD addType="box" price="5"/>
		</things>
		<things cnName="技能刷新卡">
			<name>skillFleshCard</name>
			<cnName>技能刷新卡</cnName>
			<zuobiNum>2000</zuobiNum>
			<btnList>use</btnList>
			<iconUrl>ThingsIcon/skillFleshCard</iconUrl><!-- 技能图标 -->
			<description>使用后刷新当前控制单位所有主动技能的冷却时间、载具的冷却时间。</description>
			<effectD type="fleshSkillCd" value="1" useInLevelB="1" levelUseLimitNum="2" />
			<smeltD addType="festival" price="5"/>
		</things>
		
		
	</father>	
	<father name="props" cnName="节日礼包">
		<things cnName="清明节礼包">
			<name>qingming2017Gift</name>
			<cnName>清明节礼包</cnName>
			<iconUrl>ThingsIcon/normalGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店银币购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="1"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;normalChest;1</gift>
			<gift>things;doubleHighCard;2</gift>
			<gift>things;armsTitanium;20</gift>
			<gift>things;armsRadium;20</gift>
			<gift>things;strengthenStone;50</gift>
			<gift>things;rifleHornet;5</gift>

		</things>
		
		
		<things cnName="绿色家园礼包">
			<name>arborDayGift</name>
			<cnName>绿色家园礼包</cnName>
			<iconUrl>ThingsIcon/normalGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店银币购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="1"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;skillFleshCard;5</gift>
			<gift>things;coinHeap_3;1</gift>
			<gift>things;lifeCatalyst;100</gift>
			<gift>things;energyCatalyst;100</gift>
			<gift>things;variationCatalyst;100</gift>
			<gift>things;magicChest;1</gift>
			<gift>things;armsTitanium;50</gift>
			<gift>things;armsRadium;80</gift>
		</things>
		
		
		<things cnName="感恩节全民礼包">
			<name>normalGratitudeGift</name>
			<cnName>感恩节全民礼包</cnName>
			<iconUrl>ThingsIcon/normalGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店银币购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;bloodStone;20</gift>
			<gift>things;godStone;80</gift>
			<gift>things;skillStone;120</gift>
			<gift>things;converStone;80</gift>
		</things>
		<things cnName="感恩节豪华礼包">
			<name>goldGratitudeGift</name>
			<cnName>感恩节豪华礼包</cnName>
			<iconUrl>ThingsIcon/goldGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店黄金购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;sniperSmilodon;2</gift>
			<gift>things;rebirthStone;2</gift>
			<gift>things;skillFleshCard;2</gift>
			<gift>things;strengthenStone;20</gift>
			<gift>things;converStone;20</gift>
		</things>
		
		<things cnName="元宵节全民礼包">
			<name>normalYuanGift2016</name>
			<cnName>元宵节全民礼包</cnName>
			<iconUrl>ThingsIcon/normalGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店银币购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;armsEchelonCard;10</gift>
			<gift>things;equipEchelonCard;10</gift>
			<gift>things;strengthenStone;20</gift>
			<gift>things;godStone;20</gift>
		</things>
		<things cnName="元宵节黄金礼包">
			<name>goldYuanGift2016</name>
			<cnName>元宵节黄金礼包</cnName>
			<iconUrl>ThingsIcon/goldGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店黄金购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;anniversaryCash;10</gift>
			<gift>things;armsEchelonCard;10</gift>
			<gift>things;equipEchelonCard;10</gift>
			<gift>things;strengthenStone;20</gift>
			<gift>things;godStone;20</gift>
		</things>
		
		<things cnName="元宵节畅玩礼包">
			<name>normalYuanGift2017</name>
			<cnName>元宵节全民礼包</cnName>
			<iconUrl>ThingsIcon/normalGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店银币购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;strengthenStone;20</gift>
			<gift>things;doubleMaterialsCard;5</gift>
			<gift>things;doubleEquipCard;5</gift>
			<gift>things;keyChest;10</gift>
			<gift>things;coinHeap_1;2</gift>
			<gift>things;anniversaryCash;50</gift>
			<gift>things;allBlackCash;10</gift>
			<gift>things;teamRebirthCard;3</gift>
			<gift>things;skillFleshCard;3</gift>

		</things>
		<things cnName="元宵节豪华礼包">
			<name>goldYuanGift2017</name>
			<cnName>元宵节黄金礼包</cnName>
			<iconUrl>ThingsIcon/goldGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店黄金购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;bloodStone;20</gift>
			<gift>things;deviceChest;5</gift>
			<gift>things;rebirthStone;2</gift>
			<gift>things;highArmsEchelonCard;5</gift>
			<gift>things;highEquipEchelonCard;5</gift>
			<gift>things;armsTitanium;20</gift>
			<gift>things;armsRadium;20</gift>
			<gift>things;allBlackCash;20</gift>
			<gift>things;allBlackEquipCash;20</gift>
			<gift>things;oracleSuit_belt;10</gift>
		</things>
		
		
		<things cnName="儿童节全民礼包">
			<name>normalChildrenGift</name>
			<cnName>儿童节全民礼包</cnName>
			<iconUrl>ThingsIcon/normalGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店银币购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;shotgunBlade;2</gift>
			<gift>things;FlyDragonGun;2</gift>
			<gift>things;normalChest;1</gift>
			<gift>things;converStone;80</gift>
			<gift>things;strengthenStone;50</gift>
			<gift>things;rebirthStone;1</gift>
		</things>
		<things cnName="儿童节豪华礼包">
			<name>goldChildrenGift</name>
			<cnName>儿童节豪华礼包</cnName>
			<iconUrl>ThingsIcon/goldGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店黄金购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;rifleHornet;2</gift>
			<gift>things;shotgunBlade;3</gift>
			<gift>things;FlyDragonGun;3</gift>
			<gift>things;magicChest;1</gift>
			<gift>things;strengthenStone;80</gift>
			<gift>things;rebirthStone;2</gift>
		</things>
		
		
		
		
		<things cnName="六一全民礼包2017">
			<name>normalChildrenGift2017</name>
			<cnName>六一全民礼包</cnName>
			<iconUrl>ThingsIcon/normalGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店银币购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;strengthenStone;30</gift>
			<gift>things;normalChest;1</gift>
			<gift>things;doubleHighCard;2</gift>
			<gift>things;allBlackCash;10</gift>
			<gift>things;allBlackEquipCash;10</gift>

		</things>
		<things cnName="儿童节豪华礼包2017">
			<name>goldChildrenGift2017</name>
			<cnName>六一节豪华礼包</cnName>
			<iconUrl>ThingsIcon/goldGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店黄金购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;bloodStone;20</gift>
			<gift>things;deviceChest;5</gift>
			<gift>things;highArmsEchelonCard;5</gift>
			<gift>things;highEquipEchelonCard;5</gift>
			<gift>things;armsTitanium;20</gift>
			<gift>things;armsRadium;20</gift>
			<gift>things;allBlackCash;20</gift>
			<gift>things;allBlackEquipCash;20</gift>

		</things>
		
		
		
		
		<things cnName="开学礼包2016">
			<name>kaixueGift2016</name>
			<cnName>开学礼包</cnName>
			<iconUrl>ThingsIcon/normalGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店银币购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;magicChest;1</gift>
			<gift>things;duelistClaw_1;5</gift>
			<gift>things;knightsMedal_1;5</gift>
			<gift>things;WatchEagleAirCash;5</gift>
			<gift>things;rolling_heroChip;5</gift>
			<gift>things;godStone;100</gift>
			<gift>things;converStone;100</gift>
			<gift>things;bloodStone;50</gift>
			<gift>things;strengthenStone;50</gift>

		</things>
		
		<things cnName="国庆畅玩礼包">
			<name>normalNationalGift</name>
			<cnName>国庆畅玩礼包</cnName>
			<iconUrl>ThingsIcon/normalGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店银币购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;strengthenStone;5</gift>
			<gift>things;doubleCard;5</gift>
			<gift>things;armsTitanium;2</gift>
			<gift>things;armsRadium;2</gift>
			<gift>things;strengthenDrug;30</gift>
			<gift>things;lifeCatalyst;30</gift>
			<gift>things;energyCatalyst;30</gift>
			<gift>things;variationCatalyst;30</gift>
			<gift>things;keyChest;5</gift>
			<gift>things;magicChest;1</gift>
			<gift>things;allBlackCash;2</gift>
		</things>
		<things cnName="国庆豪华礼包">
			<name>goldNationalGift</name>
			<cnName>国庆豪华礼包</cnName>
			<iconUrl>ThingsIcon/goldGift</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店黄金购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;rebirthStone;1</gift>
			<gift>things;armsTitanium;5</gift>
			<gift>things;armsRadium;5</gift>
			<gift>things;strengthenStone;20</gift>
			<gift>things;bloodStone;20</gift>
			<gift>things;lightStone;10</gift>
			<gift>things;armsEchelonCard;20</gift>
			<gift>things;equipEchelonCard;20</gift>
			<gift>things;dragonChest;1</gift>
			<gift>things;allBlackCash;10</gift>
		</things>
		
		
		
		
		
		
		<things cnName="周年畅玩礼包">
			<name>normalAnniverGift</name>
			<cnName>周年畅玩礼包</cnName>
			<iconUrl>ThingsIcon/postNormalChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店银币购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;bloodStone;10</gift>
			<gift>things;doubleCard;5</gift>
			<gift>things;armsTitanium;2</gift>
			<gift>things;armsRadium;2</gift>
			<gift>things;lifeCatalyst;30</gift>
			<gift>things;energyCatalyst;30</gift>
			<gift>things;variationCatalyst;20</gift>
			<gift>things;erosionFlamer;2</gift>
			<gift>things;magicChest;1</gift>
		</things>
		<things cnName="周年豪华礼包">
			<name>goldAnniverGift</name>
			<cnName>周年豪华礼包</cnName>
			<iconUrl>ThingsIcon/postPlatinumChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店黄金购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;rebirthStone;1</gift>
			<gift>things;armsTitanium;5</gift>
			<gift>things;armsRadium;5</gift>
			<gift>things;strengthenStone;20</gift>
			<gift>things;bloodStone;30</gift>
			<gift>things;armsEchelonCard;20</gift>
			<gift>things;equipEchelonCard;20</gift>
			<gift>things;dragonChest;1</gift>
			<gift>things;erosionFlamer;10</gift>
			<gift>things;allBlackCash;10</gift>
		</things>
		
		
		
		<things cnName="开学畅玩礼包">
			<name>kaixueGift2017_1</name>
			<cnName>开学畅玩礼包</cnName>
			<iconUrl>ThingsIcon/postNormalChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店银币购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;doubleArmsCard;3</gift>
			<gift>things;allBlackCash;20</gift>
			<gift>things;godStone;100</gift>
			<gift>things;converStone;100</gift>
			<gift>things;bloodStone;50</gift>
			<gift>things;ultiArmsEchelonCard;5</gift>

		</things>
		<things cnName="开学豪华礼包">
			<name>kaixueGift2017_2</name>
			<cnName>开学豪华礼包</cnName>
			<iconUrl>ThingsIcon/postPlatinumChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店黄金购买
				[n][n]使用后将获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<useSave>shopAuto</useSave>
			<showGiftB>1</showGiftB>
			<gift>things;magicChest;1</gift>
			<gift>things;doubleArmsCard;5</gift>
			<gift>things;oracleSuit_belt;10</gift>
			<gift>things;oracleSuit_head;10</gift>
			<gift>things;allBlackCash;30</gift>
			<gift>things;godStone;200</gift>
			<gift>things;converStone;200</gift>
			<gift>things;bloodStone;60</gift>
			<gift>things;fineStone;10</gift>

		</things>
	</father>	
	
	<father name="props" cnName="普通武器箱" smeltType="box">
		<!-- 武器箱子 -->
		<things cnName="1级武器箱">
			<name>firstArmsBox</name>
			<cnName>1级武器箱</cnName>
			<zuobiNum>30000</zuobiNum>
			<iconUrl>ThingsIcon/firstArmsBox</iconUrl><!-- 技能图标 -->
			<description>使用后将随机获得一把1级的武器。
				[n][n]{font color='#FFFF00'}品质概率：{/font}
				[n]白色 25%
				[n]绿色 60%
				[n]蓝色 12%
				[n]紫色 2.6%
				[n]橙色 0.34%
				[n]红色 0.006%
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;dropLv;1;;1;;;;normalBox</gift>
			<loveD num="20"/>
			<smeltD grade="-1"/>
		</things>
		
		<things cnName="普通武器箱">
			<name>armsBox</name>
			<cnName>普通武器箱</cnName>
			<zuobiNum>30000</zuobiNum>
			<iconUrl>ThingsIcon/armsBox</iconUrl><!-- 技能图标 -->
			<description>使用后将随机获得一把武器，等级为(人物等级)~(人物等级-7)之间。
				[n][n]{font color='#FFFF00'}品质概率：{/font}
				[n]白色 25%
				[n]绿色 60%
				[n]蓝色 12%
				[n]紫色 2.6%
				[n]橙色 0.34%
				[n]红色 0.006%
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;drop;1;;;;;;normalBox</gift>
			<loveD num="20"/>
			<smeltD grade="0" addType="" price="0.1"/>
		</things>
		<things cnName="高级武器箱">
			<name>armsHighBox</name>
			<cnName>高级武器箱</cnName>
			<zuobiNum>30000</zuobiNum>
			<iconUrl>ThingsIcon/armsHighBox</iconUrl><!-- 技能图标 -->
			<description>使用后将随机获得一把武器，等级为(人物等级)~(人物等级-6)之间。
				[n][n]{font color='#FFFF00'}品质概率：{/font}
				[n]蓝色 83%
				[n]紫色 13%
				[n]橙色 3%
				[n]红色 1%
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;dropBoss;1;;;;;;normalBox</gift><!-- "type", "name", "num", "color", "lv", "childType", "numExtra","tipB","dropName" -->
			<loveD num="10"/>
			<smeltD grade="0" addType="" price="0.5"/>
		</things>
		<things cnName="高级步枪箱子">
			<name>rifleBox</name>
			<cnName>高级步枪箱子</cnName>
			<zuobiNum>30000</zuobiNum>
			<iconUrl>ThingsIcon/rifleBox</iconUrl><!-- 技能图标 -->
			<description>使用后将随机获得一把步枪，等级为(人物等级)~(人物等级-6)之间。
				[n][n]{font color='#FFFF00'}品质概率：{/font}
				[n]蓝色 83%
				[n]紫色 13%
				[n]橙色 3%
				[n]红色 1%
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;dropBoss;1;0;;rifle;;;normalBox</gift>
			<loveD num="10"/>
			<smeltD grade="0" addType="" price="0.8"/>
		</things>
		<things cnName="高级狙击箱子">
			<name>sniperBox</name>
			<cnName>高级狙击箱子</cnName>
			<zuobiNum>30000</zuobiNum>
			<iconUrl>ThingsIcon/sniperBox</iconUrl><!-- 技能图标 -->
			<description>使用后将随机获得一把狙击枪，等级为(人物等级)~(人物等级-6)之间。
				[n][n]{font color='#FFFF00'}品质概率：{/font}
				[n]蓝色 83%
				[n]紫色 13%
				[n]橙色 3%
				[n]红色 1%
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;dropBoss;1;0;;sniper;;;normalBox</gift>
			<loveD num="10"/>
			<smeltD grade="0" addType="" price="0.8"/>
		</things>
		<things cnName="高级散弹箱子">
			<name>shotgunBox</name>
			<cnName>高级散弹箱子</cnName>
			<zuobiNum>30000</zuobiNum>
			<iconUrl>ThingsIcon/shotgunBox</iconUrl><!-- 技能图标 -->
			<description>使用后将随机获得一把散弹枪，等级为(人物等级)~(人物等级-6)之间。
				[n][n]{font color='#FFFF00'}品质概率：{/font}
				[n]蓝色 83%
				[n]紫色 13%
				[n]橙色 3%
				[n]红色 1%
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;dropBoss;1;0;;shotgun;;;normalBox</gift>
			<loveD num="10"/>
			<smeltD grade="0" addType="" price="0.8"/>
		</things>
		<things cnName="高级手枪箱子">
			<name>pistolBox</name>
			<cnName>高级手枪箱子</cnName>
			<zuobiNum>30000</zuobiNum>
			<iconUrl>ThingsIcon/pistolBox</iconUrl><!-- 技能图标 -->
			<description>使用后将随机获得一把手枪，等级为(人物等级)~(人物等级-6)之间。
				[n][n]{font color='#FFFF00'}品质概率：{/font}
				[n]蓝色 83%
				[n]紫色 13%
				[n]橙色 3%
				[n]红色 1%
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;dropBoss;1;0;;pistol;;;normalBox</gift>
			<loveD num="10"/>
			<smeltD grade="0" addType="" price="0.8"/>
		</things>
	</father>	
	<father name="props" cnName="黄金武器箱" smeltType="box">	
		<![CDATA[
		<things cnName="特级武器箱">
			<name>armsSuperBox</name>
			<cnName>特级武器箱</cnName>
			<description>
				使用后将随机获得一把蓝色以上的武器（不包含火炮），{font color='#FFFF00'}等级为人物等级{/font}，获得各品质武器的概率如下：
				{font color='#FFFFFF'}{b}
				[n]{font color='#66FFFF'}蓝色武器{/font} 75%的概率
				[n]{font color='#FF66FF'}紫色武器{/font} 15%的概率
				[n]{font color='#FF6600'}橙色武器{/font} 10%的概率
				{/b}{/font}
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;superBox</gift>
		</things>
		<things cnName="特级步枪箱">
			<name>rifleSuperBox</name>
			<cnName>特级步枪箱</cnName>
			<description>
				使用后将随机获得一把蓝色以上的步枪，{font color='#FFFF00'}等级为人物等级{/font}，获得各品质武器的概率如下：
				{font color='#FFFFFF'}{b}
				[n]{font color='#66FFFF'}蓝色武器{/font} 75%的概率
				[n]{font color='#FF66FF'}紫色武器{/font} 15%的概率
				[n]{font color='#FF6600'}橙色武器{/font} 10%的概率
				{/b}{/font}
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;superBox;1;0;;rifle</gift>
		</things>
		<things cnName="特级狙击箱">
			<name>sniperSuperBox</name>
			<cnName>特级狙击箱</cnName>
			<description>
				使用后将随机获得一把蓝色以上的狙击枪，{font color='#FFFF00'}等级为人物等级{/font}，获得各品质武器的概率如下：
				{font color='#FFFFFF'}{b}
				[n]{font color='#66FFFF'}蓝色武器{/font} 75%的概率
				[n]{font color='#FF66FF'}紫色武器{/font} 15%的概率
				[n]{font color='#FF6600'}橙色武器{/font} 10%的概率
				{/b}{/font}
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;superBox;1;0;;sniper</gift>
		</things>
		<things cnName="特级散弹箱">
			<name>shotgunSuperBox</name>
			<cnName>特级散弹箱</cnName>
			<description>
				使用后将随机获得一把蓝色以上的散弹枪，{font color='#FFFF00'}等级为人物等级{/font}，获得各品质武器的概率如下：
				{font color='#FFFFFF'}{b}
				[n]{font color='#66FFFF'}蓝色武器{/font} 75%的概率
				[n]{font color='#FF66FF'}紫色武器{/font} 15%的概率
				[n]{font color='#FF6600'}橙色武器{/font} 10%的概率
				{/b}{/font}
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;superBox;1;0;;shotgun</gift>
		</things>
		<things cnName="特级手枪箱">
			<name>pistolSuperBox</name>
			<cnName>特级手枪箱</cnName>
			<description>
				使用后将随机获得一把蓝色以上的手枪，{font color='#FFFF00'}等级为人物等级{/font}，获得各品质武器的概率如下：
				{font color='#FFFFFF'}{b}
				[n]{font color='#66FFFF'}蓝色武器{/font} 75%的概率
				[n]{font color='#FF66FF'}紫色武器{/font} 15%的概率
				[n]{font color='#FF6600'}橙色武器{/font} 10%的概率
				{/b}{/font}
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;superBox;1;0;;pistol</gift>
		</things>
		<things cnName="特级火炮箱">
			<name>roketSuperBox</name>
			<cnName>特级火炮箱</cnName>
			<description>
				使用后将随机获得一把{font color='#FF66FF'}紫色以上{/font}的火炮，{font color='#FFFF00'}等级为人物等级{/font}，获得各品质武器的概率如下：
				{font color='#FFFFFF'}{b}
				[n]{font color='#FF66FF'}紫色武器{/font} 80%的概率
				[n]{font color='#FF6600'}橙色武器{/font} 20%的概率
				{/b}{/font}
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>arms;superBox;1;0;;rocket</gift>
		</things>
		]]>
	</father>
	<father name="props" cnName="装备箱" smeltType="box">
		<things cnName="普通装备箱">
			<name>equipBox</name>
			<cnName>普通装备箱</cnName>
			<zuobiNum>30000</zuobiNum>
			<iconUrl>ThingsIcon/equipBox</iconUrl><!-- 技能图标 -->
			<description>使用后将随机获得一件装备，等级为(人物等级)~(人物等级-7)之间。
				[n][n]{font color='#FFFF00'}品质概率：{/font}
				[n]白色 25%
				[n]绿色 60%
				[n]蓝色 12%
				[n]紫色 2.6%
				[n]橙色 0.34%
				[n]红色 0.006%
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>equip;drop;1;;;;;;normalBox</gift>
			<loveD num="20"/>
			<smeltD grade="0" addType="" price="0.1"/>
		</things>
		<things cnName="高级装备箱">
			<name>equipHighBox</name>
			<cnName>高级装备箱</cnName>
			<zuobiNum>30000</zuobiNum>
			<iconUrl>ThingsIcon/equipHighBox</iconUrl><!-- 技能图标 -->
			<description>使用后将随机获得一件装备，等级为(人物等级)~(人物等级-6)之间，出现高品质装备的概率为普通装备箱的10倍。
				[n][n]{font color='#FFFF00'}品质概率：{/font}
				[n]蓝色 83%
				[n]紫色 13%
				[n]橙色 3%
				[n]红色 1%
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>equip;dropBoss;1;;;;;;normalBox</gift>
			<loveD num="10"/>
			<smeltD grade="0" addType="" price="0.5"/>
		</things>
		<![CDATA[
		<things cnName="特级装备箱">
			<name>equipSuperBox</name>
			<cnName>特级装备箱</cnName>
			<iconUrl>ThingsIcon/equipSuperBox</iconUrl><!-- 技能图标 -->
			<description>
				使用后将随机获得一把蓝色以上的装备，{font color='#FFFF00'}等级为人物等级{/font}，获得各品质装备的概率如下：
				{font color='#FFFFFF'}{b}
				[n]{font color='#66FFFF'}蓝色装备{/font} 75%的概率
				[n]{font color='#FF66FF'}紫色装备{/font} 15%的概率
				[n]{font color='#FF6600'}橙色装备{/font} 10%的概率
				{/b}{/font}
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>equip;superBox</gift>
			<smeltD grade="0" addType=""/>
		</things>
		]]>
	</father>
	<father name="props" cnName="银币堆">	
		<things cnName="小型银币堆">
			<name>coinHeap_1</name>
			<cnName>小型银币堆</cnName>
			<zuobiNum>30000</zuobiNum>
			<iconUrl>ThingsIcon/coinHeap_1</iconUrl><!-- 技能图标 -->
			<description>使用后添加1万银币。</description>
			<effectD type="addGift"/>
			<gift>base;coin;10000</gift>
			<smeltD addType="coin" price="2" />
			</things>
		<things cnName="中型银币堆">
			<name>coinHeap_2</name>
			<cnName>中型银币堆</cnName>
			<zuobiNum>2000</zuobiNum>
			<iconUrl>ThingsIcon/coinHeap_2</iconUrl><!-- 技能图标 -->
			<description>使用后添加10万银币。</description>
			<effectD type="addGift"/>
			<gift>base;coin;100000</gift>
			<smeltD addType="coin" price="15" />
			</things>
		<things cnName="大型银币堆">
			<name>coinHeap_3</name>
			<cnName>大型银币堆</cnName>
			<zuobiNum>200</zuobiNum>
			<iconUrl>ThingsIcon/coinHeap_3</iconUrl><!-- 技能图标 -->
			<description>使用后添加100万银币。</description>
			<effectD type="addGift"/>
			<gift>base;coin;1000000</gift>
			<smeltD addType="coin" price="100" />
			</things>
	</father>
	<father name="props" cnName="双倍卡片">	
		<things cnName="双倍经验绿卡">
			<name>doubleCard</name>
			<cnName>双倍经验绿卡</cnName>
			<zuobiNum>2000</zuobiNum>
			<iconUrl>ThingsIcon/doubleCard</iconUrl><!-- 技能图标 -->
			<description>使用后获得20分钟的双倍经验时间。</description>
			<effectD type="addGift"/>
			<gift>time;doubleExpTime;1200</gift>
			<smeltD grade="0" addType="" price="5"/>
			</things>
		<things cnName="双倍经验金卡">
			<name>doubleHighCard</name>
			<zuobiNum>1000</zuobiNum>
			<cnName>双倍经验金卡</cnName>
			<iconUrl>ThingsIcon/doubleHighCard</iconUrl><!-- 技能图标 -->
			<description>使用后获得60分钟的双倍经验时间。</description>
			<effectD type="addGift"/>
			<gift>time;doubleExpTime;3600</gift>
			<smeltD grade="0" addType="" price="10"/>
			</things>
		
		
		<things cnName="双倍武器掉率卡">
			<name>doubleArmsCard</name>
			<cnName>双倍武器掉率卡</cnName>
			<zuobiNum>2000</zuobiNum>
			<iconUrl>ThingsIcon/doubleArmsCard</iconUrl><!-- 技能图标 -->
			<description>使用后获得20分钟的双倍武器掉率时间，武器包括橙色武器、红色武器、稀有武器、黑色武器碎片。</description>
			<effectD type="addGift"/>
			<gift>time;doubleArmsDropTime;1200</gift>
			<smeltD grade="1" addType="armsChip" price="10"/>
			<loveD num="0"/>
		</things>
		<things cnName="双倍装备掉率卡">
			<name>doubleEquipCard</name>
			<cnName>双倍装备掉率卡</cnName>
			<zuobiNum>2000</zuobiNum>
			<iconUrl>ThingsIcon/doubleEquipCard</iconUrl><!-- 技能图标 -->
			<description>使用后获得20分钟的双倍装备掉率时间，装备包括橙色装备、红色装备、稀有装备、黑色装备碎片。</description>
			<effectD type="addGift"/>
			<gift>time;doubleEquipDropTime;1200</gift>
			<smeltD grade="1" addType="equipChip" price="8"/>
			<loveD num="0"/>
		</things>
		<things cnName="双倍材料掉率卡">
			<name>doubleMaterialsCard</name>
			<cnName>双倍材料掉率卡</cnName>
			<zuobiNum>2000</zuobiNum>
			<iconUrl>ThingsIcon/doubleMaterialsCard</iconUrl><!-- 技能图标 -->
			<description>使用后获得15分钟的双倍材料掉率时间，材料包括神能石、转化石、生命催化剂、商券、血石。</description>
			<effectD type="addGift"/>
			<gift>time;doubleMaterialsDropTime;900</gift>
			<smeltD grade="1" addType="otherChip" price="5"/>
			<loveD num="0"/>
		</things>
	</father>
	<father name="props" cnName="一般箱子" smeltType="box">
		<things cnName="69级零件箱">
			<name>partsChest69</name>
			<cnName>69级零件箱</cnName>
			<iconUrl>ThingsIcon/partsChest69</iconUrl><!-- 技能图标 -->
			<description>
				使用后将获得6个69级的普通零件。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]锻造合成
				[n]商店、竞技场兑换、军队商店
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="4"/>
			<smeltD grade="0" addType="" price="10"/>
			<gift>parts;loaderParts_69;6</gift>
		</things>
		
		<things cnName="72级零件箱">
			<name>partsChest72</name>
			<cnName>72级零件箱</cnName>
			<iconUrl>ThingsIcon/partsChest72</iconUrl><!-- 技能图标 -->
			<description>
				使用后将获得6个72级的普通零件。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]vip每日礼包领取
				[n]每日任务
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="4"/>
			<smeltD grade="0" addType="" price="25"/>
			<gift>parts;loaderParts_72;6</gift>
		</things>
		
		<things cnName="75级零件箱">
			<name>partsChest75</name>
			<cnName>75级零件箱</cnName>
			<iconUrl>ThingsIcon/partsChest75</iconUrl><!-- 技能图标 -->
			<description>
				使用后将获得6个75级的普通零件。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]锻造合成
				[n]商店、军队商店
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="4"/>
			<smeltD grade="1" addType="" price="50"/>
			<gift>parts;loaderParts_75;6</gift>
		</things>
		<things cnName="78级零件箱">
			<name>partsChest78</name><addDropDefineB>1</addDropDefineB>
			<cnName>78级零件箱</cnName>
			<iconUrl>ThingsIcon/partsChest78</iconUrl><!-- 技能图标 -->
			<description>
				使用后将获得6个78级的普通零件。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店
				[n]每周任务
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="4"/>
			<smeltD grade="-1" addType="" price="150"/>
			<gift>parts;loaderParts_78;6</gift>
		</things>
		<things cnName="81级零件箱">
			<name>partsChest81</name>
			<cnName>81级零件箱</cnName>
			<iconUrl>ThingsIcon/partsChest81</iconUrl><!-- 技能图标 -->
			<description>
				使用后将获得6个81级的普通零件。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="4"/>
			<smeltD grade="-1" addType=""/>
			<gift>parts;loaderParts_81;6</gift>
		</things>
		<things cnName="84级零件箱">
			<name>partsChest84</name>
			<cnName>84级零件箱</cnName>
			<iconUrl>ThingsIcon/partsChest84</iconUrl><!-- 技能图标 -->
			<description>
				使用后将获得6个84级的普通零件。
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="4"/>
			<gift>parts;loaderParts_84;6</gift>
		</things>
		<things cnName="87级零件箱">
			<name>partsChest87</name>
			<cnName>87级零件箱</cnName>
			<iconUrl>ThingsIcon/partsChest87</iconUrl><!-- 技能图标 -->
			<description>
				使用后将获得6个87级的普通零件。
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="4"/>
			<gift>parts;loaderParts_87;6</gift>
		</things>
		<things cnName="90级零件箱">
			<name>partsChest90</name>
			<cnName>90级零件箱</cnName>
			<iconUrl>ThingsIcon/partsChest90</iconUrl><!-- 技能图标 -->
			<description>
				使用后将获得6个90级的普通零件。
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="4"/>
			<gift>parts;loaderParts_90;6</gift>
		</things>
		
		
		<things cnName="凯撒箱">
			<name>sprintSword20</name>
			<cnName>凯撒箱</cnName>
			<iconUrl>ThingsIcon/sprintSword20</iconUrl><!-- 技能图标 -->
			<description>
				使用后获得20把凯撒。[n]
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买
			</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="1"/>
			<smeltD grade="-1"/>
			<gift>equip;sprintSword_1;20</gift>
			
		</things>
		<things cnName="副手箱">
			<name>weaponChest</name>
			<cnName>副手箱</cnName>
			<iconUrl>ThingsIcon/weaponChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、每日任务“特种兵的赌约”（2星难度以上）。
				[n]2、通关后抽奖。
				[n]3、每日问答答题全对奖励。
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="4"/>
			<smeltD grade="1" addType="" price="15"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			<gift pro="1">equip;butcherBlade_1;1</gift>
			<gift pro="1">equip;moonDagger_1;1</gift>
			<gift pro="1">equip;wolongStick_1;1</gift>
			
		</things>
		<things cnName="装置箱">
			<name>deviceChest</name>
			<cnName>装置箱</cnName>
			<iconUrl>ThingsIcon/deviceChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、每日任务“古老的火炮”。
				[n]2、通关后抽奖。
				[n]3、每日问答答题全对奖励。
				[n]4、商店购买。
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="4"/>
			<smeltD grade="1" addType="" price="10"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			<gift pro="1">equip;earthquakeGenerator_1</gift>
			<gift pro="1">equip;endlessRocket_1</gift>
			<gift pro="1">equip;lightningTower_1</gift>
			<gift pro="1">equip;energyShield_1</gift>
			<gift pro="1">equip;electricDevicer_1</gift>
		</things>
		<things cnName="钥匙箱">
			<name>keyChest</name>
			<cnName>钥匙箱</cnName>
			<iconUrl>ThingsIcon/keyChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、每日任务“逃出升天”。
				[n]2、通关后抽奖。
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="3"/>
			<smeltD grade="0" addType="" price="2"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			<gift pro="1">things;dreamKey;1</gift>
			<gift pro="1">things;courageKey;1</gift>
			<gift pro="1">things;energyKey;1</gift>
			<gift pro="1">things;victoryKey;1</gift>
		</things>
		
		<things cnName="武器宝石箱">
			<name>armsGemChest</name>
			<cnName>武器宝石箱</cnName>
			<iconUrl>ThingsIcon/armsGemChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、副本任务“古飙”。
				[n]2、商店购买。
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="3"/>
			<smeltD grade="-1" addType="" price="3"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			<gift pro="28">things;fireGem;1</gift>
			<gift pro="28">things;electricGem;1</gift>
			<gift pro="28">things;frozenGem;1</gift>
			<gift pro="16">things;poisonGem;1</gift>
		</things>
		<things cnName="装备宝石箱">
			<name>equipGemChest</name>
			<cnName>装备宝石箱</cnName>
			<iconUrl>ThingsIcon/equipGemChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买。
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="3"/>
			<smeltD grade="-1" addType="" price="3"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			<gift pro="40">things;wisdomGem;1</gift>
			<gift pro="33">things;agileGem;1</gift>
			<gift pro="27">things;defenceGem;1</gift>
			<gift pro="33">things;alertGem;1</gift>
		</things>
		
	</father>	
	<father name="props" cnName="稀有宝箱" smeltType="box">
		<things cnName="稀有宝箱">
			<name>normalChest</name><addDropDefineB>1</addDropDefineB>
			<cnName>稀有宝箱</cnName>
			<iconUrl>ThingsIcon/normalChest</iconUrl><!-- 技能图标 -->
			<description>
				可在“锻造>合成>物品”中合成小卡、小隆、小娜、小田等时装。
				[n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、消灭财宝僵尸。
				[n]2、竞技场兑换、商店购买。
				[n]3、完成活跃任务领取活跃礼包。
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<smeltD grade="2" addType="armsEquip" price="40"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			<![CDATA[
			<gift pro="2.5">equip;xiaoNa</gift>
			<gift pro="5">equip;xiaoTian</gift>
			<gift pro="2.5">equip;xiaoKa</gift>
			<gift pro="5">equip;xiaoLong</gift>
			]]>
			<gift pro="2">arms;pistolSange</gift>
			<gift pro="2">arms;shotgunLightning</gift>
			<gift pro="4">arms;rocketHummer</gift>
			
			<gift pro="66">equip;earthquakeGenerator_1;8</gift>
			<gift pro="66">equip;endlessRocket_1;8</gift>
			<gift pro="66">equip;lightningTower_1;8</gift>
			<gift pro="66">equip;energyShield_1;8</gift>
			<gift pro="66">equip;electricDevicer_1;8</gift>
			
			<gift pro="250">things;skillStone;200</gift>
			<gift pro="250">things;godStone;100</gift>
			<gift pro="250">things;converStone;100</gift>
			<gift pro="250">things;taxStamp;200</gift>
			
			<gift pro="200">base;coin;800000</gift>
		</things>
		<things cnName="传奇宝箱">
			<name>magicChest</name><addDropDefineB>1</addDropDefineB>
			<cnName>传奇宝箱</cnName>
			<iconUrl>ThingsIcon/magicChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、每次消灭完首领将有一定几率出现财宝僵尸，关卡难度越高、关卡等级越高，出现财宝僵尸的概率就越高。消灭财宝僵尸将有一定几率掉落传奇宝箱。
				[n]2、竞技场兑换。
				[n]3、商店购买（每天限定次数）。
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<smeltD grade="2" addType="armsEquip" price="60"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			<gift pro="25" sp="0.003">equip;Thunder</gift><![CDATA[]]>
			<gift pro="45">arms;pistolRed</gift>
			<gift pro="45">arms;rifleYellow</gift>
			<gift pro="45">arms;sniperBlue</gift>
			<gift pro="45">arms;shotgunRed</gift>
			<gift pro="120">equip;butcherBlade_1;15</gift>
			<gift pro="120">equip;moonDagger_1;15</gift>
			<gift pro="120">equip;wolongStick_1;15</gift>
			<gift pro="1286">things;dreamKey;8</gift>
			<gift pro="1286">things;courageKey;8</gift>
			<gift pro="1286">things;energyKey;8</gift>
			<gift pro="1286">things;victoryKey;8</gift>
			<gift pro="667">things;strengthenStone;30</gift>
			<gift pro="1587" sp="0.158">things;arenaStamp;200</gift>
			<gift pro="750">things;bloodStone;30</gift>
			<gift pro="1295">things;converStone;150</gift>
		</things>
		
		
		<things cnName="远古宝箱">
			<name>dragonChest</name><addDropDefineB>1</addDropDefineB>
			<cnName>远古宝箱</cnName>
			<iconUrl>ThingsIcon/dragonChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、消灭50级以上普通关卡、秘境首领有几率掉落，每天最多掉落2个。
				[n]2、竞技场兑换、军队商店兑换。
				[n]3、商店购买（每天限定次数）。
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<smeltD grade="2" addType="armsEquip" price="60"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			
			<gift pro="6" sp="0.003">equip;FlyDragonAir;1</gift>
			<gift pro="50">things;rifleHornet;10</gift>
			<gift pro="167">things;pistolCabrite;10</gift>
			<gift pro="250">things;christmasGun;10</gift>
			<gift pro="333">things;anniversaryCash;20</gift>
			<gift pro="167">things;lightStone;40</gift>
			<gift pro="250">things;converStone;200</gift>
			<gift pro="250">things;bloodStone;30</gift>
			<gift pro="333">things;armsTitanium;20</gift>
			<gift pro="500">things;armsRadium;20</gift>
			
			<gift pro="100">things;partsChest81;1</gift>
			<gift pro="100">things;partsChest84;1</gift>
			<gift pro="20">things;partsChest87;1</gift>
			<gift pro="310" sp="0.108">things;yearPig;1</gift>

		</things>
		
		
		<things cnName="神器宝箱">
			<name>artifactChest</name>
			<cnName>神器宝箱</cnName>
			<iconUrl>ThingsIcon/artifactChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			
			
			<gift pro="700">things;ghostAxe;1</gift>
			<gift pro="100">things;allBlackCash;5</gift>
			<gift pro="100">things;allBlackEquipCash;5</gift>
			<gift pro="200">things;AircraftGunCash;5</gift>
			<gift pro="200">things;erosionFlamer;3</gift>
			<gift pro="200">things;beadCrossbow;3</gift>
			<gift pro="200">things;oracleSuit_head;3</gift>
			<gift pro="200">things;oracleSuit_belt;3</gift>
			<gift pro="200">things;oracleSuit_coat;3</gift>
			<gift pro="500">things;weaponChest;1</gift>
			<gift pro="500">things;deviceChest;1</gift>
			<gift pro="500">things;fineStone;1</gift>
			<gift pro="800">things;skillStone;30</gift>
			<gift pro="800">things;strengthenDrug;30</gift>
		</things>
		
		
		<things cnName="异虎宝箱">
			<name>tigerChest</name>
			<cnName>异虎宝箱</cnName>
			<iconUrl>ThingsIcon/tigerChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]活跃度礼包
				[n]商店购买（每天限定次数）
				[n][n]使用后获得以下所有物品：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="0"/>
			<showGiftB>1</showGiftB>
			
			<gift>things;SaberTigerCarCash;1</gift>
			<gift>base;coin;20000</gift>
			<gift>things;bloodStone;3</gift>
			<gift>things;strengthenStone;1</gift>
			<gift>things;allBlackCash;1</gift>
			<gift>things;allBlackEquipCash;2</gift>
			<gift>things;armsRadium;3</gift>
			<gift>things;armsTitanium;3</gift>
			<gift>things;redFire;1</gift>
			<gift>things;rocketCate;1</gift>
			<gift>things;meltFlamer;1</gift>
			<gift>things;pistolFox;1</gift>
		</things>
		<things cnName="竞技宝箱">
			<name>arenaChest</name>
			<cnName>竞技宝箱</cnName>
			<iconUrl>ThingsIcon/arenaChest</iconUrl><!-- 技能图标 -->
			<description>
				可在“锻造>合成>物品”中合成小鸣时装、小波时装、小艾时装、超能英雄称号、特种奇兵称号。
				[n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]首领工厂高难度首领
				[n]功勋牌兑换
				[n][n]使用后将随机获得以下物品中的1种</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="0"/>
			<giftRandomB>1</giftRandomB>
			
			<gift pro="200">things;yearDog;1</gift>
			<gift pro="200">things;yearMonkey;1</gift>
			<gift pro="200">things;yearSnake;1</gift>
			<gift pro="200">things;yearCattle;1</gift>
			<gift pro="200">things;yearPig;1</gift>
			<gift pro="200">things;yearHourse;1</gift>
			<gift pro="200">things;yearMouse;1</gift>
			<gift pro="200">things;yearRabbit;1</gift>
		</things>
		<things cnName="生肖碎片包">
			<name>zodiacBag</name>
			<cnName>生肖碎片包</cnName>
			<iconUrl>ThingsIcon/zodiacBag</iconUrl><!-- 技能图标 -->
			<description>
				包含6种生肖碎片
				[n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]活跃值礼包
				[n][n]使用后获得以下所有物品</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<showGiftB>1</showGiftB>
			<loveD num="0"/>
			
			<gift>things;yearMonkey;1</gift>
			<gift>things;yearPig;1</gift>
			<gift>things;yearRabbit;1</gift>
			<gift>things;yearMouse;1</gift>
			<gift>things;yearHourse;1</gift>
			<gift>things;yearCattle;1</gift>
		</things>
		
		<![CDATA[
		<things cnName="异虎宝箱">
			<name>tigerChest</name>
			<cnName>异虎宝箱</cnName>
			<iconUrl>ThingsIcon/tigerChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]活跃度礼包
				[n]商店购买（每天限定次数）
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			
			<gift pro="20" sp="0.002">equip;SaberTigerCar;1</gift>
			<gift pro="2000">things;SaberTigerCarCash;2</gift>
			<gift pro="2000">base;coin;100000</gift>
			<gift pro="2000">things;bloodStone;20</gift>
			<gift pro="2000">things;strengthenStone;20</gift>
			<gift pro="2000">things;allBlackCash;8</gift>
			<gift pro="2000">things;allBlackEquipCash;8</gift>
			<gift pro="1000">things;armsRadium;15</gift>
			<gift pro="1000">things;armsTitanium;15</gift>
			<gift pro="500">things;redFire;8</gift>
			<gift pro="500">things;rocketCate;8</gift>
			<gift pro="500">things;meltFlamer;8</gift>
			<gift pro="500">things;pistolFox;8</gift>
			<gift pro="500">things;partsChest81;1</gift>
			<gift pro="120">things;partsChest84;1</gift>
			<gift pro="40">things;partsChest87;1</gift>
		</things>
		<things cnName="竞技宝箱">
			<name>arenaChest</name>
			<cnName>竞技宝箱</cnName>
			<iconUrl>ThingsIcon/arenaChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]2021年暑期签到
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			
			<gift pro="200">things;yearDog;1</gift>
			<gift pro="200">things;yearMonkey;1</gift>
			<gift pro="200">things;yearSnake;1</gift>
			<gift pro="200">things;yearCattle;1</gift>
			<gift pro="200">things;yearPig;1</gift>
			<gift pro="200">things;yearHourse;1</gift>
			<gift pro="200">things;yearMouse;1</gift>
			<gift pro="200">things;yearRabbit;1</gift>
			<gift pro="20">head;specialSoldiers;1</gift>
			<gift pro="20">head;superHero;1</gift>
			<gift pro="40">equip;xiaoAi;1</gift>
			<gift pro="20">equip;xiaoBo;1</gift>
			<gift pro="10">equip;xiaoMing;1</gift>
		</things>
		]]>
		
		
		
		
		
		<things cnName="联邦工资包">
			<name>postNormalChest</name>
			<cnName>联邦工资包</cnName>
			<iconUrl>ThingsIcon/postNormalChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、职务系统中的每日礼包。
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			
			<gift pro="240">things;armsEchelonCard;5</gift>
			<gift pro="240">things;equipEchelonCard;5</gift>
			<gift pro="100">things;strengthenStone;5</gift>
			<gift pro="100">things;lightStone;5</gift>
			<gift pro="33">things;militarySupplies;50</gift>
			<gift pro="33">things;normalChest;1</gift>
			<gift pro="33">things;magicChest;1</gift>
			<gift pro="218">things;coinHeap_1;5</gift>
		</things>
		<things cnName="元首礼包">
			<name>postPlatinumChest</name>
			<cnName>元首礼包</cnName>
			<iconUrl>ThingsIcon/postPlatinumChest</iconUrl><!-- 技能图标 -->
			<description>
				{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、职务系统中的每日礼包。
				[n][n]使用后将随机获得以下物品中的1种：</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<loveD num="2"/>
			<giftRandomB>1</giftRandomB><!-- 随机物品，根据pro概率 -->
			
			<gift pro="120">things;doubleArmsCard;1</gift>
			<gift pro="160">things;doubleEquipCard;1</gift>
			<gift pro="120">things;doubleMaterialsCard;1</gift>
			<gift pro="300">things;coinHeap_1;5</gift>
			
		</things>
	</father>
	
	
	
	
	
	
	
	<father name="materials" cnName="活动物品" smeltType="festival">
		<things cnName="抢楼券">
			<name>occupyStamp</name>
			<cnName>抢楼券</cnName>
			<itemsLevel>999</itemsLevel>
			<iconUrl>ThingsIcon/occupyStamp</iconUrl><!-- 技能图标 -->
			<description>
				全民抢楼活动所需物品。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]消灭普通关卡首领必然掉落
			</description>
			<loveD num="4"/>
		</things>
		<!-- 情人节 -->
		<things cnName="白色玫瑰">
			<name>whiteRose</name>
			<cnName>白色玫瑰</cnName>
			<zuobiNum>5000</zuobiNum>
			<iconUrl>ThingsIcon/whiteRose</iconUrl><!-- 技能图标 -->
			<description>把它赠送给小樱将额外200点好感度。</description>
			<effectD type="addLove" value="200"/>
			<loveD num="1"/>
			<smeltD grade="1" addType="" price="20"/>
		</things>
		<things cnName="百合花">
			<name>lily</name>
			<cnName>百合花</cnName>
			<zuobiNum>5000</zuobiNum>
			<iconUrl>ThingsIcon/lily</iconUrl><!-- 技能图标 -->
			<description>
				开启心零支线任务的必需品。把它赠送给心零将获得180点左右的好感度。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店
				[n]纪念币商店
				[n]竞技场兑换
			</description>
			<effectD type="addLove" value="170"/>
			<loveD num="1"/>
			<smeltD grade="-1" price="20"/>
		</things>
		<things cnName="玩具熊">
			<name>teddyBear</name>
			<cnName>玩具熊</cnName>
			<zuobiNum>5000</zuobiNum>
			<iconUrl>ThingsIcon/teddyBear</iconUrl><!-- 技能图标 -->
			<description>
				开启小美支线任务的必需品。把它赠送给小美将获得200点左右的好感度。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]十年币兑换
				[n]商店购买
			</description>
			<effectD type="addLove" value="190"/>
			<loveD num="1"/>
			<smeltD grade="-1" price="20"/>
		</things>
		
		<things cnName="粽子测试">
			<name>zongzi</name>
			<cnName>粽子测试</cnName>
			<zuobiNum>5000</zuobiNum>
			<iconUrl>ThingsIcon/zongzi</iconUrl><!-- 技能图标 -->
			<description>在商店中用于兑换商品。</description>
			<loveD num="1"/>
		</things>
		
	</father>	
	<father name="props" cnName="活动道具" smeltType="festival">
		<things cnName="月饼">
			<name>moonCake</name><addDropDefineB>1</addDropDefineB>
			<cnName>月饼</cnName>
			<zuobiNum>5000</zuobiNum>
			<iconUrl>ThingsIcon/moonCake</iconUrl><!-- 技能图标 -->
			<stateD skill="moonCake" time="300" gamingB="1"  demNoneB="1"/>
			<description>使用后将增加战斗中100%的攻击力（99级主线任务、修罗模式无效），持续5分钟。</description> 
			<loveD num="0"/>
			<smeltD grade="-1" addType="" price="20" maxNum="1" />
		</things>
		<things cnName="汤圆">
			<name>tangyuan</name><addDropDefineB>1</addDropDefineB>
			<cnName>汤圆</cnName>
			<zuobiNum>1000</zuobiNum>
			<iconUrl>ThingsIcon/tangyuan</iconUrl><!-- 技能图标 -->
			<stateD skill="tangyuan" time="120" gamingB="1"  demNoneB="1"/>
			<description>使用后将增加战斗中100%的攻击力（99级主线任务、修罗模式无效），持续2分钟。</description> 
			<loveD num="0"/>
			<smeltD grade="-1" addType="" price="10"  maxNum="1"/>
		</things>
		<things cnName="饺子">
			<name>jiaozi</name><addDropDefineB>1</addDropDefineB>
			<cnName>饺子</cnName>
			<zuobiNum>1000</zuobiNum>
			<iconUrl>ThingsIcon/jiaozi</iconUrl><!-- 技能图标 -->
			<stateD skill="jiaozi" time="60" gamingB="1"  demNoneB="1"/>
			<description>使用后在战斗中将处于无敌状态（99级主线任务、修罗模式无效），持续1分钟。</description> 
			<loveD num="0"/>
			<smeltD grade="-1" addType="" price="10"  maxNum="1"/>
		</things>
		
		
		<things cnName="树叶">
			<name>leafArbor</name><addDropDefineB>1</addDropDefineB>
			<cnName>树叶</cnName>
			<zuobiNum>1000</zuobiNum>
			<iconUrl>ThingsIcon/leafArbor</iconUrl><!-- 技能图标 -->
			<stateD skill="armsDropDoubleAndGem" time="1800" gamingB="1" />
			<description>使用后增加100%的稀有武器、黑色武器掉率，增加30%的元素宝石掉落，持续30分钟。</description> 
			<loveD num="0"/>
			<smeltD grade="-1" addType="" price="20"  maxNum="1"/>
		</things>
		<things cnName="种子">
			<name>seedArbor</name><addDropDefineB>1</addDropDefineB>
			<cnName>种子</cnName>
			<zuobiNum>1000</zuobiNum>
			<iconUrl>ThingsIcon/seedArbor</iconUrl><!-- 技能图标 -->
			<stateD skill="equipDropDoubleAndEquipGem" time="1800" gamingB="1" />
			<description>使用后增加100%的稀有装备、黑色装备掉率，增加30%的装备宝石掉落，持续30分钟。</description> 
			<loveD num="0"/>
			<smeltD grade="-1" addType="" price="20"  maxNum="1"/>
		</things>
		
		<things cnName="尸宠基因盒">
			<name>geneBox</name><addDropDefineB>1</addDropDefineB>
			<cnName>尸宠基因盒</cnName>
			<zuobiNum>1000</zuobiNum>
			<iconUrl>ThingsIcon/geneBox</iconUrl><!-- 技能图标 -->
			<description>使用后将随机获得一个基因体（不包括商店出售的基因体），品质在紫色到红色之间。</description>
			<effectD type="addGift" useSurplusBagB="1" />
			<gift>gene;box;1;</gift>
			<loveD num="1"/>
			<smeltD price="30" />
			</things>
		
	</father>
	
	<father name="materials" cnName="道具" smeltType="coin">
		<things cnName="时空令牌">
			<name>spaceToken</name>
			<cnName>时空令牌</cnName>
			<iconUrl>ThingsIcon/spaceToken</iconUrl><!-- 技能图标 -->
			<description>
				该物品可让你在关卡中随机召唤出一位未出战的队友，每次使用消耗1个时空令牌。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买
			</description>
			<loveD num="15"/>
			<effectD levelUseLimitNum="1" />
			<smeltD grade="0" addType="" price="5"/>
		</things>
		<things cnName="灵魂芯片">
			<name>soulChip</name>
			<cnName>灵魂芯片</cnName>
			<iconUrl>ThingsIcon/soulChip</iconUrl><!-- 技能图标 -->
			<description>
				功能暂未开放。
			</description>
			<loveD num="15"/>
		</things>
		
		<things index="-1" cnName="摩卡令牌">
			<name>mochaToken</name><addDropDefineB>1</addDropDefineB>
			<cnName>摩卡令牌</cnName>
			<iconUrl>ThingsIcon/mochaToken</iconUrl><!-- 技能图标 -->
			
			<description>
				点击自动使用，则玩家在进入普通关卡（不包含修罗模式）时，会自动消耗1张摩卡令牌，同时召唤摩卡参加战斗。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]秘境“异角龙”
			</description>
			<effectD type="skill" skillArr="addMocha" autoUseCondition="startLevel" endlessAutoUseOneB="1" />
			<loveD num="15"/>
			<smeltD grade="1" addType="" price="10"/>
		</things>
	</father>	
	<father name="materials" cnName="金币道具">	
		<things cnName="武器守护卡">
			<name>armsEchelonCard</name>
			<cnName>武器守护卡</cnName><addDropDefineB>1</addDropDefineB>
			<iconUrl>ThingsIcon/armsEchelonCard</iconUrl><!-- 技能图标 -->
			<description>
				强化武器的保级道具，最高保级14级。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]活跃度礼包
				[n]优胜券兑换
				[n]功勋牌兑换
				[n]纪念币兑换
				[n]商店购买
			</description>
			<smeltD addType="armsChip" price="1" />
		</things>
		<things cnName="装备守护卡">
			<name>equipEchelonCard</name>
			<cnName>装备守护卡</cnName><addDropDefineB>1</addDropDefineB>
			<iconUrl>ThingsIcon/equipEchelonCard</iconUrl><!-- 技能图标 -->
			<description>
				强化装备的保级道具，最高保级14级。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买
			</description>
			<smeltD addType="equipChip" price="1" />
		</things>
		<things cnName="高级装备守护卡">
			<name>highEquipEchelonCard</name>
			<cnName>高级装备守护卡</cnName><addDropDefineB>1</addDropDefineB>
			<iconUrl>ThingsIcon/highEquipEchelonCard</iconUrl><!-- 技能图标 -->
			<description>
				强化装备的保级道具，最高保级20级。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]装备守护卡合成
				[n]商店购买
			</description>
			<smeltD addType="equipChip" price="10"  maxNum="2"/>
		</things>
		<things cnName="高级武器守护卡">
			<name>highArmsEchelonCard</name>
			<cnName>高级武器守护卡</cnName><addDropDefineB>1</addDropDefineB>
			<iconUrl>ThingsIcon/highArmsEchelonCard</iconUrl><!-- 技能图标 -->
			<description>
				强化武器的保级道具，最高保级20级。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]武器守护卡合成
				[n]商店购买
			</description>
			<smeltD  addType="armsChip" price="10" maxNum="2" />
		</things>
	</father>	
	<father name="materials" cnName="月卡">	
		<!-- 黄金商品 -->    
		<things cnName="联邦总统">
			<name>platinumMonthCard</name>
			<cnName>白金月卡</cnName>
			<iconUrl>ThingsIcon/platinumMonthCard</iconUrl><!-- 技能图标 -->
			<description>。</description>
		</things>
		<things cnName="联邦总统3天">
			<name>platinumMonthCard3</name>
			<cnName>白金月卡3天</cnName>
			<iconUrl>ThingsIcon/platinumMonthCard3</iconUrl><!-- 技能图标 -->
			<description>。</description>
		</things>
		<things cnName="联邦国务卿">
			<name>goldMonthCard</name>
			<cnName>黄金月卡</cnName>
			<iconUrl>ThingsIcon/goldMonthCard</iconUrl><!-- 技能图标 -->
			<description>。</description>
		</things>
		<things cnName="联邦部长">
			<name>normalMonthCard</name>
			<cnName>普通月卡</cnName>
			<iconUrl>ThingsIcon/normalMonthCard</iconUrl><!-- 技能图标 -->
			<description>。</description>
		</things>
		
	</father>	
	<father name="materials" cnName="石头消耗品" smeltType="stone">
		<things cnName="超能石">
			<name>skillStone</name>
			<cnName>超能石</cnName>
			<iconUrl>ThingsIcon/skillStone</iconUrl><!-- 技能图标 -->
			<description>
				学习和升级技能所需的石头。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]消灭怪物掉落
				[n]商店购买、竞技场兑换
			</description>
			<loveD num="3" growB="1"/>
			<smeltD grade="0" addType="" price="0.1" />
		</things>
		<things cnName="神能石">
			<name>godStone</name>
			<cnName>神能石</cnName>
			<iconUrl>ThingsIcon/godStone</iconUrl><!-- 技能图标 -->
			<description>
				学习和升级技能所需的稀有石头，同时还可以用于重造武器、替换尸宠资质、升级载具。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]消灭怪物掉落
				[n]商店购买、竞技场兑换
			</description>
			<loveD num="1" growB="1"/>
			<smeltD grade="0" addType="" price="0.4"/>
		</things>
		<things cnName="光能石">
			<name>lightStone</name><addDropDefineB>1</addDropDefineB>
			<cnName>光能石</cnName>
			<iconUrl>ThingsIcon/lightStone</iconUrl><!-- 技能图标 -->
			<description>
				学习和升级技能高等级所需的稀有石头。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]锻造合成所得
				[n]商店购买、竞技场兑换
			</description>
			<smeltD grade="1" addType="" price="3"/>
		</things>
		<things cnName="核能石">
			<name>nuclearStone</name>
			<cnName>核能石</cnName>
			<iconUrl>ThingsIcon/nuclearStone</iconUrl><!-- 技能图标 -->
			<loveD num="0"/>
			<description>
				升级载具技能至最高级所需的稀有石头。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买
			</description>
		</things>
		
		
		
		<things cnName="转化石">
			<name>converStone</name>
			<cnName>转化石</cnName>
			<iconUrl>ThingsIcon/converStone</iconUrl><!-- 技能图标 -->
			<description>
				可用于转化生成套装、升级武器和装备、升级尸宠资质的稀有石头。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]消灭怪物掉落
				[n]商店购买、竞技场兑换
			</description>
			<loveD num="1" growB="1"/>
			<smeltD grade="0" addType="" price="0.4"/>
		</things>
		<things cnName="强化石">
			<name>strengthenStone</name><addDropDefineB>1</addDropDefineB>
			<cnName>强化石</cnName>
			<iconUrl>ThingsIcon/strengthenStone</iconUrl><!-- 技能图标 -->
			<description>
				可用于强化装备、武器。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、活跃礼包领取
				[n]2、每日任务“银币追逐者”
				[n]3、寻宝任务“强化石宝藏”
				[n]4、商店购买、竞技场兑换
			</description>
			<loveD num="10"/>
			<smeltD grade="0" addType="" price="1.5"/>
		</things>
		<things cnName="血石">
			<name>bloodStone</name><addDropDefineB>1</addDropDefineB>
			<cnName>血石</cnName>
			<iconUrl>ThingsIcon/bloodStone</iconUrl><!-- 技能图标 -->
			<description>
				可用于培养尸宠的资质、强化载具。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]签到礼包领取
				[n]消灭50级以上的首领掉落
				[n]商店购买、竞技场兑换
				[n]寻宝任务“血石宝藏”
			</description>
			<loveD num="10"/>
			<smeltD grade="1" addType="" price="2"/>
		</things>
		<things cnName="精石">
			<name>fineStone</name><addDropDefineB>1</addDropDefineB>
			<cnName>精石</cnName>
			<iconUrl>ThingsIcon/fineStone</iconUrl><!-- 技能图标 -->
			<description>
				可用于强化载具。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]秘境掉落，8月14日截止
				[n]血石兑换
				[n]商店购买
			</description>
			<loveD num="10"/>
			<smeltD grade="1" addType="" price="15"/>
		</things>
		
		
		
		<things cnName="钛晶">
			<name>armsTitanium</name>
			<cnName>钛晶</cnName>
			<iconUrl>ThingsIcon/armsTitanium</iconUrl><!-- 技能图标 -->
			<description>
				进化武器所需材料。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]提炼黑色碎片
				[n]提炼80级以上黑色、稀有武器装备
				[n]商店购买
				[n]竞技场兑换
				[n]功勋牌兑换
			</description>
			<loveD num="1" growB="1"/>
			<smeltD type="armsEquip" grade="1" addType="" price="3"/>
		</things>
		<things cnName="镭晶">
			<name>armsRadium</name><addDropDefineB>1</addDropDefineB>
			<cnName>镭晶</cnName>
			<iconUrl>ThingsIcon/armsRadium</iconUrl><!-- 技能图标 -->
			<description>
				进化武器所需材料。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]竞技场兑换
				[n]功勋牌兑换
				[n]每日签到、活跃礼包
				[n]商店购买
			</description>
			<loveD num="1" growB="1"/>
			<smeltD type="armsEquip" grade="1" addType="" price="2"/>
		</things>
		
		
		
	</father>
	<father name="materials" cnName="其他消耗品">
		
		<things cnName="强化剂">
			<name>strengthenDrug</name>
			<cnName>强化剂</cnName><addDropDefineB>1</addDropDefineB>
			<zuobiNum>10000000</zuobiNum>
			<iconUrl>ThingsIcon/strengthenDrug</iconUrl><!-- 技能图标 -->
			<description>
				用于强化尸宠的战斗力、生命值、头部防御，还可用于直接升级尸宠等级。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]分解基因体
				[n]商店购买
			</description>
			<loveD num="30"/>
			<smeltD type="pet" grade="0" addType="" price="0.2"/>
		</things>
		<things cnName="战宠号角">
			<name>petSuppleCard</name>
			<cnName>战宠号角</cnName><addDropDefineB>1</addDropDefineB>
			<zuobiNum>100000</zuobiNum>
			<iconUrl>ThingsIcon/petSuppleCard</iconUrl><!-- 技能图标 -->
			<description>
				用于获得宠物的替补功能。放生宠物后，系统也会返还一定的战宠号角。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]秘境
			</description>
			<loveD num="10"/>
			<smeltD type="pet" grade="1" addType="" price="10"/>
		</things>
		
		
		<things cnName="生命催化剂">
			<name>lifeCatalyst</name>
			<cnName>生命催化剂</cnName>
			<iconUrl>ThingsIcon/lifeCatalyst</iconUrl><!-- 技能图标 -->
			<description>
				用于进化尸宠。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、商店购买
				[n]2、消灭75级以上怪物掉落，掉率与转化石相同
			</description>
			<loveD num="8"/>
			<smeltD type="pet" grade="1" addType="" price="1"/>
		</things>
		<things cnName="能量催化剂">
			<name>energyCatalyst</name>
			<cnName>能量催化剂</cnName>
			<iconUrl>ThingsIcon/energyCatalyst</iconUrl><!-- 技能图标 -->
			<description>
				用于进化尸宠。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、商店购买
				[n]2、活跃礼包领取
			</description>
			<loveD num="8"/>
			<smeltD type="pet" grade="1" addType="" price="1"/>
		</things>
		<things cnName="变异催化剂">
			<name>variationCatalyst</name>
			<cnName>变异催化剂</cnName>
			<iconUrl>ThingsIcon/variationCatalyst</iconUrl><!-- 技能图标 -->
			<description>
				用于进化尸宠。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、商店购买
				[n]2、竞技场商店兑换
			</description>
			<loveD num="8"/>
			<smeltD type="pet" grade="1" addType="" price="1"/>
		</things>
	</father>	
	<father name="materials" cnName="兑换币" smeltType="currency">
		<things cnName="优胜券">
			<name>arenaStamp</name>
			<cnName>优胜券</cnName>
			<zuobiNum>400000</zuobiNum>
			<iconUrl>ThingsIcon/arenaStamp</iconUrl><!-- 技能图标 -->
			<description>
				可用来兑换极为稀有的竞技场奖励，在竞技场的“奖励兑换”界面中使用。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]挑战竞技场可获得优胜券
			</description>
			<loveD num="100"/>
			<smeltD price="0.02" />
		</things>
		<things cnName="军队功勋牌">
			<name>exploitCards</name>
			<cnName>军队功勋牌</cnName>
			<iconUrl>ThingsIcon/exploitCards</iconUrl><!-- 技能图标 -->
			<description>
				可在军队功勋商店里兑换指定商品。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、在军队中进行黄金捐献
				[n]2、完成活跃任务领取活跃礼包
			</description>
			<smeltD price="10" />
		</things>
		<things cnName="军备物资">
			<name>militarySupplies</name>
			<cnName>军备物资</cnName>
			<iconUrl>ThingsIcon/militarySupplies</iconUrl><!-- 技能图标 -->
			<description>
				用于升级军队中守望者的饲养等级、联邦大厦的权利等级、炊事馆的食用等级。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]军队每日福利礼包
				[n]军队建筑“联邦大厦”中获得
				[n]通过军队功勋牌兑换获得
				[n]商店购买（限购）
			</description>
			<loveD num="20"/>
			<smeltD price="0.1"/>
		</things>
		<things cnName="商券">
			<name>taxStamp</name>
			<cnName>商券</cnName>
			<iconUrl>ThingsIcon/taxStamp</iconUrl><!-- 技能图标 -->
			<description>
				想要神秘商人那购买物品，你必须缴纳对应数量的商券。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]寻宝任务奖励
				[n]30级以上怪物掉落
				[n]商店购买
			</description>
			<loveD num="2" growB="1"/>
			<smeltD price="0.3" />
		</things>
	</father>
	<father name="materials" cnName="载具碎片" smeltType="vehicle">
		<things cnName="沙漠进袭者碎片">
			<name>DesertTankCash</name>
			<cnName>沙漠进袭者碎片</cnName>
			<iconUrl>ThingsIcon/DesertTankCash</iconUrl><!-- 技能图标 -->
			<description>
				用于合成和进化载具“沙漠进袭者”。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买。
				[n]副本任务“狂刃之尸”奖励和掉落。
			</description>
			<loveD num="20"/>
			<smeltD grade="0" addType="" price="2"/>
		</things>
		
		<things cnName="破晓碎片">
			<name>DaybreakCash</name>
			<cnName>破晓碎片</cnName>
			<iconUrl>ThingsIcon/DaybreakCash</iconUrl><!-- 技能图标 -->
			<description>
				用于合成和进化载具“破晓”。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买。
				[n]副本任务“幽灵毒气弹”奖励和掉落。
			</description>
			<loveD num="20"/>
			<smeltD grade="0" addType="" price="2"/>
		</things>
		<things cnName="收割者碎片">
			<name>RedReaperCash</name>
			<cnName>收割者碎片</cnName>
			<iconUrl>ThingsIcon/RedReaperCash</iconUrl><!-- 技能图标 -->
			<description>
				用于合成和进化载具“收割者”。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买。
				[n]副本任务“酸雨城”奖励和掉落。
			</description>
			<loveD num="20"/>
			<smeltD grade="0" addType="" price="2"/>
		</things>
		<things cnName="潜行者碎片">
			<name>SeaSharkCash</name>
			<cnName>潜行者碎片</cnName>
			<iconUrl>ThingsIcon/SeaSharkCash</iconUrl><!-- 技能图标 -->
			<description>
				用于合成和进化载具“潜行者”。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买。
				[n]副本任务“眩晕之锤”奖励和掉落。
			</description>
			<loveD num="20"/>
			<smeltD grade="0" addType="" price="2"/>
		</things>
		<things cnName="巨鲸碎片">
			<name>BlueWhaleCash</name>
			<cnName>巨鲸碎片</cnName>
			<iconUrl>ThingsIcon/BlueWhaleCash</iconUrl><!-- 技能图标 -->
			<description>
				用于合成和进化载具“巨鲸”。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买。
				[n]副本任务“克隆之魂”奖励和掉落。
			</description>
			<loveD num="20"/>
			<smeltD grade="0" addType="" price="2"/>
		</things>
		
		<!-- 黄金载具 -->
		<things cnName="先知碎片">
			<name>ProphetCash</name>
			<cnName>先知碎片</cnName>
			<iconUrl>ThingsIcon/ProphetCash</iconUrl><!-- 技能图标 -->
			<description>
				进化载具“先知”所需的物品。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买。
				[n]副本任务“钢铁之躯”奖励和掉落。
			</description>
			<loveD num="20"/>
			<smeltD grade="2" addType="" price="3"/>
		</things>
		<things cnName="制裁者碎片">
			<name>PunisherCash</name>
			<cnName>制裁者碎片</cnName>
			<iconUrl>ThingsIcon/PunisherCash</iconUrl><!-- 技能图标 -->
			<description>
				进化载具“制裁者”所需的物品。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买。
				[n]副本任务“飓风力量”奖励和掉落。
			</description>
			<loveD num="20"/>
			<smeltD grade="2" addType="" price="3"/>
		</things>
		<things cnName="挖掘者碎片">
			<name>DiggersCash</name><addDropDefineB>1</addDropDefineB>
			<cnName>挖掘者碎片</cnName>
			<iconUrl>ThingsIcon/DiggersCash</iconUrl><!-- 技能图标 -->
			<description>
				进化载具“挖掘者”所需的物品。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买。
				[n]副本任务“鬼爵降临”奖励和掉落。
			</description>
			<loveD num="20"/>
			<smeltD grade="1" addType="" price="3"/>
		</things>
		<things cnName="泰坦碎片">
			<name>TitansCash</name>
			<cnName>泰坦碎片</cnName>
			<iconUrl>ThingsIcon/TitansCash</iconUrl><!-- 技能图标 -->
			<description>
				进化载具“泰坦”、“黑暗泰坦”所需的物品。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]商店购买。
				[n]副本任务“无疆统治”奖励和掉落。
			</description>
			<loveD num="20"/>
			<smeltD grade="2" addType="" price="3"/>
		</things>
		<things cnName="赤焰碎片">
			<name>RedMotoCash</name><addDropDefineB>1</addDropDefineB>
			<cnName>赤焰碎片</cnName>
			<iconUrl>ThingsIcon/RedMotoCash</iconUrl><!-- 技能图标 -->
			<description>
				合成载具“赤焰”所需的物品。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]秘境“虚空客”掉落。
			</description>
			<loveD num="20"/>
			<smeltD grade="1" addType="" price="1"/>
		</things>
		
		<things cnName="狩猎者碎片">
			<name>AircraftGunCash</name><addDropDefineB>1</addDropDefineB>
			<cnName>狩猎者碎片</cnName>
			<iconUrl>ThingsIcon/AircraftGunCash</iconUrl><!-- 技能图标 -->
			<description>
				用于合成和进化载具“狩猎者”，也是武器进化到第八级所需的材料。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]秘境“钢铁僵尸王”奖励。
			</description>
			<loveD num="20"/>
			<smeltD grade="1" addType="" price="8"/>
		</things>
		
		
		<things cnName="守望之翼碎片">
			<name>WatchEagleAirCash</name><addDropDefineB>1</addDropDefineB>
			<cnName>守望之翼碎片</cnName>
			<iconUrl>ThingsIcon/WatchEagleAirCash</iconUrl><!-- 技能图标 -->
			<description>
				用于合成飞行器“守望之翼”。[n]{b}{font color='#FF66FF'}也是合成黑色武器“熔炉”必需品之一，需求40个。{/font}{/b}
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]秘境“极速守卫”。
			</description>
			<loveD num="20"/>
			<smeltD grade="1" addType="" price="8"/>
		</things>
		
		<things cnName="异齿虎碎片">
			<name>SaberTigerCarCash</name><addDropDefineB>1</addDropDefineB>
			<cnName>异齿虎碎片</cnName>
			<iconUrl>ThingsIcon/SaberTigerCarCash</iconUrl><!-- 技能图标 -->
			<description>
				用于合成载具“异齿虎”。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]异虎宝箱
			</description>
			<loveD num="20"/>
			<smeltD grade="-1" addType="" price="8"/>
		</things>
		<things cnName="年兽碎片">
			<name>NianCarCash</name><addDropDefineB>1</addDropDefineB>
			<cnName>年兽碎片</cnName>
			<iconUrl>ThingsIcon/NianCarCash</iconUrl><!-- 技能图标 -->
			<description>
				进化载具的材料，也可以用于合成载具“年兽”。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]秘境年兽
			</description>
			<loveD num="20"/>
			<smeltD grade="-1" addType="" price="8"/>
		</things>
		
	</father>	
	<father name="materials" cnName="其他碎片" smeltType="armsEquip">
		<things cnName="周年碎片">
			<name>anniversaryCash</name><addDropDefineB>1</addDropDefineB>
			<cnName>周年碎片</cnName>
			<iconUrl>ThingsIcon/anniversaryCash</iconUrl><!-- 技能图标 -->
			<description>
				可在“锻造>合成”界面中合成黑色武器碎片。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]无尽模式（主要）。
				[n]VIP每日礼包。
			</description>
			<loveD num="20"/>
			<smeltD grade="1" addType="" price="1"/>
		</things>
		<things cnName="神武碎片">
			<name>allBlackCash</name><addDropDefineB>1</addDropDefineB>
			<cnName>神武碎片</cnName>
			<iconUrl>ThingsIcon/allBlackCash</iconUrl><!-- 技能图标 -->
			<description>
				可在“锻造>合成”界面中合成指定黑色武器碎片。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]每日任务大逃亡、直上云霄。
				[n]问答。
				[n]秘境“GM三人组”。
				[n]VIP每日礼包。
			</description>
			<loveD num="20"/>
			<smeltD grade="2" addType="box" price="10"  maxNum="1"/>
		</things>
		<things cnName="神护碎片">
			<name>allBlackEquipCash</name><addDropDefineB>1</addDropDefineB>
			<cnName>神护碎片</cnName>
			<iconUrl>ThingsIcon/allBlackEquipCash</iconUrl><!-- 技能图标 -->
			<description>
				可在“锻造>合成”界面中合成指定黑色装备碎片。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]每日任务狙击之王、枪林弹雨。
				[n]问答。
				[n]秘境“GM三人组”。
				[n]VIP每日礼包。
			</description>
			<loveD num="20"/>
			<smeltD grade="2" addType="box" price="10" maxNum="1" />
		</things>
		
	</father>		
	<father name="materials" cnName="钥匙" smeltType="stone">
		<things cnName="幻想钥匙"><addDropDefineB>1</addDropDefineB>
			<name>dreamKey</name>
			<cnName>幻想钥匙</cnName>
			<dropLevelArr>76,84</dropLevelArr>
			<iconUrl>ThingsIcon/dreamKey</iconUrl><!-- 技能图标 -->
			<description>
				开启某些任务所需物品。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、消灭76、84级普通关卡首领有一定几率掉落。
				[n]2、钥匙箱开出。
			</description>
			<loveD num="4"/>
			<smeltD grade="0" addType="" price="1" />
			
		</things>
		<things cnName="勇气钥匙"><addDropDefineB>1</addDropDefineB>
			<name>courageKey</name>
			<cnName>勇气钥匙</cnName>
			<dropLevelArr>77,83</dropLevelArr>
			<iconUrl>ThingsIcon/courageKey</iconUrl><!-- 技能图标 -->
			<description>
				开启某些任务所需物品。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、消灭77、83级普通关卡首领有一定几率掉落。
				[n]2、钥匙箱开出。
			</description>
			<loveD num="4"/>
			<smeltD grade="0" addType="" price="1"/>
		</things>
		<things cnName="能量钥匙"><addDropDefineB>1</addDropDefineB>
			<name>energyKey</name>
			<cnName>能量钥匙</cnName>
			<dropLevelArr>78,82</dropLevelArr>
			<iconUrl>ThingsIcon/energyKey</iconUrl><!-- 技能图标 -->
			<description>
				开启某些任务所需物品。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、消灭78、82级普通关卡首领有一定几率掉落。
				[n]2、钥匙箱开出。
			</description>
			<loveD num="4"/>
			<smeltD grade="0" addType="" price="1"/>
		</things>
		<things cnName="胜利钥匙"><addDropDefineB>1</addDropDefineB>
			<name>victoryKey</name>
			<cnName>胜利钥匙</cnName>
			<dropLevelArr>79,81</dropLevelArr>
			<iconUrl>ThingsIcon/victoryKey</iconUrl><!-- 技能图标 -->
			<description>
				开启某些任务所需物品。
				[n][n]{b}{font color='#FFFF00'}获得方式：{/font}{/b}
				[n]1、消灭79、81级普通关卡首领有一定几率掉落。
				[n]2、钥匙箱开出。
			</description>
			<loveD num="4"/>
			<smeltD grade="0" addType="" price="1"/>
		</things>
		
		
	</father>
</data>