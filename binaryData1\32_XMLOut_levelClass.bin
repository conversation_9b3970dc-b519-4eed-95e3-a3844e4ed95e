<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="normal">
		<gather name="沃土镇关卡">
			<level name="WoTu_1">
				<!-- 关卡数据 -->
				<info enemyLv="1" diff="0.45"/>
				<!-- 基本属性 -->
				<sceneLabel>WoTu</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="2" />
						<unit cnName="战斗僵尸" num="3" />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="3" />
						<unit cnName="战斗僵尸" num="4" />
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="8" />
						<unit cnName="战斗僵尸" num="8" />
						<unit cnName="携弹僵尸" num="1" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="携弹僵尸" num="1" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">600,290,200,117</rect>
					<rect id="r_over">2940,563,60,125</rect>
					<rect id="r1">1500,180,500,80</rect>
					<rect id="r2">2000,180,500,80</rect>
					<rect id="r3">2500,180,500,80</rect>
					<rect id="r4">30,30,30,30</rect>
					
					<rect id="r_bmp">100,480,2500,30</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">491,504,78,30</rect>
					<rect label="addCharger">2507,504,78,30</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
					</group>
					<group>
						<event id="e2_1"><condition delay="2"></condition></event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_2">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_3">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:we2; r_birth</order>
						</event>
						<event id="e2_4">
							<condition doNumber="5" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<!-- 敌人全部清除干净之后，开始人物对话-->
						<event id="e2_7"><condition>enemyNumber:less_1</condition></event>
						<event id="e2_10"><!-- 几秒之后特种兵接着对话 -->
							<condition delay="1"></condition>
							<order>say; startList:s2</order>
						</event>
						<!-- 对话完毕后胜利 -->
						<event id="e2_11">
							<condition>say:listOver; s2</condition>
							<order>worldMap:levelName; WoTu:WoTu_2</order>
							<order>worldMap:save; superNum:1</order><!-- 允许精英怪出现1波 -->
							<order>completeNowMenoryTask</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<!-- 普通关卡 -->
			<level name="WoTu_2">
				<!-- 关卡数据 -->
				<info enemyLv="1" diff="0.5"/>
				<!-- 基本属性 -->
				<sceneLabel>WoTu</sceneLabel>
				<drop coin="0.75" exp="0.75" arms="0.8" equip="0.8" skillStone="0.8" taxStamp="0.3" />
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="8" />
						<unit cnName="战斗僵尸" num="8" />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="3" />
						<unit cnName="战斗僵尸" num="4" />
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="5" />
						<unit cnName="战斗僵尸" num="5" />
						<unit cnName="携弹僵尸" num="1" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="战斗僵尸" num="1" unitType="boss" />
						<unit cnName="战斗僵尸" num="1" unitType="boss" dropLabel="no" lifeMul="0.3" expMul="0.3" coinMul="0.3" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">600,290,200,117</rect>
					<rect id="r_over">2940,563,60,125</rect>
					<rect id="r1">1500,180,500,80</rect>
					<rect id="r2">2000,180,500,80</rect>
					<rect id="r3">2500,180,500,80</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">491,504,78,30</rect>
					<rect label="addCharger">2507,504,78,30</rect>
				</rectG>
				
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
					</group>
					
					<group>
						<!-- 产生敌人 ,随机2个命令中的一个-->
						<event id="e2_1"><condition delay="3"></condition></event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_2">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<!-- 对话完毕后胜利 -->
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<!-- 任务 -->
			<level name="WoTu_doctor">
				<!-- 关卡数据 -->
				<info enemyLv="19"/>
				<!-- 基本属性 -->
				<sceneLabel>WoTu</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<!-- 我方 -->
					<unitOrder id="we2" camp="we">
						<unit cnName="制毒师" num="1" lifeMul="50"   aiOrder="followBodyAttack:我" dieGotoState="stru"/><!-- 特种兵，跟随目标为主角 -->
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="8" />
						<unit cnName="战斗僵尸" num="8" />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="3" />
						<unit cnName="战斗僵尸" num="4" />
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="5" />
						<unit cnName="战斗僵尸" num="5" />
						<unit cnName="携弹僵尸" num="1" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="战斗僵尸" num="1" unitType="boss" />
						<unit cnName="战斗僵尸" num="1" unitType="boss" dropLabel="no" lifeMul="0.3"  expMul="0.3" coinMul="0.3"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">600,290,200,117</rect>
					<rect id="r_over">2940,563,60,125</rect>
					<rect id="r1">1500,180,500,80</rect>
					<rect id="r2">2000,180,500,80</rect>
					<rect id="r3">2500,180,500,80</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">491,504,78,30</rect>
					<rect label="addCharger">2507,504,78,30</rect>
				</rectG>
				
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we2;r3</order>
						</event>
					</group>
					
					<group>
						<!-- 产生敌人 ,随机2个命令中的一个-->
						<event id="e2_1"><condition delay="3"></condition></event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_2">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<!-- 对话完毕后胜利 -->
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>body:制毒师; rebirth</order>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_9"><!-- 对话结束后给玩家添加手机物品 -->
							<condition>say:listOver; s1</condition>
							<order>task:WoTu_doctor; complete</order>
							<order>worldMap:levelName; WoTu:WoTu_3</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="WoTu_3">
				<!-- 关卡数据 -->
				<info enemyLv="19"/>
				<!-- 基本属性 -->
				<sceneLabel>WoTu</sceneLabel>
				<drop coin="0.75" exp="0.75" arms="0.8" equip="0.8" skillStone="0.8" taxStamp="0.3"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					<!-- 我方 -->
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="8" />
						<unit cnName="战斗僵尸" num="8" />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="3" />
						<unit cnName="战斗僵尸" num="4" />
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="5" />
						<unit cnName="战斗僵尸" num="5" />
						<unit cnName="携弹僵尸" num="1" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="战斗僵尸" num="1" unitType="boss" />
						<unit cnName="战斗僵尸" num="1" unitType="boss" dropLabel="no" lifeMul="0.3"  expMul="0.3" coinMul="0.3"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">600,290,200,117</rect>
					<rect id="r_over">2940,563,60,125</rect>
					<rect id="r1">1500,180,500,80</rect>
					<rect id="r2">2000,180,500,80</rect>
					<rect id="r3">2500,180,500,80</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">491,504,78,30</rect>
					<rect label="addCharger">2507,504,78,30</rect>
				</rectG>
				
				<!-- 事件集************************************************ -->
				<eventG>
					
					<group>
						<!-- 产生敌人 ,随机2个命令中的一个-->
						<event id="e2_1"><condition delay="3"></condition></event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_2">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<!-- 对话完毕后胜利 -->
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="WoTu_find">
				<!-- 关卡数据 -->
				<info enemyLv="42"/>
				<!-- 基本属性 -->
				<sceneLabel>WoTu</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="8" />
						<unit cnName="战斗僵尸" num="8" />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="3" />
						<unit cnName="战斗僵尸" num="4" />
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="5" />
						<unit cnName="战斗僵尸" num="5" />
						<unit cnName="携弹僵尸" num="1" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="毒蛛" lifeMul="1.2" num="1" unitType="boss" />
						<unit cnName="毒蛛" lifeMul="1" num="2" unitType="super"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">600,290,200,117</rect>
					<rect id="r_over">2940,563,60,125</rect>
					<rect id="r1">1500,180,500,80</rect>
					<rect id="r2">2000,180,500,80</rect>
					<rect id="r3">2500,180,500,80</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">491,504,78,30</rect>
					<rect label="addCharger">2507,504,78,30</rect>
				</rectG>
				
				<!-- 事件集************************************************ -->
				<eventG>
					
					<group>
						<!-- 产生敌人 ,随机2个命令中的一个-->
						<event id="e2_1"><condition delay="3"></condition></event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_2">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<!-- 对话完毕后胜利 -->
						<event id="e2_11">
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>level; rebirthAllMore</order>
						</event>
						<event id="e2_8"><!-- 距离小于200才开始对话 -->
							<condition delay="0.5"></condition>
							<order>say; startList:s1</order>
						</event>
						<!--对话结束后胜利 -->
						<event id="e_win">
							<condition>say:listOver; s1</condition>
							<order>task:now; complete</order>
							<order>worldMap:levelName; WoTu:WoTu_4</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="WoTu_4">
				<!-- 关卡数据 -->
				<info enemyLv="42"/>
				<!-- 基本属性 -->
				<sceneLabel>WoTu</sceneLabel>
				<drop coin="0.75" exp="0.75" arms="0.8" equip="0.8" skillStone="0.8" taxStamp="0.3"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="8" dpsMul="1.6" />
						<unit cnName="战斗僵尸" num="8" dpsMul="1.6"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="3" dpsMul="1.6"/>
						<unit cnName="战斗僵尸" num="4" dpsMul="1.6"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="5" dpsMul="1.6"/>
						<unit cnName="战斗僵尸" num="5" dpsMul="1.6"/>
						<unit cnName="携弹僵尸" num="1" dpsMul="1.6"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="战斗僵尸" num="1" unitType="boss" dpsMul="1.4"/>
						<unit cnName="战斗僵尸" num="1" unitType="super" dropLabel="no"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">600,290,200,117</rect>
					<rect id="r_over">2940,563,60,125</rect>
					<rect id="r1">1500,180,500,80</rect>
					<rect id="r2">2000,180,500,80</rect>
					<rect id="r3">2500,180,500,80</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">491,504,78,30</rect>
					<rect label="addCharger">2507,504,78,30</rect>
				</rectG>
				
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_2">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<!-- 对话完毕后胜利 -->
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="WoTu_back">
				<!-- 关卡数据 -->
				<info enemyLv="1" diy="wotuBack" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" />
				<!-- 基本属性 -->
				<sceneLabel>WoTu</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="2" dpsMul="8" />
						<unit cnName="文杰表哥" lifeMul="10" dpsMul="3" aiOrder="followBodyAttack:本我" dieGotoState="stru" />
					</unitOrder>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="2" />
						<unit cnName="战斗僵尸" num="3" />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="3" />
						<unit cnName="战斗僵尸" num="4" />
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="8" />
						<unit cnName="战斗僵尸" num="8" />
						<unit cnName="携弹僵尸" num="1" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="携弹僵尸" num="1" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">600,290,200,117</rect>
					<rect id="r_over">2940,563,60,125</rect>
					<rect id="r1">1500,180,500,80</rect>
					<rect id="r2">2000,180,500,80</rect>
					<rect id="r3">2500,180,500,80</rect>
					<rect id="r4">30,30,30,30</rect>
					
					<rect id="r_bmp">100,480,2500,30</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">491,504,78,30</rect>
					<rect label="addCharger">2507,504,78,30</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>heroEverParasitic:本我</order>
							<order>P2EverParasitic:文杰表哥</order>
						</event>
						<event id="e1_1"><condition delay="2"></condition><order>say; startList:s1</order></event>
						<event id="e2_0"><condition>say:listOver; s1</condition></event><event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_2">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<!-- 敌人全部清除干净之后，开始人物对话-->
						<event id="e2_7"><condition delay="1">enemyNumber:less_1</condition><order>level; rebirthWeStruHero</order><order>say; startList:s2</order></event>
						<event id="e2_11"><condition>say:listOver; s2</condition><order>task:now; complete</order><order>level; showPointer:r_over</order></event>
					</group>
					<group>
						<!-- 死亡 任务失败 -->
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; 本我</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.03"></condition>
							<order>level; fail</order>
						</event>
					</group>
				</eventG>
			</level>
			
			
			<level name="WoTu_test">
				<!-- 关卡数据 -->
				<info enemyLv="99" noMoreB="1" mustSingleB="1"/>
				<drop noB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>XiShan1</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<numberType>num</numberType>
						<unit cnName="矿工僵尸" num="1" lifeMul="100" unitType="boss" />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>num</numberType>
						<unit cnName="矿工僵尸" num="1" lifeMul="1000"/>
					</unitOrder>
				</unitG>
				
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event>
							<order>createUnit:enemy1;r123</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy2;r123</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy2;r123</order>
						</event>
						<![CDATA[
						<event>
							<condition doNumber="100">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r123</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
						
						<event id="e2_1">
							<condition doNumber="2">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
						</event>
						<event id="e2_1">
							<condition doNumber="3">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
						</event>
						<event>
							<condition doNumber="3">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r1</order>
						</event>
						<event>
							<condition doNumber="3">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
						</event>
						<event id="e2_1">
							<condition doNumber="2">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
						</event>
						<event id="e2_1">
							<condition doNumber="3">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
						]]>
					</group>
				</eventG>
			</level>
		</gather>
		
		<gather name="杨梅岭关卡">
			<level name="YangMei_1"><!-- 有任务 -->
			<!-- 关卡数据 -->
				<info enemyLv="3" diff="0.4"/>
				<!-- 基本属性 -->
				<sceneLabel>YangMei</sceneLabel>
				<drop equip="0" skillStone="0"/>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="4"/>
						<unit cnName="战斗僵尸" num="3" />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="3"/>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="携弹僵尸" num="3" />
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="携弹僵尸" num="9" />
					</unitOrder>
					<unitOrder id="enemy5">
						<unit cnName="僵尸狙击兵" unitType="boss"  lifeMul="0.5" dropLabel="{'type':'arms','type2':'now'}" armsRange="sniperRifle" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">173,490,246,148</rect>
					<rect id="r1">1300,757,400,60</rect>
					<rect id="r2">2357,515,455,86</rect>
					<rect id="r3">2600,-200,314,66</rect>
					<rect id="r_over">-20,-129,63,117</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">212,578,78,30</rect>
					<rect label="addCharger">2704,578,78,30</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!--开始和表哥对话-->
						<event id="e1_1">
							<condition delay="2"></condition>
							<order>say; startList:s1</order>
						</event>
					
					<!-- 对话结束后，添加任务，产生敌人 ,随机2个命令中的一个-->
						<event id="e2_0">
							<condition>say:listOver; s1</condition>
							<order>task; get:YangMei_1</order>
						</event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition delay="2" doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<event id="e2_5">
							<condition>enemyNumber:less_4</condition>
							<order>createUnit:enemy5; r3</order>
						</event>
					</group>
					<!-- 任务完成后胜利 -->
					<group>
						<event id="e_task">
							<condition delay="0.5">bodyEvent:del; 僵尸狙击兵</condition>
							<order>task:YangMei_1; complete</order>
							<order>worldMap:save; superNum:2</order><!-- 允许精英怪出现1波 -->
							<order>worldMap:levelName; YangMei:YangMei_2</order>
						</event>	
						<event id="e_win">
							<condition delay="1"></condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="YangMei_2">
				<!-- 关卡数据 -->
				<info enemyLv="3" diff="0.5" />
				<!-- 基本属性 -->
				<sceneLabel>YangMei</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1.5"/>
						<unit cnName="战斗僵尸" num="1.5" />
						<unit cnName="携弹僵尸" num="1.5" />
						<unit cnName="僵尸暴枪兵" num="0.5"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1.5"/>
						<unit cnName="战斗僵尸" num="1.5" />
						<unit cnName="携弹僵尸" num="1.5" />
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸狙击兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="肥胖僵尸" num="3"/>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="携弹僵尸" num="3" />
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸狙击兵" unitType="boss" num="1"/>
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">173,490,246,148</rect>
					<rect id="r1">1300,757,400,60</rect>
					<rect id="r2">2357,515,455,86</rect>
					<rect id="r3">2600,-200,314,66</rect>
					<rect id="r_over">-20,-129,63,117</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">212,578,78,30</rect>
					<rect label="addCharger">2704,578,78,30</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_3">
							<condition orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
						
				</eventG>
			</level>
			<level name="YangMei_3">
				<!-- 关卡数据 -->
				<info enemyLv="18"/>
				<!-- 基本属性 -->
				<sceneLabel>YangMei</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1.5"/>
						<unit cnName="战斗僵尸" num="1.5" />
						<unit cnName="携弹僵尸" num="1.5" />
						<unit cnName="僵尸暴枪兵" num="0.5"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1.5"/>
						<unit cnName="战斗僵尸" num="1.5" />
						<unit cnName="携弹僵尸" num="1.5" />
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸狙击兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="肥胖僵尸" num="3"/>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="携弹僵尸" num="3" />
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸狙击兵" unitType="boss" num="1"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">173,490,246,148</rect>
					<rect id="r1">1300,757,400,60</rect>
					<rect id="r2">2357,515,455,86</rect>
					<rect id="r3">2600,-200,314,66</rect>
					<rect id="r_over">-20,-129,63,117</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">212,578,78,30</rect>
					<rect label="addCharger">2704,578,78,30</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_3">
							<condition orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
						
				</eventG>
			</level>
			<level name="YangMei_back">
				<!-- 关卡数据 -->
				<info enemyLv="36"/>
				<!-- 基本属性 -->
				<sceneLabel>YangMei</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="橄榄僵尸" num="1.5"/>
						<unit cnName="携弹僵尸" num="1.5" />
						<unit cnName="僵尸炮兵总管" num="1"/>
						<unit cnName="僵尸突击兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="携弹僵尸" num="2" />
						<unit cnName="僵尸炮兵总管" num="1"/>
						<unit cnName="僵尸突击兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="携弹僵尸" num="2" />
						<unit cnName="僵尸炮兵总管" num="1.5"/>
						<unit cnName="僵尸突击兵" num="1.5"/>
						<unit cnName="僵尸狙击兵" unitType="boss" num="1" dpsMul="1.5" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">173,490,246,148</rect>
					<rect id="r1">1300,757,400,60</rect>
					<rect id="r2">2357,515,455,86</rect>
					<rect id="r3">2600,-200,314,66</rect>
					<rect id="r_over">-20,-129,63,117</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">212,578,78,30</rect>
					<rect label="addCharger">2704,578,78,30</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_3">
							<condition orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>worldMap:levelName; YangMei:YangMei_4</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
						
				</eventG>
			</level>
			<level name="YangMei_4">
				<!-- 关卡数据 -->
				<info enemyLv="36"/>
				<!-- 基本属性 -->
				<sceneLabel>YangMei</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="橄榄僵尸" num="1.5"/>
						<unit cnName="携弹僵尸" num="1.5" />
						<unit cnName="僵尸炮兵总管" num="1"/>
						<unit cnName="僵尸突击兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="携弹僵尸" num="2" />
						<unit cnName="僵尸炮兵总管" num="1.5"/>
						<unit cnName="僵尸突击兵" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="携弹僵尸" num="1" />
						<unit cnName="僵尸狙击兵" unitType="boss" num="1" dpsMul="1.5" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">173,490,246,148</rect>
					<rect id="r1">1300,757,400,60</rect>
					<rect id="r2">2357,515,455,86</rect>
					<rect id="r3">2600,-200,314,66</rect>
					<rect id="r_over">-20,-129,63,117</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">212,578,78,30</rect>
					<rect label="addCharger">2704,578,78,30</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_3">
							<condition orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							
							<order>level; showPointer:r_over</order>
						</event>
					</group>
						
				</eventG>
			</level>
		</gather>
		
		<gather name="下沙关卡">
			<level name="XiaSha_1">
				<!-- 关卡数据 -->
				<info enemyLv="4" diff="0.45" />
				<!-- 基本属性 -->
				<sceneLabel>XiaSha</sceneLabel>
				<drop skillStone="0"/>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="僵尸暴枪兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="3"/>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="携弹僵尸" num="2" />
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="3"/>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="携弹僵尸" num="2" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="僵尸狙击兵" num="2"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1" lifeMul="0.35" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">192,60,232,120</rect>
					<rect id="r_over">2956,1073,53,113</rect>
					<rect id="r1">2944,1067,56,127</rect>
					<rect id="r2">1034,-16,300,134</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">45,111,80,46</rect>
					<rect label="addCharger">2400,838,80,46</rect>
					
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!--开始和表哥对话-->
						<event id="e1_1">
							<condition delay="2"></condition>
							<order>say; startList:s1</order>
						</event>
					
					<!-- 对话结束后，产生敌人 ,随机2个命令中的一个-->
						<event id="e2_0"><condition>say:listOver; s1</condition></event>
						<event id="e2_1">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
						</event>
						<event id="e2_2">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
						</event>
						
						<event id="e_win">
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>worldMap:levelName; XiaSha:XiaSha_2</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					
				</eventG>
			</level>
			<level name="XiaSha_2">
				<!-- 关卡数据 -->
				<info enemyLv="4" diff="0.55" />
				<!-- 基本属性 -->
				<sceneLabel>XiaSha</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.25"/>
						<unit cnName="战斗僵尸" num="0.25" />
						<unit cnName="携弹僵尸" num="0.25" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.25"/>
						<unit cnName="战斗僵尸" num="0.25" />
						<unit cnName="携弹僵尸" num="0.25" />
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3" mustSuperB="1">
						<unit cnName="僵尸狙击兵" num="3"/>
						<unit cnName="僵尸暴枪兵" num="2"/>
						<unit cnName="僵尸暴枪兵" num="1" lifeMul="0.6" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">192,60,232,120</rect>
					<rect id="r_over">2956,1073,53,113</rect>
					<rect id="r1">2944,1067,56,127</rect>
					<rect id="r2">1034,-16,300,134</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">45,111,80,46</rect>
					<rect label="addCharger">2400,838,80,46</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
					<!-- 对话结束后，产生敌人 ,随机2个命令中的一个-->
						<event id="e2_1">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
						</event>
						<event id="e2_2">
							<condition doNumber="8" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="9" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					
				</eventG>
			</level>
			<level name="XiaSha_3">
				<!-- 关卡数据 -->
				<info enemyLv="19"/>
				<!-- 基本属性 -->
				<sceneLabel>XiaSha</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.25"/>
						<unit cnName="战斗僵尸" num="0.25" />
						<unit cnName="携弹僵尸" num="0.25" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.25"/>
						<unit cnName="战斗僵尸" num="0.25" />
						<unit cnName="携弹僵尸" num="0.25" />
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3" mustSuperB="1">
						<unit cnName="僵尸狙击兵" num="3"/>
						<unit cnName="僵尸暴枪兵" num="2"/>
						<unit cnName="僵尸暴枪兵" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">192,60,232,120</rect>
					<rect id="r_over">2956,1073,53,113</rect>
					<rect id="r1">2944,1067,56,127</rect>
					<rect id="r2">1034,-16,300,134</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">45,111,80,46</rect>
					<rect label="addCharger">2400,838,80,46</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
					<!-- 对话结束后，产生敌人 ,随机2个命令中的一个-->
						<event id="e2_1">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
						</event>
						<event id="e2_2">
							<condition doNumber="8" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="9" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					
				</eventG>
			</level>
			<level name="XiaSha_Ling">
				<!-- 关卡数据 -->
				<info enemyLv="32"/>
				<!-- 基本属性 -->
				<sceneLabel>XiaSha</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="携弹僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="携弹僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3" mustSuperB="1">
						<unit cnName="僵尸狙击兵" num="3"/>
						<unit cnName="僵尸暴枪兵" num="2"/>
						<unit cnName="鬼目游尸" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">192,60,232,120</rect>
					<rect id="r_over">2956,1073,53,113</rect>
					<rect id="r1">2944,1067,56,127</rect>
					<rect id="r2">1034,-16,300,134</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">45,111,80,46</rect>
					<rect label="addCharger">2400,838,80,46</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
					<!-- 对话结束后，产生敌人 ,随机2个命令中的一个-->
						<event id="e2_1">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
						</event>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>worldMap:levelName; XiaSha:XiaSha_4</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					
				</eventG>
			</level>
			<level name="XiaSha_4">
				<!-- 关卡数据 -->
				<info enemyLv="32"/>
				<!-- 基本属性 -->
				<sceneLabel>XiaSha</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="2"/>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="携弹僵尸" num="2" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="2"/>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="携弹僵尸" num="2" />
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="鬼目游尸" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">192,60,232,120</rect>
					<rect id="r_over">2956,1073,53,113</rect>
					<rect id="r1">2944,1067,56,127</rect>
					<rect id="r2">1034,-16,300,134</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">45,111,80,46</rect>
					<rect label="addCharger">2400,838,80,46</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
					<!-- 对话结束后，产生敌人 ,随机2个命令中的一个-->
						<event id="e2_1">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
						</event>
						<event id="e2_2">
							<condition doNumber="5" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="5" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					
				</eventG>
			</level>
		</gather>	
		
		<gather name="凤尾洋关卡">
			<level name="FengWei_1">
				<!-- 关卡数据 -->
				<info enemyLv="5" diff="0.5" />
				<!-- 基本属性 -->
				<sceneLabel>FengWei</sceneLabel>
				<drop skillStone="0"/>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.25"/>
						<unit cnName="战斗僵尸" num="0.25" />
						<unit cnName="携弹僵尸" num="0.25" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.25"/>
						<unit cnName="战斗僵尸" num="0.25" />
						<unit cnName="携弹僵尸" num="0.25" />
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸狙击兵" num="3"/>
						<unit cnName="僵尸暴枪兵" num="3"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="携弹僵尸" num="1" dpsMul="0.6" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">1734,640,145,83</rect>
					<rect id="r_over">3376,1008,56,127</rect>
					<rect id="r1">20,968,442,158</rect>
					<rect id="r2">3000,968,442,158</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">514,900,145,83</rect>
					<rect label="addCharger">2600,900,145,83</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<!--开始和表哥对话-->
						<event id="e1_1">
							<condition delay="2"></condition>
							<order>say; startList:s1</order>
						</event>
					<!-- 对话结束后，产生敌人 ,随机2个命令中的一个-->
						<event id="e2_0"><condition>say:listOver; s1</condition></event>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_2"><condition delay="4">enemyNumber:less_1</condition></event>
						
						<event id="e2_3">
							<condition doNumber="9" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e2_2"><condition delay="4">enemyNumber:less_1</condition></event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
						</event>
						<!--对话结束后胜利 -->
						<event id="e_win">
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>worldMap:levelName; FengWei:FengWei_2</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="FengWei_2">
				<!-- 关卡数据 -->
				<info enemyLv="5" diff="0.6"/>
				<!-- 基本属性 -->
				<sceneLabel>FengWei</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="携弹僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.55"/>
						<unit cnName="战斗僵尸" num="0.55" />
						<unit cnName="携弹僵尸" num="0.55" />
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸狙击兵" num="3"/>
						<unit cnName="僵尸暴枪兵" num="3"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="携弹僵尸" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">1734,640,145,83</rect>
					<rect id="r_over">3376,1008,56,127</rect>
					<rect id="r1">20,968,442,158</rect>
					<rect id="r2">3000,968,442,158</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">514,900,145,83</rect>
					<rect label="addCharger">2600,900,145,83</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
						</event>
						<!-- 所有单位都死后，表哥部队出现 -->
						<event id="e_WenJieShow">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="FengWei_3">
				<!-- 关卡数据 -->
				<info enemyLv="24"/>
				<!-- 基本属性 -->
				<sceneLabel>FengWei</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="携弹僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="0.6"/>
						<unit cnName="僵尸空降兵" num="0.6"/>
						<unit cnName="僵尸暴枪兵" num="0.6"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.55"/>
						<unit cnName="战斗僵尸" num="0.55" />
						<unit cnName="携弹僵尸" num="0.55" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸空降兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸狙击兵" num="2"/>
						<unit cnName="僵尸暴枪兵" num="2"/>
						<unit cnName="僵尸空降兵" num="2"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="携弹僵尸" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">1734,640,145,83</rect>
					<rect id="r_over">3376,1008,56,127</rect>
					<rect id="r1">20,568,442,158</rect>
					<rect id="r2">3000,568,442,158</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">514,900,145,83</rect>
					<rect label="addCharger">2600,900,145,83</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
						</event>
						<!-- 所有单位都死后，表哥部队出现 -->
						<event id="e_WenJieShow">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="FengWei_TianYing">
				<!-- 关卡数据 -->
				<info enemyLv="31"/>
				<!-- 基本属性 -->
				<sceneLabel>FengWei</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="天鹰特种兵" num="4"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸空降兵" num="4"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="天鹰特种兵" num="3"/>
						<unit cnName="僵尸空降兵" num="3"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="天鹰特种兵" num="1"/>
						<unit cnName="僵尸空降兵" num="1"/>
						<unit cnName="天鹰空降兵" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">1734,640,145,83</rect>
					<rect id="r_over">3376,1008,56,127</rect>
					<rect id="r1">20,568,442,158</rect>
					<rect id="r2">3000,568,442,158</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">514,900,145,83</rect>
					<rect label="addCharger">2600,900,145,83</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<event id="e2_2">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e2_4">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
						</event>
						<!-- 所有单位都死后，表哥部队出现 -->
						<event id="e_WenJieShow">
							<condition>enemyNumber:less_1</condition>
							<order>worldMap:levelName; FengWei:FengWei_4</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="FengWei_4">
				<!-- 关卡数据 -->
				<info enemyLv="31"/>
				<!-- 基本属性 -->
				<sceneLabel>FengWei</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="天鹰特种兵" num="4"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸空降兵" num="4"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="天鹰特种兵" num="4"/>
						<unit cnName="僵尸空降兵" num="3"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="天鹰空降兵" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">1734,640,145,83</rect>
					<rect id="r_over">3376,1008,56,127</rect>
					<rect id="r1">20,568,442,158</rect>
					<rect id="r2">3000,568,442,158</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">514,900,145,83</rect>
					<rect label="addCharger">2600,900,145,83</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<event id="e2_2">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_3">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e2_4">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
						</event>
						<!-- 所有单位都死后，表哥部队出现 -->
						<event id="e_WenJieShow">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="FengWei_5">
				<info enemyLv="30"/>
				<!-- 基本属性 -->
				<sceneLabel>FengWei</sceneLabel>
				<fixed target="FengWei_4" info="no" drop="all" unitG="all" rectG="all" eventG="all"/>
			</level>
		</gather>	
		
		<gather name="西池关卡">
			<level name="XiChi_1"><!-- 有任务 -->
				<!-- 关卡数据 -->
				<info enemyLv="6" diff="0.55"/>
				<!-- 基本属性 -->
				<sceneLabel>XiChi</sceneLabel>
				<drop skillStone="0"/>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="携弹僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.55"/>
						<unit cnName="战斗僵尸" num="0.55" />
						<unit cnName="携弹僵尸" num="0.55" />
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="僵尸狙击兵" num="3"/>
						<unit cnName="僵尸暴枪兵" num="3"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="僵尸炮兵总管" unitType="boss" dropLabel="{'type':'things','name':'skillStone','num':4}"/>
					</unitOrder>
				</unitG>
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1144,963,286,52</rect>
					<rect id="r_over">2896,1109,70,126</rect>
					<rect id="r1">1331,221,352,92</rect>
					<rect id="r2">15,1141,222,92</rect>
					<rect id="r3">2504,1171,222,92</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">771,1011,120,62</rect>
					<rect label="addCharger">2600,1166,145,83</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!-- 添加任务 -->
						<event id="e1_1">
							<condition delay="1"></condition>
							<order>task; get:XiChi_1</order>
						</event>
						<!-- 2秒后发兵 -->
						<event id="e1_1"><condition delay="2"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<event id="e_boss">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						
						<event id="e_task">
							<condition delay="1">enemyNumber:less_1</condition>
							<order>task:XiChi_1; complete</order>
							<order>worldMap:levelName; XiChi:XiChi_2</order>
						</event>
						<!-- 开始对话 -->
						<event id="e_say">
							<order>say; startList:s1</order>
						</event>
						<event id="e_say">
							<condition>say:listOver; s1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<!-- 任务完成后胜利 -->
				</eventG>
			</level>
			<level name="XiChi_2">
				<!-- 关卡数据 -->
				<info enemyLv="6" diff="0.65" />
				<!-- 基本属性 -->
				<sceneLabel>XiChi</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="携弹僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="0.7"/>
						<unit cnName="僵尸暴枪兵" num="0.7"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.55"/>
						<unit cnName="战斗僵尸" num="0.55" />
						<unit cnName="携弹僵尸" num="0.55" />
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸狙击兵" num="2"/>
						<unit cnName="僵尸暴枪兵" num="2"/>
						<unit cnName="僵尸炮兵总管" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1144,963,286,52</rect>
					<rect id="r_over">2896,1109,70,126</rect>
					<rect id="r1">1331,221,352,92</rect>
					<rect id="r2">15,1141,222,92</rect>
					<rect id="r3">2504,1171,222,92</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">771,1011,120,62</rect>
					<rect label="addCharger">2600,1166,145,83</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!-- 2秒后发兵 -->
						<event id="e1_1"><condition delay="2"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<!-- 开始对话 -->
						<event id="e_win">
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
						
						<!-- 胜利由任务决定 -->
					</group>
				</eventG>
			</level>
			<level name="XiChi_blood">
				<!-- 关卡数据 -->
				<info enemyLv="21" />
				<!-- 基本属性 -->
				<sceneLabel>XiChi</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<!-- 我方 -->
					<unitOrder id="we1" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="制毒师" num="1" lifeMul="99999" noUnderHurtB="1"  aiOrder="followBodyAttack:我" dieGotoState="stru"/>
					</unitOrder>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.6"/>
						<unit cnName="战斗僵尸" num="0.6" />
						<unit cnName="携弹僵尸" num="0.6" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸空降兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.55"/>
						<unit cnName="战斗僵尸" num="0.55" />
						<unit cnName="携弹僵尸" num="0.55" />
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
						<unit cnName="僵尸空降兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸狙击兵" num="2"/>
						<unit cnName="僵尸暴枪兵" num="2"/>
						<unit cnName="僵尸空降兵" unitType="boss" num="1"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1144,963,286,52</rect>
					<rect id="r_over">2896,1109,70,126</rect>
					<rect id="r1">1331,221,352,92</rect>
					<rect id="r2">15,541,222,92</rect>
					<rect id="r3">2504,571,222,92</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">771,1011,120,62</rect>
					<rect label="addCharger">2600,1166,145,83</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<!-- 2秒后发兵 -->
						<event id="e1_1"><condition delay="2"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<!-- 开始对话 -->
						<event id="e_win">
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
						
						<!-- 胜利由任务决定 -->
					</group>
					<group>
						<event id="e_win">
							<condition delay="0.5">task:state; XiChi_blood:complete</condition>
							<order>level; noEnemy</order>
						</event>
						<event id="e_say">
							<condition>enemyNumber:less_1</condition>
							<order>worldMap:levelName; XiChi:XiChi_3</order>
							<order>say; startList:s1</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="XiChi_3">
				<!-- 关卡数据 -->
				<info enemyLv="21" />
				<!-- 基本属性 -->
				<sceneLabel>XiChi</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="携弹僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="携弹僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
						<unit cnName="僵尸炮兵总管" num="1"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="防暴僵尸" num="1" dpsMul="1.5" lifeMul="0.8" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1144,963,286,52</rect>
					<rect id="r_over">2896,1109,70,126</rect>
					<rect id="r1">1331,221,352,92</rect>
					<rect id="r2">15,700,222,92</rect>
					<rect id="r3">2504,700,222,92</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">771,1011,120,62</rect>
					<rect label="addCharger">2600,1166,145,83</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!-- 2秒后发兵 -->
						<event id="e1_1"><condition delay="2"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="5" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<!-- 开始对话 -->
						<event id="e_win">
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="XiChi_win">
				<!-- 关卡数据 -->
				<info enemyLv="41" />
				<!-- 基本属性 -->
				<sceneLabel>XiChi</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="携弹僵尸" num="1" />
						<unit cnName="天鹰特种兵" num="1"/>
						<unit cnName="天鹰空降兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="0.55"/>
						<unit cnName="战斗僵尸" num="0.55" />
						<unit cnName="携弹僵尸" num="0.55" />
						<unit cnName="天鹰特种兵" num="1.5"/>
						<unit cnName="天鹰空降兵" num="1.5"/>
						<unit cnName="防暴僵尸" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="奇皇博士" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1144,963,286,52</rect>
					<rect id="r_over">2840,1109,70,126</rect>
					<rect id="r1">1331,221,352,92</rect>
					<rect id="r2">15,700,222,92</rect>
					<rect id="r3">2504,700,222,92</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">771,1011,120,62</rect>
					<rect label="addCharger">2600,1166,145,83</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<!-- 2秒后发兵 -->
						<event id="e1_1"><condition delay="2"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<!-- 开始对话 -->
						<event id="e_win">
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>task:XiChi_win; complete</order>
							<order>worldMap:levelName; XiChi:XiChi_win</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		
		<gather name="白鹭镇关卡">
			<level name="BaiLu_1">
				<!-- 关卡数据 -->
				<info enemyLv="7" diff="0.55"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiLu</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="藏师将军" num="1" aiOrder="followBodyAttack:我"/>
						<unit cnName="雇佣兵" num="4"  aiOrder="followBodyAttack:藏师将军"/><!-- 特种兵，跟随目标为主角 -->
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal" ></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.1"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="0.3" />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="1" />
						<unit cnName="橄榄僵尸" num="1" unitType="boss" />
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1070,234,160,50</rect>
					<rect id="r_over">2946,363,71,130</rect>
					<rect id="r1">30,250,220,64</rect>
					<rect id="r2">2770,427,220,64</rect>
					<rect id="r3">1144,621,220,64</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2286,300,74,44</rect>
					<rect label="addCharger">437,230,74,44</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!-- 2秒后发兵 -->
						<event id="e1_1">
							<condition delay="2"></condition>
						</event>
						
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<!-- 所有单位都死后，藏师部队出现 -->
						<event id="e_ZangShiShow">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:we2; r2</order>
							<order>level; rebirthAllMore</order>
						</event>
						<event id="e2_8"><!-- 距离小于200才开始对话 -->
							<condition>bodyGap:less_350; 我:藏师将军</condition>
							<order>say; startList:s1</order>
						</event>
						<!--对话结束后胜利 -->
						<event id="e2_9">
							<condition delay="1">say:listOver; s1</condition>
							<order>say; startList:s2</order>
							<order>body:藏师将军; ai:patrolRandom</order><!-- 随机巡逻 -->
						</event>
						<event id="e_win">
							<condition>say:listOver; s2</condition>
							<order>worldMap:save; superNum:3</order><!-- 允许精英怪出现 -->
							<order>worldMap:levelName; BaiLu:BaiLu_2</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="BaiLu_2">
				<!-- 关卡数据 -->
				<info enemyLv="7" diff="0.65"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiLu</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal" ></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.1"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="0.3" />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="1" />
						<unit cnName="橄榄僵尸" num="1" unitType="boss" />
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1070,234,160,50</rect>
					<rect id="r_over">2946,363,71,130</rect>
					<rect id="r1">30,250,220,64</rect>
					<rect id="r2">2770,427,220,64</rect>
					<rect id="r3">1144,621,220,64</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2286,300,74,44</rect>
					<rect label="addCharger">437,230,74,44</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<![CDATA[]]>
						<!-- 2秒后发兵 -->
						<event id="e1_1">
							<condition delay="2"></condition>
						</event>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="BaiLu_ZangShi">
				<!-- 关卡数据 -->
				<info enemyLv="19"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiLu</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="藏师将军" num="1" lifeMul="0.3" aiOrder="followBodyAttack:我" dieGotoState="stru"/>
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal" ></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="天鹰特种兵" num="3" />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="天鹰特种兵" num="4" />
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="天鹰特种兵" num="5" />
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1070,234,160,50</rect>
					<rect id="r_over">2946,363,71,130</rect>
					<rect id="r1">30,250,220,64</rect>
					<rect id="r2">2770,427,220,64</rect>
					<rect id="r3">1144,621,220,64</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2286,300,74,44</rect>
					<rect label="addCharger">437,230,74,44</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we2; r2</order>
						</event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_11">
							<condition>say:listOver; s1</condition>
							<order>task:BaiLu_ZangShi; complete</order>
							<order>more; add:ZangShi</order>
							<order>worldMap:levelName; BaiLu:BaiLu_3</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
			<level name="BaiLu_3">
				<!-- 关卡数据 -->
				<info enemyLv="19"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiLu</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" ></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.1"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="0.3" />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="1" />
						<unit cnName="橄榄僵尸" num="1" unitType="boss" />
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1070,234,160,50</rect>
					<rect id="r_over">2946,363,71,130</rect>
					<rect id="r1">30,250,220,64</rect>
					<rect id="r2">2770,427,220,64</rect>
					<rect id="r3">1144,621,220,64</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2286,300,74,44</rect>
					<rect label="addCharger">437,230,74,44</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<![CDATA[]]>
						<!-- 2秒后发兵 -->
						<event id="e1_1">
							<condition delay="2"></condition>
						</event>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="BaiLu_back">
				<!-- 关卡数据 -->
				<info enemyLv="39"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiLu</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal" ></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.1"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="0.1" />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="橄榄僵尸" num="1" unitType="boss" dpsMul="2.5" lifeMul="1" />
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1070,234,160,50</rect>
					<rect id="r_over">2946,363,71,130</rect>
					<rect id="r1">30,250,220,64</rect>
					<rect id="r2">2770,427,220,64</rect>
					<rect id="r3">1144,621,220,64</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2286,300,74,44</rect>
					<rect label="addCharger">437,230,74,44</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<![CDATA[]]>
						<!-- 2秒后发兵 -->
						<event id="e1_1">
							<condition delay="2"></condition>
						</event>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>worldMap:levelName; BaiLu:BaiLu_4</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="BaiLu_4">
				<!-- 关卡数据 -->
				<info enemyLv="39"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiLu</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" ></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.1"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="0.3" />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="1" />
						<unit cnName="橄榄僵尸" num="1" unitType="boss" dpsMul="2.5" lifeMul="2" />
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1070,234,160,50</rect>
					<rect id="r_over">2946,363,71,130</rect>
					<rect id="r1">30,250,220,64</rect>
					<rect id="r2">2770,427,220,64</rect>
					<rect id="r3">1144,621,220,64</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2286,300,74,44</rect>
					<rect label="addCharger">437,230,74,44</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<![CDATA[]]>
						<!-- 2秒后发兵 -->
						<event id="e1_1">
							<condition delay="0.01"></condition>
						</event>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="BaiLu_5">
				<info enemyLv="40"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiLu</sceneLabel>
				<fixed target="BaiLu_4" info="no" drop="all" unitG="all" rectG="all" eventG="all"/>
			</level>
			<level name="BaiLu_re">
				<!-- 关卡数据 -->
				<info enemyLv="7" diff="0.55" diy="wotuBack" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1"/>
				<drop arms="0" equip="0"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiLu</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="4" dpsMul="16" armsRange="pistol1,shotgun1" skillArr="crazy_hero_5,groupLight_hero_5" />
						<unit cnName="文杰表哥" lifeMul="18" dpsMul="4" aiOrder="followBodyAttack:本我" dieGotoState="stru" skillArr="tenacious_hero_5" />
					</unitOrder>
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="藏师将军" num="1" aiOrder="followBodyAttack:文杰表哥"/>
						<unit cnName="雇佣兵" num="1"  aiOrder="followBodyAttack:藏师将军"/><!-- 特种兵，跟随目标为主角 -->
						<unit cnName="丛林特种兵" num="1"  aiOrder="followBodyAttack:藏师将军"/><!-- 特种兵，跟随目标为主角 -->
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal" ></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.1"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="0.3" />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="1" unitType="boss" />
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1070,234,160,50</rect>
					<rect id="r_over">2946,363,71,130</rect>
					<rect id="r1">30,250,220,64</rect>
					<rect id="r2">2770,427,220,64</rect>
					<rect id="r3">1144,621,220,64</rect>
					
					<rect id="r_back">-400,235,85,55</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2286,300,74,44</rect>
					<rect label="addCharger">437,230,74,44</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>heroEverParasitic:本我</order>
							<order>P2EverParasitic:文杰表哥</order>
						</event>
						<event id="e1_1"><condition></condition><order>say; startList:s1</order></event>
						<event id="e2_0"><condition>say:listOver; s1</condition></event><event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_2">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<!-- 所有单位都死后，藏师部队出现 -->
						<event id="e_ZangShiShow">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:we2; r2</order>
							<order>level; rebirthWeStruHero</order>
						</event>
						<event id="e2_8"><!-- 距离小于200才开始对话 -->
							<condition>bodyGap:less_350; 本我:藏师将军</condition>
							<order>say; startList:s2</order>
						</event>
						<!--对话结束后胜利 -->
						<event id="e2_9">
							<condition delay="0.1">say:listOver; s2</condition>
							<order>say; startList:s3</order>
							<order>body:藏师将军; rangeLimit:close</order>
							<order>body:丛林特种兵; rangeLimit:close</order>
							<order>body:雇佣兵; rangeLimit:close</order>
							<order>body:藏师将军; followPoint:r_back</order>
						</event>
						<event id="e_win">
							<condition delay="0.1">say:listOver; s3</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					
					<group>
						<!-- 死亡 任务失败 -->
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; 本我</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.03"></condition>
							<order>level; fail</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>	
		
		<gather name="水升村关卡">
			<level name="ShuiSheng_1"><!-- 有任务 -->
				<!-- 关卡数据 -->
				<info enemyLv="8" diff="0.6"/>
				<!-- 基本属性 -->
				<sceneLabel>ShuiSheng</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="藏师将军" num="1" aiOrder="followBodyAttack:我"/>
						<unit cnName="雇佣兵" num="3"  aiOrder="followBodyAttack:藏师将军"/><!-- 特种兵，跟随目标为主角 -->
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.1"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="橄榄僵尸" num="3"/>
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.3" />
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="4" />
						<unit cnName="橄榄僵尸" num="4"/>
						<unit cnName="屠刀僵尸" num="4" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.5" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="僵尸王" unitType="boss" imgType="normal" dropLabel="{'type':'arms','type2':'rocket'}" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1801,994,126,48</rect>
					<rect id="r_over">3453,1077,48,84</rect>
					<rect id="r1">30,1096,220,64</rect>
					<rect id="r2">3257,1096,220,64</rect>
					<rect id="r3">1667,375,312,103</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">722,858,60,38</rect>
					<rect label="addCharger">2736,858,60,38</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!--开始和表哥对话-->
						<event id="e1_1">
							<condition delay="0.5"></condition>
							<order>task; get:ShuiSheng_1</order>
						</event>
						<!-- 2秒后发兵 -->
						<event id="e1_1">
							<condition delay="1"></condition>
						</event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="5" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<event id="e2_5">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						
						<!-- 所有单位都死后，藏师部队出现 -->
						<event id="e_ZangShiShow">
							<condition>enemyNumber:less_1</condition>
							<order>task:ShuiSheng_1; complete</order>
							<order>createUnit:we2; r2</order>
							<order>level; rebirthAllMore</order>
						</event>
						<event id="e2_8"><!-- 距离小于200才开始对话 -->
							<condition>bodyGap:less_350; 我:藏师将军</condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e_win">
							<condition>say:listOver; s2</condition>
							<order>worldMap:levelName; ShuiSheng:ShuiSheng_2</order>
							<order>worldMap:save; dropRocketB:1</order><!-- 允许掉落火箭炮 -->
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="ShuiSheng_2">
				<!-- 关卡数据 -->
				<info enemyLv="8" diff="0.7"/>
				<!-- 基本属性 -->
				<sceneLabel>ShuiSheng</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.1"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="橄榄僵尸" num="3"/>
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy3" mustSuperB="1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="4" />
						<unit cnName="橄榄僵尸" num="4"/>
						<unit cnName="屠刀僵尸" num="4" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.5"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="僵尸王" unitType="boss" imgType="normal"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1801,994,126,48</rect>
					<rect id="r_over">3453,1077,48,84</rect>
					<rect id="r1">30,1096,220,64</rect>
					<rect id="r2">3257,1096,220,64</rect>
					<rect id="r3">1667,375,312,103</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">722,858,60,38</rect>
					<rect label="addCharger">2736,858,60,38</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_5">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<event id="e_win">
							<condition delay="2">enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="ShuiSheng_king">
				<!-- 关卡数据 -->
				<info enemyLv="29"/>
				<!-- 基本属性 -->
				<sceneLabel>ShuiSheng</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
				
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="0.2"/>
						<unit cnName="僵尸空军总管" num="0.3"/>
						<unit cnName="僵尸空降兵" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="橄榄僵尸" num="3"/>
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="0.4"/>
						<unit cnName="僵尸空军总管" num="0.4"/>
						<unit cnName="僵尸空降兵" num="0.4"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="4" />
						<unit cnName="橄榄僵尸" num="4"/>
						<unit cnName="屠刀僵尸" num="4" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸空军总管" num="0.5"/>
						<unit cnName="僵尸空降兵" num="0.5"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="僵尸王" unitType="boss" skillArr="trueshot_enemy,sweep_enemy" imgType="normal"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1801,994,126,48</rect>
					<rect id="r_over">3453,1077,48,84</rect>
					<rect id="r1">30,1096,220,64</rect>
					<rect id="r2">3257,1096,220,64</rect>
					<rect id="r3">1667,375,312,103</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">722,858,60,38</rect>
					<rect label="addCharger">2736,858,60,38</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_5">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s1</condition>
							<order>say; startList:s2</order>
						</event>	
						<event id="e2_11">
							<condition>say:listOver; s2</condition>
							<order>task:ShuiSheng_king; complete</order>
							<order>worldMap:levelName; ShuiSheng:ShuiSheng_3</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
			<level name="ShuiSheng_3">
				<!-- 关卡数据 -->
				<info enemyLv="30"/>
				<!-- 基本属性 -->
				<sceneLabel>ShuiSheng</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="0.2"/>
						<unit cnName="僵尸空军总管" num="0.3"/>
						<unit cnName="僵尸空降兵" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="橄榄僵尸" num="3"/>
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="0.4"/>
						<unit cnName="僵尸空军总管" num="0.4"/>
						<unit cnName="僵尸空降兵" num="0.4"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="4" />
						<unit cnName="橄榄僵尸" num="4"/>
						<unit cnName="屠刀僵尸" num="4" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸空军总管" num="0.5"/>
						<unit cnName="僵尸空降兵" num="0.5"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="僵尸王" unitType="boss" dpsMul="1.2" skillArr="trueshot_enemy,sweep_enemy" imgType="normal"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1801,994,126,48</rect>
					<rect id="r_over">3453,1077,48,84</rect>
					<rect id="r1">30,1096,220,64</rect>
					<rect id="r2">3257,1096,220,64</rect>
					<rect id="r3">1667,375,312,103</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">722,858,60,38</rect>
					<rect label="addCharger">2736,858,60,38</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_5">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<event id="e_win">
							<condition delay="2">enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="ShuiSheng_king2">
				<info enemyLv="58"/>
				<!-- 基本属性 -->
				<sceneLabel>ShuiSheng</sceneLabel>
				<fixed target="ShuiSheng_3" info="no" drop="all" unitG="all" rectG="all" eventG="no"/>
				<eventG>
					<group>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_5">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order>
						</event>
						<event id="e_win">
							<condition delay="2">enemyNumber:less_1</condition>
							<order>task:now; complete</order>
							<order>worldMap:levelName; ShuiSheng:ShuiSheng_4</order>
							<order>worldMap:levelName; FengWei:FengWei_5</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>	
			<level name="ShuiSheng_4">
				<info enemyLv="58"/>
				<!-- 基本属性 -->
				<sceneLabel>ShuiSheng</sceneLabel>
				<fixed target="ShuiSheng_3" info="no" drop="all" unitG="all" rectG="all" eventG="all"/>
			</level>
		</gather>	
		
		<gather name="百丈道关卡">
			<level name="BaiZhang_1"><!-- 有任务 -->
				<!-- 关卡数据 -->
				<info enemyLv="9" diff="0.8"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiZhang</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<!-- 我方 -->
					<unitOrder id="we1" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="藏师将军" num="1" dpsMul="1.5" aiOrder="followBodyAttack:我" levelSetting="hero+10" lifeMul="999999" noUnderHurtB="1" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="0.1"/>
						<unit cnName="僵尸炮兵总管" num="0.05"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="3.5" />
						<unit cnName="橄榄僵尸" num="3.5"/>
						<unit cnName="屠刀僵尸" num="3.5" />
						<unit cnName="僵尸突击兵" num="0.1"/>
						<unit cnName="僵尸炮兵总管" num="0.05"  />
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="5" />
						<unit cnName="橄榄僵尸" num="5"/>
						<unit cnName="屠刀僵尸" num="5" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.3" />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="战斗僵尸" num="1" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">747,171,198,71</rect>
					<rect id="r_over">2948,308,70,109</rect>
					<rect id="r1">15,353,274,71</rect>
					<rect id="r2">2703,353,274,71</rect>
					<rect id="r3">1346,-64,300,72</rect>
					<rect id="r_back">-300,300,20,71</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">1476,933,60,38</rect>
					<rect label="addCharger">1453,16,60,38</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
							<order>worldMap:save; dropRocketB:1</order><!-- 允许掉落火箭炮 -->
						</event>
						<!-- 添加任务-->
						<event id="e2_0">
							<condition delay="1"></condition>
							<order>task; getAndSetNowTaskData:BaiZhang_1</order>
						</event>
						<!-- 2秒后发兵 -->
						<event id="e1_1"><condition delay="2"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="4">enemyNumber:less_1</condition></event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1"><condition delay="4">enemyNumber:less_1</condition></event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<event id="e_killAllEnemy">
							<condition>enemyNumber:less_1</condition>
						</event>
						<!-- 任务成功 -->
						<event id="e_taskWin">
							<condition>killEnemyNum:more; 我:藏师将军</condition>
							<order>task:BaiZhang_1; complete</order>
							<order>level; rebirthAllMore</order>
						</event>
						<event id="e2_8">
							<condition>bodyGap:less_350; 我:藏师将军</condition>
							<order>say; startList:s3</order>
						</event>
						<event id="e2_9">
							<condition>say:listOver; s3</condition>
							<order>body:藏师将军; rangeLimit:close</order>
							<order>body:藏师将军; followPoint:r_back</order>
							<order>worldMap:levelName; BaiZhang:BaiZhang_2</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					
					<!-- 任务失败 -->
					<group>
						<event id="e2_5">
							<condition>affterDelLevelEvent:e_killAllEnemy</condition>
						</event>
						<event id="e_task_fail">
							<condition>killEnemyNum:noMore; 我:藏师将军</condition>
							<order>task:BaiZhang_1; fail</order>
							<order>level; rebirthAllMore</order>
						</event>
						<event id="e2_8">
							<condition>bodyGap:less_350; 我:藏师将军</condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e_fail">
							<condition delay="1">say:listOver; s2</condition>
							<order>level; fail</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="BaiZhang_2">
				<!-- 关卡数据 -->
				<info enemyLv="9" diff="0.8"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiZhang</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
				
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="0.1"/>
						<unit cnName="僵尸炮兵总管" num="0.05"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="3.5" />
						<unit cnName="橄榄僵尸" num="3.5"/>
						<unit cnName="屠刀僵尸" num="3.5" />
						<unit cnName="僵尸突击兵" num="0.1"/>
						<unit cnName="僵尸炮兵总管" num="0.05" />
					</unitOrder>
					<unitOrder id="enemy3" mustSuperB="1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="5" />
						<unit cnName="橄榄僵尸" num="5"/>
						<unit cnName="屠刀僵尸" num="5" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">747,171,198,71</rect>
					<rect id="r_over">2948,308,70,109</rect>
					<rect id="r1">15,353,274,71</rect>
					<rect id="r2">2703,353,274,71</rect>
					<rect id="r3">1346,-64,300,72</rect>
					<rect id="r_back">-300,300,20,71</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">1476,933,60,38</rect>
					<rect label="addCharger">1453,16,60,38</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!-- 2秒后发兵 -->
						<event id="e1_1"><condition delay="2"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="4">enemyNumber:less_1</condition></event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1"><condition delay="4">enemyNumber:less_1</condition></event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_4">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
						
					</group>
					
					
				</eventG>
			</level>
			<level name="BaiZhang_train">
				<!-- 关卡数据 -->
				<info enemyLv="24"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiZhang</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="雇佣兵" num="3" lifeMul="12" dpsMul="1.3" dropLabel="no" skillArr="playerSkill"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="沙漠特种兵" num="1" lifeMul="1.5" dpsMul="1.3"  unitType="boss" dropLabel="no" skillArr="playerSkill" dieGotoState="stru" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">747,171,198,71</rect>
					<rect id="r_over">2948,308,70,109</rect>
					<rect id="r1">15,353,274,71</rect>
					<rect id="r2">2703,353,274,71</rect>
					<rect id="r3">1346,-64,300,72</rect>
					<rect id="r_back">-300,300,20,71</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">1476,933,60,38</rect>
					<rect label="addCharger">1453,16,60,38</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!-- 2秒后发兵 -->
						<event id="e2_2">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition delay="0.5">bodyEvent:die; 沙漠特种兵</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_11">
							<condition>say:listOver; s1</condition>
							<order>task:BaiZhang_train; complete</order>
							<order>worldMap:levelName; BaiZhang:BaiZhang_3</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
					
					
				</eventG>
			</level>
			<level name="BaiZhang_3">
				<!-- 关卡数据 -->
				<info enemyLv="25"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiZhang</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="0.1"/>
						<unit cnName="僵尸炮兵总管" num="0.05"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1.5" />
						<unit cnName="橄榄僵尸" num="3.5"/>
						<unit cnName="屠刀僵尸" num="3.5" />
						<unit cnName="僵尸突击兵" num="0.1"/>
						<unit cnName="僵尸炮兵总管" num="0.05" />
					</unitOrder>
					<unitOrder id="enemy3" mustSuperB="1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="橄榄僵尸" num="5"/>
						<unit cnName="屠刀僵尸" num="5" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
				</unitG>
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">2824,324,144,72</rect>
					<rect id="r_over">2948,308,70,109</rect>
					<rect id="r1">15,353,274,71</rect>
					<rect id="r2">2703,353,274,71</rect>
					<rect id="r3">1346,-64,300,72</rect>
					<rect id="r_back">-300,300,20,71</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">1476,933,60,38</rect>
					<rect label="addCharger">1453,16,60,38</rect>
				</rectG>
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<!-- 2秒后发兵 -->
						<event id="e1_1"><condition delay="0.01"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="0.01">enemyNumber:less_1</condition></event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1"><condition delay="0.01">enemyNumber:less_1</condition></event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_4">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="BaiZhang_key">
				<info enemyLv="57"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiZhang</sceneLabel>
				<fixed target="BaiZhang_3" info="no" drop="all" unitG="all" rectG="all" eventG="no"/>
				<eventG>
					<group>
						<!-- 2秒后发兵 -->
						<event id="e1_1"><condition delay="2"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="4">enemyNumber:less_1</condition></event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1"><condition delay="4">enemyNumber:less_1</condition></event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_11">
							<condition delay="1">enemyNumber:less_1</condition>
							<order>level; rebirthAllMore</order>
							<order>say; startList:s1</order>
						</event>	
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s1</condition>
							<order>task:now; complete</order>
							<order>worldMap:levelName; BaiZhang:BaiZhang_4</order>
							
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="BaiZhang_4">
				<info enemyLv="57"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiZhang</sceneLabel>
				<fixed target="BaiZhang_3" info="no" drop="all" unitG="all" rectG="all" eventG="all"/>
			</level>
			
			<level name="BaiZhang_re">
				<!-- 关卡数据 -->
				<info enemyLv="13" diff="0.9"  diy="wotuBack" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1"/>
				<drop arms="0" equip="0"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiZhang</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="4" dpsMul="15"  armsRange="sniperRifle,shotgun1" skillArr="crazy_hero_5,murderous_hero_2,groupLight_hero_5,hiding_hero_3" />
					</unitOrder>
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="亚瑟" lifeMul="99999" dpsMul="23" noUnderHurtB="1" num="1" aiOrder="followBodyAttack:本我" />
					</unitOrder>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="0.1"/>
						<unit cnName="僵尸炮兵总管" num="0.05"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="3.5" />
						<unit cnName="橄榄僵尸" num="3.5"/>
						<unit cnName="屠刀僵尸" num="3.5" />
						<unit cnName="僵尸突击兵" num="0.1"/>
						<unit cnName="僵尸炮兵总管" num="0.05"  />
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="5" />
						<unit cnName="橄榄僵尸" num="5"/>
						<unit cnName="屠刀僵尸" num="5" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.3" />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="战斗僵尸" num="1" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">747,171,198,71</rect>
					<rect id="r_over">2948,308,70,109</rect>
					<rect id="r1">15,353,274,71</rect>
					<rect id="r2">2703,353,274,71</rect>
					<rect id="r3">1346,-64,300,72</rect>
					<rect id="r_back">-300,300,20,71</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">1476,933,60,38</rect>
					<rect label="addCharger">1453,16,60,38</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>heroEverParasitic:本我</order>
							<order>P2EverParasitic:无</order>
							<order>createUnit:we2; r1</order>
						</event>
						<event id="e1_1"><condition>bodyGap:less_350; 本我:亚瑟</condition><order>say; startList:s1</order></event>
						<event id="e2_0"><condition>say:listOver; s1</condition></event><event id="e2_1"><condition delay="1"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="4">enemyNumber:less_1</condition></event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1"><condition delay="4">enemyNumber:less_1</condition></event>
						
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<event id="e_killAllEnemy">
							<condition>enemyNumber:less_1</condition>
						</event>
						<!-- 任务成功 -->
						<event id="e_taskWin">
							<condition>killEnemyNum:more; 我:亚瑟</condition>
							<order>task:BaiZhang_re; complete</order>
						</event>
						<event id="e2_8">
							<condition delay="0.5">bodyGap:less_350; 本我:亚瑟</condition>
							<order>say; startList:s3</order>
						</event>
						<event id="e2_9">
							<condition>say:listOver; s3</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					
					<!-- 任务失败 -->
					<group>
						<event id="e2_5">
							<condition>affterDelLevelEvent:e_killAllEnemy</condition>
						</event>
						<event id="e_task_fail">
							<condition>killEnemyNum:noMore; 我:亚瑟</condition>
						</event>
						<event id="e2_8">
							<condition>bodyGap:less_350; 本我:亚瑟</condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e_fail">
							<condition delay="1">say:listOver; s2</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.03"></condition>
							<order>level; fail</order>
						</event>
					</group>
					
					<group>
						<!-- 死亡 任务失败 -->
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; 本我</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.03"></condition>
							<order>level; fail</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>	
		
		<gather name="猪头洋关卡">
			<level name="ZhuTou_1">
				<!-- 关卡数据 -->
				<info enemyLv="10" diff="0.9"/>
				<!-- 基本属性 -->
				<sceneLabel>ZhuTou</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="藏师将军" num="1" lifeMul="1.5" dpsMul="1.4" unitType="boss" dieGotoState="stru" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1426,1030,142,52</rect>
					<rect id="r_over">2958,976,70,109</rect>
					<rect id="r1">20,954,266,85</rect>
					<rect id="r2">2700,954,266,85</rect>
					<rect id="r3">1344,433,295,85</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">343,956,78,47</rect>
					<rect label="addCharger">2567,956,78,47</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition delay="1" >enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r2</order>
						</event>
						<!-- 藏师死后 重生，说话 -->
						<event id="e1_2">
							<condition delay="1">bodyEvent:die; 藏师将军</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e_win">
							<condition>say:listOver; s1</condition>
							<order>addItems: {"type":"things","name":"skillStone","num":2,"tipB":true}</order>
							<order>worldMap:levelName; ZhuTou:ZhuTou_2</order>
						</event>
						<event id="e_win">
							<condition delay="1"></condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e_win">
							<condition>say:listOver; s2</condition>
							
							<order>worldMap:levelName; ZhuTou:ZhuTou_2</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="ZhuTou_2">
				<!-- 关卡数据 -->
				<info enemyLv="10" />
				<!-- 基本属性 -->
				<sceneLabel>ZhuTou</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.2"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="1"/>
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="0.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="屠刀僵尸" num="4" />
						<unit cnName="僵尸突击兵" num="1.5"/>
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸空军总管" unitType="boss"  num="1"/>
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1426,1030,142,52</rect>
					<rect id="r_over">2958,976,70,109</rect>
					<rect id="r1">20,654,266,85</rect>
					<rect id="r2">2700,654,266,85</rect>
					<rect id="r3">1344,433,295,85</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">343,956,78,47</rect>
					<rect label="addCharger">2567,956,78,47</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!-- 2秒后发兵 -->
						<event id="e1_1"><condition delay="2"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="2">enemyNumber:less_1</condition></event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1"><condition delay="2">enemyNumber:less_1</condition></event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="ZhuTou_war">
				<!-- 关卡数据 -->
				<info enemyLv="25" />
				<!-- 基本属性 -->
				<sceneLabel>ZhuTou</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="无头自爆僵尸" num="1" />
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="无头自爆僵尸" num="3" />
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="无头自爆僵尸" num="3" />
						<unit cnName="肥胖僵尸" num="2"/>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="1"/>
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸空军总管" num="1" unitType="boss" />
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1426,1030,142,52</rect>
					<rect id="r_over">2958,976,70,109</rect>
					<rect id="r1">20,654,266,85</rect>
					<rect id="r2">2700,654,266,85</rect>
					<rect id="r3">1344,433,295,85</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">343,956,78,47</rect>
					<rect label="addCharger">2567,956,78,47</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!-- 2秒后发兵 -->
						<event id="e1_1"><condition delay="2"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="2">enemyNumber:less_1</condition></event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1"><condition delay="2">enemyNumber:less_1</condition></event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_11">
							<condition>say:listOver; s1</condition>
							<order>task:ZhuTou_war; complete</order>
							<order>worldMap:levelName; ZhuTou:ZhuTou_3</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
			<level name="ZhuTou_3">
				<!-- 关卡数据 -->
				<info enemyLv="26" />
				<!-- 基本属性 -->
				<sceneLabel>ZhuTou</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="0.5"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"/>
						<unit cnName="僵尸炮兵总管" num="0.2"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="1"/>
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="0.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸空军总管" num="1" unitType="boss" />
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1426,1030,142,52</rect>
					<rect id="r_over">2958,976,70,109</rect>
					<rect id="r1">20,654,266,85</rect>
					<rect id="r2">2700,654,266,85</rect>
					<rect id="r3">1344,433,295,85</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">343,956,78,47</rect>
					<rect label="addCharger">2567,956,78,47</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!-- 2秒后发兵 -->
						<event id="e1_1"><condition delay="0.01"></condition></event>
						<![CDATA[]]>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="0.01">enemyNumber:less_1</condition></event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1"><condition delay="0.01">enemyNumber:less_1</condition></event>
						<event id="e2_4">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>	
		
		<gather name="双塔村关卡">
			<level name="ShuangTa_1">
				<!-- 关卡数据 -->
				<info enemyLv="11" diff="0.9"/>
				<!-- 基本属性 -->
				<sceneLabel>ShuangTa</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="亚瑟" num="1" aiOrder="followBody:我" />
						<unit cnName="天鹰特种兵" num="4" aiOrder="followBody:亚瑟" />
					</unitOrder>
					<unitOrder id="we3" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="藏师将军" num="1" aiOrder="followBody:我" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="僵尸突击兵" num="0.8"/>
						<unit cnName="僵尸狙击兵" num="0.8"/>
						<unit cnName="僵尸暴枪兵" num="0.8"/>
						<unit cnName="僵尸炮兵总管" num="0.8"/>
						<unit cnName="屠刀僵尸" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="屠刀僵尸" num="1" unitType="boss" />
						<unit cnName="屠刀僵尸" num="2" unitType="super" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">813,678,179,70</rect>
					<rect id="r_over">1940,1000,56,111</rect>
					<rect id="r1">28,1030,270,71</rect>
					<rect id="r2">1800,1030,270,71</rect>
					<rect id="r3">600,-250,270,71</rect>
					<rect id="r4">1466,-250,270,71</rect>
					<rect id="r_hide">-300,1030,270,71</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">316,613,83,40</rect>
					<rect label="addCharger">1746,800,83,40</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<![CDATA[]]>
						<event id="e1_1">
							<condition doNumber="16" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
							<order>createUnit:enemy1; r4</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
							<order>createUnit:enemy2; r4</order>
						</event>
						<!-- 联合国军队出现 -->
						<event id="e_show1">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:we2; r2</order>
							<order>level; rebirthAllMore</order>
						</event>
						<event id="e2_8"><!-- 距离小于200才开始对话 -->
							<condition>bodyGap:less_350; 我:亚瑟</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e_9">
							<condition>say:listOver; s1</condition>
							<order>body:亚瑟; ai:patrolRandom</order><!-- 随机巡逻 -->
						</event>
						<!-- 表哥、藏师出现 -->
						<event id="e_show1">
							<condition delay="2"></condition>
							<order>createUnit:we3; r2</order>
						</event>
						<event id="e2_8"><!-- 距离小于200才开始对话 -->
							<condition>bodyGap:less_350; 我:藏师将军</condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e_win">
							<condition>say:listOver; s2</condition>
							<order>worldMap:levelName; ShuangTa:ShuangTa_2</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="ShuangTa_2">
				<!-- 关卡数据 -->
				<info enemyLv="11" diff="0.9" />
				<!-- 基本属性 -->
				<sceneLabel>ShuangTa</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="僵尸突击兵" num="0.8"/>
						<unit cnName="僵尸狙击兵" num="0.8"/>
						<unit cnName="僵尸暴枪兵" num="0.8"/>
						<unit cnName="僵尸炮兵总管" num="0.8"/>
						<unit cnName="屠刀僵尸" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="屠刀僵尸" num="1" unitType="boss" />
						<unit cnName="屠刀僵尸" num="2" unitType="super" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">813,678,179,70</rect>
					<rect id="r_over">1930,1000,56,111</rect>
					<rect id="r1">28,1030,270,71</rect>
					<rect id="r2">1800,1030,270,71</rect>
					<rect id="r3">600,-250,270,71</rect>
					<rect id="r4">1466,-250,270,71</rect>
					<rect id="r_hide">-300,1030,270,71</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">316,613,83,40</rect>
					<rect label="addCharger">1746,800,83,40</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition doNumber="10" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
							<order>createUnit:enemy1; r4</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
							<order>createUnit:enemy2; r4</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<!-- 双塔基地 -->
			<level name="ShuangTa_base">
				<!-- 关卡数据 -->
				<info enemyLv="22" />
				<!-- 基本属性 -->
				<sceneLabel>ShuangTa</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<!-- 我方 -->
					<unitOrder id="we1" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="制毒师" lifeMul="10" num="1" aiOrder="followBodyAttack:我" dieGotoState="stru"/>
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="无头自爆僵尸" num="2"/>
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="无头自爆僵尸" num="2"/>
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="无头自爆僵尸" num="3"/>
						<unit cnName="橄榄僵尸" num="3"/>
						<unit cnName="屠刀僵尸" num="3" />
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">813,678,179,70</rect>
					<rect id="r_over">1930,1000,56,111</rect>
					<rect id="r1">28,1030,270,71</rect>
					<rect id="r2">1800,1030,270,71</rect>
					<rect id="r3">600,-250,270,71</rect>
					<rect id="r4">1466,-250,270,71</rect>
					<rect id="r_hide">-300,1030,270,71</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">316,613,83,40</rect>
					<rect label="addCharger">1746,800,83,40</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<event id="e2_2">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_3">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e2_4">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; rebirthAllMore</order>
							<order>body:制毒师; rebirth</order>
							<order>say; startList:s1</order>
						</event>
						<!-- 开始对话 -->
						<event id="e_say">
							<condition>say:listOver; s1</condition>
							<order>task:ShuangTa_base; complete</order>
							<order>worldMap:levelName; ShuangTa:ShuangTa_toBase</order>
							<order>body:制毒师; ai:patrolRandom</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="ShuangTa_toBase">
				<!-- 关卡数据 -->
				<info enemyLv="25" />
				<!-- 基本属性 -->
				<sceneLabel>ShuangTa</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="制毒师" num="1" aiOrder="patrolRandom"/>
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">813,678,179,70</rect>
					<rect id="r_over">1930,1000,56,111</rect>
					<rect id="r1">28,1030,270,71</rect>
					<rect id="r2">1800,1030,270,71</rect>
					<rect id="r3">600,-250,270,71</rect>
					<rect id="r4">1466,-250,270,71</rect>
					<rect id="r_hide">-300,1030,270,71</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">316,613,83,40</rect>
					<rect label="addCharger">1746,800,83,40</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<order>createUnit:we2; r2</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="ShuangTa_defend">
				<!-- 关卡数据 -->
				<info enemyLv="27" />
				<!-- 基本属性 -->
				<sceneLabel>ShuangTa</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="制毒师" lifeMul="5"   aiOrder="followBodyAttack:我" dieGotoState="stru"/><!-- 特种兵，跟随目标为主角 -->
						<unit cnName="沙漠特种兵" lifeMul="1" num="1" aiOrder="followBodyAttack:我" />
						<unit cnName="雇佣兵" lifeMul="0.2" num="2" aiOrder="followBodyAttack:沙漠特种兵" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal" ></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy2">
						<unit cnName="无头自爆僵尸" num="3"/>
						<unit cnName="屠刀僵尸" num="2"/>
						<unit cnName="战斗僵尸" num="2"/>
						<unit cnName="僵尸空降兵" num="0.7"/>
						<unit cnName="僵尸空军总管" num="0.7"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"/>
					</unitOrder>
					<unitOrder id="enemy1">
						<unit cnName="无头自爆僵尸" num="3" skillArr="hiding_enemy"/>
						<unit cnName="肥胖僵尸" num="2" skillArr="hiding_enemy"/>
						<unit cnName="战斗僵尸" num="2" skillArr="hiding_enemy"/>
						<unit cnName="僵尸空降兵" num="0.7" skillArr="hiding_enemy"/>
						<unit cnName="僵尸空军总管" num="0.7" skillArr="hiding_enemy"/>
						<unit cnName="僵尸狙击兵" num="0.5" skillArr="hiding_enemy"/>
						<unit cnName="僵尸暴枪兵" num="0.5" skillArr="hiding_enemy"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">813,678,179,70</rect>
					<rect id="r_over">1930,1000,56,111</rect>
					<rect id="r1">28,1030,270,71</rect>
					<rect id="r2">1800,1030,270,71</rect>
					<rect id="r3">600,-250,270,71</rect>
					<rect id="r4">1466,-250,270,71</rect>
					<rect id="r_hide">-300,1030,270,71</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">316,613,83,40</rect>
					<rect label="addCharger">1746,800,83,40</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we2; r2</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1">
							<condition doNumber="9" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
							<order>createUnit:enemy1; r4</order>
						</event>
						
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>body:制毒师; rebirth</order>
							<order>level; rebirthAllMore</order>
							<order>say; startList:s1</order>
						</event>
						<!-- 开始对话 -->
						<event id="e_say">
							<condition>say:listOver; s1</condition>
							<order>task:ShuangTa_defend; complete</order>
							<order>worldMap:levelName; ShuangTa:ShuangTa_toBase2</order>
							<order>body:制毒师; ai:patrolRandom</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="ShuangTa_toBase2">
				<!-- 关卡数据 -->
				<info enemyLv="27" />
				<!-- 基本属性 -->
				<sceneLabel>ShuangTa</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="制毒师" num="1" aiOrder="patrolRandom"/>
						<unit cnName="沙漠特种兵" lifeMul="1" num="1" aiOrder="patrolRandom" />
						<unit cnName="雇佣兵" lifeMul="0.2" num="2" aiOrder="followBodyAttack:沙漠特种兵" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">813,678,179,70</rect>
					<rect id="r_over">1930,1000,56,111</rect>
					<rect id="r1">28,1030,270,71</rect>
					<rect id="r2">1800,1030,270,71</rect>
					<rect id="r3">600,-250,270,71</rect>
					<rect id="r4">1466,-250,270,71</rect>
					<rect id="r_hide">-300,1030,270,71</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">316,613,83,40</rect>
					<rect label="addCharger">1746,800,83,40</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<order>createUnit:we2; r2</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="ShuangTa_fall">
				<!-- 关卡数据 -->
				<info enemyLv="35" />
				<!-- 基本属性 -->
				<sceneLabel>ShuangTa</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="鬼目游尸" num="1"/>
						<unit cnName="冥刃游尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1"/>
						<unit cnName="无头自爆僵尸" num="1"/>
						<unit cnName="僵尸空降兵" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="冥刃游尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1"/>
						<unit cnName="僵尸空降兵" num="1"/>
						<unit cnName="鬼目射手" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">813,678,179,70</rect>
					<rect id="r_over">1930,1000,56,111</rect>
					<rect id="r1">28,1030,270,71</rect>
					<rect id="r2">1800,1030,270,71</rect>
					<rect id="r3">600,-250,270,71</rect>
					<rect id="r4">1466,-250,270,71</rect>
					<rect id="r_hide">-300,1030,270,71</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">316,613,83,40</rect>
					<rect label="addCharger">1746,800,83,40</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<event id="e1_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
							<order>createUnit:enemy1; r4</order>
						</event>
						<event id="e1_1">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
							<order>createUnit:enemy1; r4</order>
						</event>
						<event id="e1_1">
							<condition doNumber="5" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
							<order>createUnit:enemy1; r4</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
							<order>createUnit:enemy2; r4</order>
						</event>
						<event id="e_win">
							<condition delay="0.1">enemyNumber:less_1</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e_win">
							<condition delay="0.1">say:listOver; s1</condition>
							<order>worldMap:levelName; ShuangTa:ShuangTa_3</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="ShuangTa_3">
				<!-- 关卡数据 -->
				<info enemyLv="35" />
				<!-- 基本属性 -->
				<sceneLabel>ShuangTa</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="鬼目游尸" num="1"/>
						<unit cnName="冥刃游尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1"/>
						<unit cnName="无头自爆僵尸" num="1"/>
						<unit cnName="僵尸空降兵" num="1"/>
						<unit cnName="僵尸空军总管" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="鬼目射手" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">813,678,179,70</rect>
					<rect id="r_over">1930,1000,56,111</rect>
					<rect id="r1">28,1030,270,71</rect>
					<rect id="r2">1800,1030,270,71</rect>
					<rect id="r3">600,-250,270,71</rect>
					<rect id="r4">1466,-250,270,71</rect>
					<rect id="r_hide">-300,1030,270,71</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">316,613,83,40</rect>
					<rect label="addCharger">1746,800,83,40</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<event id="e1_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
							<order>createUnit:enemy1; r4</order>
						</event>
						<event id="e1_1">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
							<order>createUnit:enemy1; r4</order>
						</event>
						<event id="e1_1">
							<condition doNumber="5" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
							<order>createUnit:enemy1; r4</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
							<order>createUnit:enemy2; r4</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="ShuangTa_re">
				<!-- 关卡数据 -->
				<info enemyLv="11" diy="wotuBack" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1"/>
				<drop arms="0" equip="0"/>
				<!-- 基本属性 -->
				<sceneLabel>ShuangTa</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="4" dpsMul="16" armsRange="shotgun1,sniperRifle" skillArr="crazy_hero_5,groupLight_hero_5" />
						<unit cnName="文杰表哥" lifeMul="18" dpsMul="4" aiOrder="followBodyAttack:本我" dieGotoState="stru" skillArr="tenacious_hero_5" />
					</unitOrder>
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="亚瑟" num="1" aiOrder="followBody:文杰表哥" />
						<unit cnName="天鹰特种兵" num="4" aiOrder="followBody:亚瑟" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal" ></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="僵尸突击兵" num="0.8"/>
						<unit cnName="僵尸狙击兵" num="0.8"/>
						<unit cnName="僵尸暴枪兵" num="0.8"/>
						<unit cnName="僵尸炮兵总管" num="0.8"/>
						<unit cnName="屠刀僵尸" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="屠刀僵尸" num="1" unitType="boss" />
						<unit cnName="屠刀僵尸" num="2" unitType="super" />
					</unitOrder>
				</unitG>
				
			<rectG>
					<rect id="r_birth">813,678,179,70</rect>
					<rect id="r_over">1940,1000,56,111</rect>
					<rect id="r1">28,1030,270,71</rect>
					<rect id="r2">1800,1030,270,71</rect>
					<rect id="r3">600,-250,270,71</rect>
					<rect id="r4">1466,-250,270,71</rect>
					<rect id="r_hide">-300,1030,270,71</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">316,613,83,40</rect>
					<rect label="addCharger">1746,800,83,40</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>heroEverParasitic:本我</order>
							<order>P2EverParasitic:文杰表哥</order>
						</event>
						<event id="e1_1"><condition></condition><order>say; startList:s1</order></event>
						<event id="e2_0"><condition>say:listOver; s1</condition></event><event id="e2_1"><condition delay="1"></condition></event>
						<event id="e1_1">
							<condition doNumber="15" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
							<order>createUnit:enemy1; r4</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
							<order>createUnit:enemy2; r4</order>
						</event>
						<!-- 所有单位都死后，藏师部队出现 -->
						<event id="e_ZangShiShow">
							<condition delay="0.1">enemyNumber:less_1</condition>
							<order>level; rebirthWeStruHero</order>
							<order>say; startList:s2</order>
						</event>
						<event id="e_111">
							<condition delay="0.1">say:listOver; s2</condition>
							<order>createUnit:we2; r2</order>
						</event>
						<event id="e2_8"><!-- 距离小于200才开始对话 -->
							<condition>bodyGap:less_250; 文杰表哥:亚瑟</condition>
							<order>say; startList:s3</order>
						</event>
						<!--对话结束后胜利 -->
						<event id="e_win">
							<condition delay="0.1">say:listOver; s3</condition>
							<order>body:亚瑟; ai:patrolRandom</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					
					<group>
						<!-- 死亡 任务失败 -->
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; 本我</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.03"></condition>
							<order>level; fail</order>
						</event>
					</group>
				</eventG>
			</level>
			
		</gather>
		
		<gather name="北斗城关卡">
			<level name="BeiDou_1">
				<!-- 关卡数据 -->
				<info enemyLv="13" music="music_city"/>
				<!-- 基本属性 -->
				<sceneLabel>BeiDou</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<!-- 我方 -->
					<unitOrder id="we1" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="藏师将军" lifeMul="20" dpsMul="2" num="1" aiOrder="followBodyAttack:我" skillArr="pointBoom_hero_4,groupLight_hero_5" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="亚瑟" num="1" aiOrder="followBody:我" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="携弹僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸突击兵" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">2942,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">29,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2126,170,71,60</rect>
					<rect label="addCharger">1113,374,71,60</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<![CDATA[]]>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<!-- 联合国军队出现 -->
						<event id="e_show1">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:we2; r2</order>
							<order>level; rebirthAllMore</order>
						</event>
						<event id="e2_8"><!-- 距离小于200才开始对话 -->
							<condition>bodyGap:less_350; 我:亚瑟</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e_9">
							<condition>say:listOver; s1</condition>
							<order>arms; unlockSite:3</order><!-- 随机巡逻 -->
							<order>worldMap:levelName; BeiDou:BeiDou_2</order>
						</event>
						<event id="e_9">
							<condition delay="1"></condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e_9">
							<condition>say:listOver; s2</condition>
							<order>body:亚瑟; ai:patrolRandom</order><!-- 随机巡逻 -->
							<order>worldMap:levelName; BeiDou:BeiDou_2</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="BeiDou_2">
				<!-- 关卡数据 -->
				<info enemyLv="13" music="music_city"/>
				<!-- 基本属性 -->
				<sceneLabel>BeiDou</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="4" />
						<unit cnName="屠刀僵尸" num="4" />
						<unit cnName="携弹僵尸" num="4" />
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸突击兵" num="1" unitType="boss" />
					</unitOrder>
					
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">2942,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">400,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2126,170,71,60</rect>
					<rect label="addCharger">1113,374,71,60</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1"><condition>enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1"><condition>enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="BeiDou_mercenary">
				<!-- 关卡数据 -->
				<info enemyLv="23" music="music_city"/>
				<!-- 基本属性 -->
				<sceneLabel>BeiDou</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="沙漠特种兵" lifeMul="9999" num="1" aiOrder="followBodyAttack:我" noUnderHurtB="1"/>
						<unit cnName="雇佣兵" lifeMul="2" num="2" aiOrder="followBodyAttack:沙漠特种兵" />
					</unitOrder>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="无头自爆僵尸" num="0.5" />
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="携弹僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸突击兵" num="1" lifeMul="1.5" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">2942,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">29,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2126,170,71,60</rect>
					<rect label="addCharger">1113,374,71,60</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we2; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_2</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1">enemyNumber:less_6</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>say; startList:s1</order>
						</event>
						<!-- 开始对话 -->
						<event id="e_say">
							<condition>say:listOver; s1</condition>
							<order>task:BeiDou_mercenary; complete</order>
							<order>worldMap:levelName; BeiDou:BeiDou_3</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="BeiDou_3">
				<!-- 关卡数据 -->
				<info enemyLv="23" music="music_city" />
				<!-- 基本属性 -->
				<sceneLabel>BeiDou</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="无头自爆僵尸" num="3" />
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="携弹僵尸" num="2" />
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸突击兵" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">2942,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">29,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2126,170,71,60</rect>
					<rect label="addCharger">1113,374,71,60</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="BeiDou_goto">
				<!-- 关卡数据 -->
				<info enemyLv="43" music="music_city" />
				<!-- 基本属性 -->
				<sceneLabel>BeiDou</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="战斗僵尸" num="2.5" />
						<unit cnName="无头自爆僵尸" num="2" />
						<unit cnName="屠刀僵尸" num="2.5" />
						<unit cnName="携弹僵尸" num="2.5" />
						<unit cnName="毒蛛" num="2" />
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸突击兵" dpsMul="2" lifeMul="1.2" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">2942,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">29,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2126,170,71,60</rect>
					<rect label="addCharger">1113,374,71,60</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<event id="e1_1">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1"><condition delay="0.001">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1"><condition delay="0.001">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>task:BeiDou_goto; complete</order>
							<order>worldMap:levelName; BeiDou:BeiDou_goto</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="BeiDou_re">
				<!-- 关卡数据 -->
				<info enemyLv="13" music="music_city" diy="wotuBack" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" />
				<drop arms="0" equip="0"/>
				<!-- 基本属性 -->
				<sceneLabel>BeiDou</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="本我" lifeMul="6" dpsMul="18"  armsRange="sniperRifle,shotgun1" skillArr="crazy_hero_5,murderous_hero_2,groupLight_hero_5,hiding_hero_3" />
						<unit cnName="文杰表哥" lifeMul="18" dpsMul="4" aiOrder="followBodyAttack:本我" dieGotoState="stru" skillArr="tenacious_hero_5,feedback_hero_5" />
					</unitOrder>
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="藏师将军" lifeMul="7" aiOrder="followBodyAttack:本我" />
					</unitOrder>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="携弹僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸突击兵" num="1" unitType="boss" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">2942,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">29,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2126,170,71,60</rect>
					<rect label="addCharger">1113,374,71,60</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we3; r_birth</order>
							<order>heroEverParasitic:本我</order>
							<order>P2EverParasitic:文杰表哥</order>
							<order>createUnit:we2; r2</order>
						</event>
						<event id="e1_1"><condition>bodyGap:less_350; 本我:藏师将军</condition><order>say; startList:s1</order></event>
						<event id="e2_0"><condition>say:listOver; s1</condition></event><event id="e2_1"><condition delay="1"></condition></event>
						
						<![CDATA[]]>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<!-- 联合国军队出现 -->
						<event id="e_show1">
							<condition>enemyNumber:less_1</condition>
							<order>level; rebirthWeStruHero</order>
							<order>say; startList:s2</order>
						</event>
						<event id="e_9">
							<condition>say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					
					<group>
						<!-- 死亡 任务失败 -->
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; 本我</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.03"></condition>
							<order>level; fail</order>
						</event>
					</group>
					<group>
						<!-- 制毒师死亡 任务失败 -->
						<event id="e_fail">
							<condition delay="0.5">bodyEvent:die; 藏师将军</condition>
							<order>alert:yes; 藏师倒下，任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.3"></condition>
							<order>level; fail</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		
		<gather name="东山澳关卡">
			<level name="DongShan_1">
				<!-- 关卡数据 -->
				<info enemyLv="14" />
				<!-- 基本属性 -->
				<sceneLabel>DongShan</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="肥胖僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="无头自爆僵尸" num="0.5"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸空降兵" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="肥胖僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="无头自爆僵尸" num="3"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸空降兵" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸空降兵" unitType="boss" num="1" />
						<unit cnName="僵尸空降兵" unitType="super" lifeMul="0.5" dropLabel="no" num="2"  expMul="0.3" coinMul="0.3"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1426,1030,142,52</rect>
					<rect id="r_over">2958,976,70,109</rect>
					<rect id="r1">20,754,266,85</rect>
					<rect id="r2">2700,754,266,85</rect>
					<rect id="r3">1344,433,295,85</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">332,898,78,47</rect>
					<rect label="addCharger">2554,916,78,47</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<!--开始和表哥对话-->
						<event id="e1_1">
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
					
					<!-- 对话结束后，添加任务，产生敌人 ,随机2个命令中的一个-->
						<event id="e2_0">
							<condition>say:listOver; s1</condition>
						</event>
						
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="3">enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>worldMap:levelName;DongShan:DongShan_2</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="DongShan_2">
				<!-- 关卡数据 -->
				<info enemyLv="14" />
				<!-- 基本属性 -->
				<sceneLabel>DongShan</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="肥胖僵尸" num="3"/>
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="无头自爆僵尸" num="0.5"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="肥胖僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="无头自爆僵尸" num="3"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸空降兵" unitType="boss" num="1" />
						<unit cnName="僵尸空降兵" unitType="super" lifeMul="0.3" dropLabel="no" num="2"  expMul="0.3" coinMul="0.3"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1426,1030,142,52</rect>
					<rect id="r_over">2958,976,70,109</rect>
					<rect id="r1">20,754,266,85</rect>
					<rect id="r2">2700,754,266,85</rect>
					<rect id="r3">1344,433,295,85</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">332,898,78,47</rect>
					<rect label="addCharger">2554,916,78,47</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1">
							<condition>enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e1_1">
							<condition orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="DongShan_QiHuang">
				<!-- 关卡数据 -->
				<info enemyLv="28" />
				<!-- 基本属性 -->
				<sceneLabel>DongShan</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="肥胖僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="无头自爆僵尸" num="0.5"/>
						<unit cnName="僵尸空降兵" num="0.5"/>
						<unit cnName="僵尸空军总管" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="无头自爆僵尸" num="3"/>
						<unit cnName="僵尸空降兵" num="0.5"/>
						<unit cnName="僵尸空军总管" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="奇皇博士" dieGotoState="stru" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1426,1030,142,52</rect>
					<rect id="r_over">2958,976,70,109</rect>
					<rect id="r1">20,754,266,85</rect>
					<rect id="r2">2700,754,266,85</rect>
					<rect id="r3">1344,433,295,85</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">332,898,78,47</rect>
					<rect label="addCharger">2554,916,78,47</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="3">enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="1" doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition delay="0.5">bodyEvent:die; 奇皇博士</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s1</condition>
							<order>body:奇皇博士; rebirth</order>
							<order>body:奇皇博士; toDie:die</order>
							<order>say; startList:s2</order>
						</event>	
						<event id="e2_11">
							<condition>say:listOver; s2</condition>
							<order>task:DongShan_QiHuang; complete</order>
							<order>worldMap:levelName; DongShan:DongShan_3</order>
							<order>worldMap:levelName; YangMei:YangMei_3</order>
							<order>worldMap:levelName; XiaSha:XiaSha_3</order>
							<order>worldMap:levelName; FengWei:FengWei_3</order>
							<order>worldMap:levelName; QingSha:QingSha_3</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
			<level name="DongShan_3">
				<!-- 关卡数据 -->
				<info enemyLv="28" />
				<!-- 基本属性 -->
				<sceneLabel>DongShan</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="肥胖僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="无头自爆僵尸" num="0.5"/>
						<unit cnName="僵尸空降兵" num="0.5"/>
						<unit cnName="僵尸空军总管" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="无头自爆僵尸" num="3"/>
						<unit cnName="僵尸空降兵" num="0.5"/>
						<unit cnName="僵尸空军总管" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸空降兵" unitType="boss" num="1" />
						<unit cnName="僵尸空降兵" unitType="super" lifeMul="0.3" dropLabel="no" num="2"  expMul="0.3" coinMul="0.3"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1426,1030,142,52</rect><rect id="r_over">2958,976,70,109</rect><rect id="r1">20,754,266,85</rect><rect id="r2">2700,754,266,85</rect><rect id="r3">1344,433,295,85</rect><rect label="addCharger">332,898,78,47</rect><rect label="addCharger">2554,916,78,47</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="3">enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="DongShan_lost">
				<!-- 关卡数据 -->
				<info enemyLv="52"/>
				<!-- 基本属性 -->
				<sceneLabel>DongShan</sceneLabel>
				<fixed target="DongShan_4" info="no" drop="all" unitG="all" rectG="all" eventG="no"/>
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we2; r2</order>
						</event>
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_7</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="3">enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_10</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="1" orderChooseType="randomOne">enemyNumber:less_10</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_11">
							<condition delay="1">enemyNumber:less_1</condition>
							<order>level; rebirthAllMore</order>
							<order>body:奇皇博士; rebirth</order>
							<order>body:奇皇博士; rangeLimit:close</order>
							<order>body:奇皇博士; followPoint:r_back</order>
							<order>say; startList:s1</order>
						</event>	
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s1</condition>
							<order>task:now; complete</order>
							<order>worldMap:levelName; DongShan:DongShan_4</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>	
			<level name="DongShan_4">
				<!-- 关卡数据 -->
				<info enemyLv="52" />
				<!-- 基本属性 -->
				<sceneLabel>DongShan</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					<!-- 我方 -->
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="奇皇博士" skillArr="feedback_enemy,recovery_enemy,groupLight_enemy" aiOrder="followBodyAttack:我" warningRange="99999" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="7" skillArr="State_AddMove" lifeMul="0.8" />
						<unit cnName="肥胖僵尸" num="0.5"/>
						<unit cnName="屠刀僵尸" num="0.5" />
						<unit cnName="僵尸狙击兵" num="0.1"/>
						<unit cnName="僵尸暴枪兵" num="0.1"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="16" skillArr="State_AddMove" lifeMul="0.8"/>
						<unit cnName="肥胖僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="防暴僵尸" unitType="boss" hurtMul="2"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1426,1030,142,52</rect><rect id="r_over">2958,976,70,109</rect><rect id="r_back">3658,976,70,109</rect><rect id="r1">20,754,266,85</rect><rect id="r2">2700,754,266,85</rect><rect id="r3">1344,433,295,85</rect><rect label="addCharger">332,898,78,47</rect><rect label="addCharger">2554,916,78,47</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<event id="e1_1">
							<condition delay="0.01" doNumber="5" orderChooseType="randomOne">enemyNumber:less_7</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1">
							<condition delay="0.01">enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition delay="0.01" doNumber="3" orderChooseType="randomOne">enemyNumber:less_10</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
						</event>
						<event id="e1_1">
							<condition delay="0.01" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>	
		<gather name="青纱镇关卡">
			<level name="QingSha_1">
				<!-- 关卡数据 -->
				<info enemyLv="15" />
				<!-- 基本属性 -->
				<sceneLabel>QingSha</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="藏师将军" lifeMul="2" dpsMul="20" num="1" aiOrder="followBodyAttack:我" skillArr="pointBoom_hero_4,groupLight_hero_5" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="we3" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="亚瑟" num="1" aiOrder="followBody:我"  skillArr="pointBoom_hero_4"/>
						<unit cnName="天鹰特种兵" num="2" aiOrder="followBody:亚瑟" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1.5" />
						<unit cnName="肥胖僵尸" num="1.5"/>
						<unit cnName="屠刀僵尸" num="1.5" />
						<unit cnName="无头自爆僵尸" num="2"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1.5" />
						<unit cnName="肥胖僵尸" num="1.5"/>
						<unit cnName="屠刀僵尸" num="1.5" />
						<unit cnName="无头自爆僵尸" num="4"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="沙漠特种兵" unitType="boss" skillArr="paralysis_enemy,groupSpeedUp_enemy,feedback_enemy" dieGotoState="stru"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">2241,638,238,85</rect>
					<rect id="r_over">3860,610,80,106</rect>
					<rect id="r1">30,582,388,112</rect>
					<rect id="r2">3600,582,388,112</rect>
					<rect id="r3">2137,-76,342,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">961,492,84,66</rect>
					<rect label="addCharger">3348,484,84,66</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we2; r1</order>
						</event>
						
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="3">enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition delay="1" doNumber="4" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						
						<event id="e1_1">
							<condition delay="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e1_2">
							<condition delay="1">bodyEvent:die; 沙漠特种兵</condition>
							<order>say; startList:s1</order>
						</event>
						
						<event id="e1_2">
							<condition delay="0.2">say:listOver; s1</condition>
							<order>createUnit:we3; r3</order>
							<order>body:沙漠特种兵; rebirth</order>
							<order>body:亚瑟; setShootXY:沙漠特种兵</order>
							<order>body:亚瑟; doSkill:pointBoom_hero_4</order>
							<order>body:沙漠特种兵;toDie:die</order>
						</event>
						<event id="e1_2">
							<condition delay="1.5"></condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e_win">
							<condition>say:listOver; s2</condition>
							<order>worldMap:levelName;QingSha:QingSha_2</order>
							<order>body:亚瑟; ai:patrolRandom</order><!-- 随机巡逻 -->
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="QingSha_2">
				<!-- 关卡数据 -->
				<info enemyLv="15" />
				<!-- 基本属性 -->
				<sceneLabel>QingSha</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1.5" />
						<unit cnName="肥胖僵尸" num="1.5"/>
						<unit cnName="屠刀僵尸" num="1.5" />
						<unit cnName="无头自爆僵尸" num="2"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1.5" />
						<unit cnName="肥胖僵尸" num="1.5"/>
						<unit cnName="屠刀僵尸" num="1.5" />
						<unit cnName="无头自爆僵尸" num="4"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"  />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">2241,638,238,85</rect>
					<rect id="r_over">3860,610,80,106</rect>
					<rect id="r1">30,582,388,112</rect>
					<rect id="r2">3600,582,388,112</rect>
					<rect id="r3">2137,-76,342,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">961,492,84,66</rect>
					<rect label="addCharger">3348,484,84,66</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="3">enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="QingSha_3">
				<!-- 关卡数据 -->
				<info enemyLv="27" />
				<!-- 基本属性 -->
				<sceneLabel>QingSha</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1.5" />
						<unit cnName="肥胖僵尸" num="1.5"/>
						<unit cnName="屠刀僵尸" num="1.5" />
						<unit cnName="无头自爆僵尸" num="2"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1.5" />
						<unit cnName="肥胖僵尸" num="1.5"/>
						<unit cnName="屠刀僵尸" num="1.5" />
						<unit cnName="无头自爆僵尸" num="4"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"  />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">2241,638,238,85</rect>
					<rect id="r_over">3860,610,80,106</rect>
					<rect id="r1">30,582,388,112</rect>
					<rect id="r2">3600,582,388,112</rect>
					<rect id="r3">2137,-76,342,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">961,492,84,66</rect>
					<rect label="addCharger">3348,484,84,66</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="3">enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="QingSha_clone">
				<!-- 关卡数据 -->
				<info enemyLv="34" />
				<!-- 基本属性 -->
				<sceneLabel>QingSha</sceneLabel>
				<drop coin="0.4" exp="0.4" arms="0.5" equip="0.5" skillStone="0.4" taxStamp="0.3"/>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="15" skillArr="State_AddMove" lifeMul="0.8" />
						<unit cnName="肥胖僵尸" num="0.5"/>
						<unit cnName="屠刀僵尸" num="0.5" />
						<unit cnName="无头自爆僵尸" num="0.5"/>
						<unit cnName="僵尸狙击兵" num="0.1"/>
						<unit cnName="僵尸暴枪兵" num="0.1"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="20" skillArr="State_AddMove" lifeMul="0.8"/>
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="无头自爆僵尸" num="1"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="奇皇博士" unitType="boss" dieGotoState="stru" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">2241,638,238,85</rect>
					<rect id="r_over">3860,610,80,106</rect>
					<rect id="r1">30,582,388,112</rect>
					<rect id="r2">3600,582,388,112</rect>
					<rect id="r3">2137,-76,342,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">961,492,84,66</rect>
					<rect label="addCharger">3348,484,84,66</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="3">enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_10</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="1" orderChooseType="randomOne">enemyNumber:less_10</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition delay="0.5">bodyEvent:die; 奇皇博士</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s1</condition>
							<order>body:奇皇博士; rebirth</order>
							<order>body:奇皇博士; toDie:die</order>
						</event>	
						<event id="e_win">
							<condition delay="0.5"></condition>
							<order>worldMap:levelName; QingSha:QingSha_4</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="QingSha_4">
				<!-- 关卡数据 -->
				<info enemyLv="34" />
				<!-- 基本属性 -->
				<sceneLabel>QingSha</sceneLabel>
				<drop coin="0.4" exp="0.4" arms="0.5" equip="0.5" skillStone="0.4" taxStamp="0.3"/>
				<!-- 发兵集 -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="15" skillArr="State_AddMove" lifeMul="0.8" />
						<unit cnName="肥胖僵尸" num="0.5"/>
						<unit cnName="屠刀僵尸" num="0.5" />
						<unit cnName="无头自爆僵尸" num="0.5"/>
						<unit cnName="僵尸狙击兵" num="0.1"/>
						<unit cnName="僵尸暴枪兵" num="0.1"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="20" skillArr="State_AddMove" lifeMul="0.8"/>
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="无头自爆僵尸" num="1"/>
						<unit cnName="僵尸狙击兵" num="0.5"/>
						<unit cnName="僵尸暴枪兵" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="奇皇博士" unitType="boss"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">2241,638,238,85</rect>
					<rect id="r_over">3860,610,80,106</rect>
					<rect id="r1">30,582,388,112</rect>
					<rect id="r2">3600,582,388,112</rect>
					<rect id="r3">2137,-76,342,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">961,492,84,66</rect>
					<rect label="addCharger">3348,484,84,66</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="3">enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_10</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="1" orderChooseType="randomOne">enemyNumber:less_10</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="QingSha_riots">
				<!-- 关卡数据 -->
				<info enemyLv="51" />
				<!-- 基本属性 -->
				<sceneLabel>QingSha</sceneLabel>
				<fixed target="QingSha_5" info="no" drop="all" unitG="all" rectG="all" eventG="no"/>
				<!-- 事件集 -->
				<eventG>
					<group>
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we2; r2</order>
						</event>
						<event id="e1_1">
							<condition delay="0.5" doNumber="5" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1">
							<condition delay="3">enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition delay="1" doNumber="5" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="1" orderChooseType="randomOne">enemyNumber:less_10</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e_win">
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s1</condition>
							<order>task:now; complete</order>
							<order>worldMap:levelName; QingSha:QingSha_5</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
			<level name="QingSha_5">
				<!-- 发兵集 -->
				<info enemyLv="50" />
				<sceneLabel>QingSha</sceneLabel>
				<unitG>
					<!-- 我方 -->
					<unitOrder id="we2" camp="we">
						<unit id="tianying" cnName="天鹰特种兵" num="1" lifeMul="0.3" dieGotoState="stru"/>
						<unit cnName="天鹰特种兵"  lifeMul="0.02" num="2" />
					</unitOrder>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="肥胖僵尸" num="2"/>
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="无头自爆僵尸" num="0.5"/>
						<unit cnName="僵尸空降兵" num="0.5"/>
						<unit cnName="僵尸空军总管" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="战斗僵尸" num="1" />
						<unit cnName="肥胖僵尸" num="1"/>
						<unit cnName="屠刀僵尸" num="1" />
						<unit cnName="无头自爆僵尸" num="3"/>
						<unit cnName="僵尸空降兵" num="0.5"/>
						<unit cnName="僵尸空军总管" num="0.5"  />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸空降兵" unitType="boss" num="1" lifeMul="2" />
					</unitOrder>
				</unitG>
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">2241,638,238,85</rect><rect id="r_over">3860,610,80,106</rect><rect id="r1">30,582,388,112</rect><rect id="r2">3600,582,388,112</rect><rect id="r3">2137,-76,342,106</rect>
					<rect label="addCharger">961,492,84,66</rect><rect label="addCharger">3348,484,84,66</rect>
				</rectG>
				<!-- 事件集 -->
				<eventG>
					<group>
						<event id="e1_1">
							<condition delay="0.01" doNumber="5" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1">
							<condition delay="0.01">enemyNumber:less_1</condition>
						</event>
						<event id="e1_1">
							<condition delay="0.01" doNumber="5" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition delay="0.01" orderChooseType="randomOne">enemyNumber:less_10</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>	
		</gather>
		
		<gather name="清明岭关卡">
			<level name="QingMing_1">
				<!-- 关卡数据 -->
				<info enemyLv="16" />
				<!-- 基本属性 -->
				<sceneLabel>QingMing</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="无头自爆僵尸" num="3" />
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="肥胖僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸空军总管" unitType="boss"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1850,820,330,106</rect>
					<rect id="r_over">-30,800,80,135</rect>
					<rect id="r1">20,50,330,106</rect>
					<rect id="r2">2768,192,330,106</rect>
					<rect id="r3">1280,46,330,106</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">2072,310,84,48</rect>
					<rect label="addCharger">122,868,84,66</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e1_1">
							<condition>say:listOver; s1</condition>
						</event>
						<![CDATA[]]>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition>enemyNumber:less_1</condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e_9">
							<condition>say:listOver; s2</condition>
							<order>worldMap:levelName;QingMing:QingMing_2</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="QingMing_2">
				<!-- 关卡数据 -->
				<info enemyLv="16" />
				<!-- 基本属性 -->
				<sceneLabel>QingMing</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="无头自爆僵尸" num="3" />
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="肥胖僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸空军总管" unitType="boss"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1850,820,330,106</rect>
					<rect id="r_over">-30,800,80,135</rect>
					<rect id="r1">20,50,330,106</rect>
					<rect id="r2">2768,192,330,106</rect>
					<rect id="r3">1280,46,330,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2072,310,84,48</rect>
					<rect label="addCharger">122,868,84,66</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_4</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
						
					</group>
				</eventG>
			</level>
			<level name="QingMing_Arthur">
				<!-- 关卡数据 -->
				<info enemyLv="30" />
				<!-- 基本属性 -->
				<sceneLabel>QingMing</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<!-- 我方 -->
					<unitOrder id="we1" camp="we"><!-- camp不填默认为enemy -->
						
						<unit cnName="僵尸王" lifeMul="15" skillArr="trueshot_enemy,paralysis_enemy" imgType="normal" aiOrder="followBodyAttack:我" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="无头自爆僵尸" num="3" />
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="橄榄僵尸" num="3" />
						<unit cnName="僵尸空降兵" num="1"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸空军总管" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="亚瑟" unitType="boss" skillArr="pointBoom_enemy,paralysis_enemy,selfBurn_enemy,imploding_enemy,sweep_shell,crazy_enemy" dieGotoState="stru" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1850,820,330,106</rect>
					<rect id="r_over">-30,800,80,135</rect>
					<rect id="r1">20,50,330,106</rect>
					<rect id="r2">2768,192,330,106</rect>
					<rect id="r3">1280,46,330,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2072,310,84,48</rect>
					<rect label="addCharger">122,868,84,66</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e_win">
							<condition delay="0.5">bodyEvent:die; 亚瑟</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s1</condition>
							<order>body:亚瑟; rebirth</order>
							<order>body:亚瑟; toDie:die</order>
							<order>task:QingMing_Arthur; complete</order>
							<order>worldMap:levelName; QingMing:QingMing_3</order>
							<order>level; showPointer:r_over</order>
						</event>	
						
					</group>
				</eventG>
			</level>
			<level name="QingMing_3">
				<!-- 关卡数据 -->
				<info enemyLv="30" />
				<!-- 基本属性 -->
				<sceneLabel>QingMing</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="无头自爆僵尸" num="3" />
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="橄榄僵尸" num="3" />
						<unit cnName="僵尸空降兵" num="1"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸空军总管" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="亚瑟" unitType="boss"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1850,820,330,106</rect>
					<rect id="r_over">-30,800,80,135</rect>
					<rect id="r1">20,50,330,106</rect>
					<rect id="r2">2768,192,330,106</rect>
					<rect id="r3">1280,46,330,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2072,310,84,48</rect>
					<rect label="addCharger">122,868,84,66</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="0.01">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="0.01">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_1">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
						
					</group>
				</eventG>
			</level>
			<level name="QingMing_find">
				<info enemyLv="61"/>
				<!-- 基本属性 -->
				<sceneLabel>QingMing</sceneLabel>
				<fixed target="QingMing_3" info="no" drop="all" unitG="all" rectG="all" eventG="no"/>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_5</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_6</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e2_11">
							<condition delay="1">enemyNumber:less_1</condition>
							<order>level; rebirthAllMore</order>
							<order>say; startList:s1</order>
						</event>	
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s1</condition>
							<order>task:now; complete</order>
							<order>worldMap:levelName; QingMing:QingMing_4</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="QingMing_4">
				<info enemyLv="61"/>
				<!-- 基本属性 -->
				<sceneLabel>QingMing</sceneLabel>
				<fixed target="QingMing_3" info="no" drop="all" unitG="all" rectG="all" eventG="all"/>
			</level>
			<!-- 大结局 -->
			<level name="QingMing_re">
				<!-- 关卡数据 -->
				<info enemyLv="9" diy="wotuBack" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" />
				<drop arms="0" equip="0"/>
				<!-- 基本属性 -->
				<sceneLabel>QingMing</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit cnName="藏师将军" armsRange="old_rocket22" lifeMul="2" dpsMul="4" skillArr="State_AddMove50" />
					</unitOrder>
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="财宝僵尸" lifeMul="0.15" noSuperB="1" num="1" skillArr="noSkillHurt,onlyUnderOldRocket" aiOrder="patrolRandomNoAttack"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1850,820,330,106</rect>
					<rect id="r_over">-30,800,80,135</rect>
					<rect id="r1">20,50,330,106</rect>
					<rect id="r2">2768,192,330,106</rect>
					<rect id="r3">1280,46,330,106</rect>
					<rect label="addCharger">2072,310,84,48</rect>
					<rect label="addCharger">122,868,84,66</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we3; r_birth</order>
							<order>heroEverParasitic:藏师将军</order>
							<order>onlyKeepArms:藏师将军;old_rocket22</order>
							<order>level;taskTimingB:false</order>
						</event>
						<event id="e1_1">
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e1_1">
							<condition>say:listOver; s1</condition>
							<order>level;taskTimingB:true</order>
						</event>
						<![CDATA[]]>
						<event id="e1_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_2</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition>enemyNumber:less_1</condition>
							<order>level;taskTimingB:false</order>
							<order>say; startList:s2</order>
						</event>
						<event id="e_9">
							<condition>say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			
		</gather>
		
		
		<gather name="南唐关卡">
			<level name="NanTang_1">
				<!-- 关卡数据 -->
				<info enemyLv="18" music="music_city"/>
				<!-- 基本属性 -->
				<sceneLabel>NanTang</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<unitOrder id="we2" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="亚瑟" num="1" aiOrder="followBody:我" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="肥胖僵尸" num="2" />
						<unit cnName="屠刀僵尸" num="2" />
						<unit cnName="携弹僵尸" num="2" />
						<unit cnName="橄榄僵尸" num="2"/>
						<unit cnName="无头自爆僵尸" num="2"/>
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					
					<unitOrder id="enemy3">
						<unit cnName="火炮僵尸王" dpsMul="0.8" num="1" unitType="boss"/>
					</unitOrder>
				</unitG>
				
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">3742,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">130,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					<rect id="r4">-290,-166,368,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2126,120,71,60</rect>
					<rect label="addCharger">1113,324,71,60</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<![CDATA[]]>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						
						<event id="e1_1">
							<condition orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<!-- 亚瑟出现 -->
						<event id="e_9">
							<condition>enemyNumber:less_1</condition>
							<order>level; rebirthAllMore</order>
							<order>createUnit:we2; r3</order>
						</event>
						<event id="e2_8"><!-- 距离小于200才开始对话 -->
							<condition>bodyGap:less_350; 我:亚瑟</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e_9">
							<condition>say:listOver; s1</condition>
							<order>body:亚瑟; rangeLimit:close</order>
							<order>body:亚瑟; followPoint:r4</order>
						</event>
						<!-- 藏师对话 -->
						<event id="e_9">
							<condition delay="1"></condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e_9">
							<condition>say:listOver; s2</condition>
							<order>task; add:BaiLu_ZangShi</order>
						</event>
						<event id="e_9">
							<condition></condition>
							<order>worldMap:levelName;NanTang:NanTang_2</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="NanTang_2">
				<!-- 关卡数据 -->
				<info enemyLv="18" music="music_city"/>
				<!-- 基本属性 -->
				<sceneLabel>NanTang</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="肥胖僵尸" num="3" />
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="携弹僵尸" num="3" />
						<unit cnName="橄榄僵尸" num="3"/>
						<unit cnName="无头自爆僵尸" num="3"/>
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					
					<unitOrder id="enemy3">
						<unit cnName="火炮僵尸王" num="1" unitType="boss"/>
					</unitOrder>
				</unitG>
				
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">3742,700,73,115</rect>
					<rect id="r1">1115,-114,408,106</rect>
					<rect id="r2">299,-166,368,106</rect>
					<rect id="r3">2618,-88,263,106</rect>
					<rect id="r4">1115,-114,408,10</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2126,120,71,60</rect>
					<rect label="addCharger">1113,324,71,60</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<![CDATA[]]>
						<event id="e1_1">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1"><condition>enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1"><condition>enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						
						<event id="e1_1">
							<condition orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<!-- 亚瑟出现 -->
						<event id="e_9">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="NanTang_dive">
				<!-- 关卡数据 -->
				<info enemyLv="26" music="music_city"/>
				<!-- 基本属性 -->
				<sceneLabel>NanTang</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					
					<unitOrder id="we3" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="藏师将军" lifeMul="3" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="携弹僵尸" num="3" />
						<unit cnName="橄榄僵尸" num="3"/>
						<unit cnName="无头自爆僵尸" num="3"/>
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸炮兵总管" num="0.3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸空降兵" unitType="boss"/>
						<unit cnName="携弹僵尸" unitType="boss" dropLabel="no" lifeMul="0.3" expMul="0.3" coinMul="0.3" />
					</unitOrder>
				</unitG>
				
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">3742,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">29,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					<rect id="r4">-290,-166,368,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2126,120,71,60</rect>
					<rect label="addCharger">1113,324,71,60</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						
						<![CDATA[]]>
						<event id="e1_1">
							<condition doNumber="6" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="3">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<!-- 亚瑟出现 -->
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s1</condition>
							<order>say; startList:s2</order>
						</event>	
						<event id="e2_11">
							<condition>say:listOver; s2</condition>
							<order>task:NanTang_dive; complete</order>
							<order>worldMap:levelName; NanTang:NanTang_3</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
					<group>
						<!-- 制毒师死亡 任务失败 -->
						<event id="e_fail">
							<condition delay="0.5">bodyEvent:die; 藏师将军</condition>
							<order>alert:yes; 藏师倒下，任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.3"></condition>
							<order>level; fail</order>
						</event>
					</group>
					
				</eventG>
			</level>
			<level name="NanTang_3">
				<!-- 关卡数据 -->
				<info enemyLv="26" music="music_city"/>
				<!-- 基本属性 -->
				<sceneLabel>NanTang</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					
					<!-- 我方 -->
					<unitOrder id="we3" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="小樱" lifeMul="4" aiOrder="followBodyAttack:我" dieGotoState="stru" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="携弹僵尸" num="4" />
						<unit cnName="橄榄僵尸" num="4"/>
						<unit cnName="无头自爆僵尸" num="4"/>
						<unit cnName="僵尸突击兵" num="0.3"/>
						<unit cnName="僵尸狙击兵" num="0.3"/>
						<unit cnName="僵尸暴枪兵" num="0.3"/>
						<unit cnName="僵尸空军总管" num="0.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="火炮僵尸王" num="1" unitType="boss"/>
					</unitOrder>
				</unitG>
				
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">3742,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">29,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					<rect id="r4">-290,-166,368,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">2126,120,71,60</rect>
					<rect label="addCharger">1113,324,71,60</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we1; r_birth</order>
						</event>
						<![CDATA[]]>
						<event id="e1_1">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1"><condition delay="0.01">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1"><condition delay="0.01">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e1_1">
							<condition orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
						</event>
						<!-- 亚瑟出现 -->
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			<level name="NanTang_XiaoYing">
				<!-- 关卡数据 -->
				<info enemyLv="26" music="music_city" noMoreB="1" />
				<fixed target="NanTang_3" info="no" drop="all" unitG="all" rectG="all" eventG="no"/>
				<!-- 基本属性 -->
				<sceneLabel>NanTang</sceneLabel>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event id="e1_1">
							<condition ></condition>
							<order>createUnit:we3; r1</order>
						</event>
						<![CDATA[]]>
						<event id="e1_1">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
						</event>
						<event id="e1_1"><condition delay="0.01">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1"><condition delay="0.01">enemyNumber:less_1</condition></event>
						<event id="e1_1">
							<condition doNumber="2" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_1">
							<condition orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<!-- boss死亡后出现对话 -->
						<event id="e_win">
							<condition>enemyNumber:less_1</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_11">
							<condition delay="0.1">say:listOver; s1</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
					<group>
						<!-- 小樱死亡 任务失败 -->
						<event id="e_fail">
							<condition delay="0.5">bodyEvent:die; 小樱</condition>
							<order>alert:yes; 小樱已倒下，任务失败！</order>
						</event>
						<event id="e_fail">
							<condition delay="0.3"></condition>
							<order>level; fail</order>
						</event>
					</group>
					
				</eventG>
			</level>
		</gather>
		
		
		
		
		
	</father>
</data>