<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="enemy">
		<body name="狂人机器" shell="compound">
			
			<name>Warrior</name>
			<cnName>狂人机器</cnName><headIconUrl>IconGather/Warrior</headIconUrl>
			<raceType>robot</raceType>
			<swfUrl>swf/enemy/Warrior.swf</swfUrl>
			<!-- 基本系数 -->
			<showLevel>999</showLevel>
			<lifeRatio>1.8</lifeRatio>
			<rateRatio>0.05</rateRatio>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgArr>
				stand,move,run
				,normalAttack,shootAttack,shakeAttack
				,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<lifeBarExtraHeight>-50</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>15</maxVx>
			<runStartVx>8</runStartVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr>WarriorShield,WarriorShoot,WarriorSprint,cmldef_enemy,fightReduct2,defenceBounce_enemy</bossSkillArr>
			<bossSkillArrCn>60%近战防御、80%防毒、胶性表皮</bossSkillArrCn>
			<extraDropArmsB>1</extraDropArmsB>
			<extraAIClassLabel>Warrior_AIExtra</extraAIClassLabel>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel><cn>竖劈</cn>
					<hurtRatio>3</hurtRatio>
					<attackType>direct</attackType>
					<skillArr></skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>3</hurtRatio>
					<attackType>holy</attackType>
					<bulletLabel>WarriorBullet</bulletLabel>
					<grapRect>-650,-200,380,200</grapRect>
					<skillArr>silenceWarrior,noMoveWarrior</skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shakeAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtMul>0.9</hurtMul>
					<attackType>holy</attackType>
					<skillArr></skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
		
		<bullet cnName="剑气">
			<name>WarriorBullet</name>
			<cnName>狂人机器剑气</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>7</bulletLife>
			<bulletWidth>70</bulletWidth>
			<hitType>rect</hitType>
			<skillArr>silenceWarrior</skillArr>
			<!--攻击时的属性------------------------------------------------------------ -->
			<twoHitGap>0.5</twoHitGap>
			<attackGap>1</attackGap>
			<attackDelay>0.9</attackDelay>
			<bulletAngle>179.9</bulletAngle>
			<bulletAngleRange>5</bulletAngleRange>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>				<!-- 1个攻击间隔内的射击次数（默认值为1）-
			->	
								
			<shootAngle>0</shootAngle>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-300,-74</shootPoint>
			<bulletSpeed>35</bulletSpeed>
			<penetrationNum>999</penetrationNum>
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="2" con="filter">Warrior/bullet</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/fitHit</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
	</father>
	
	<father name="other" cnName="其他">
		<skill name="狂人飞刃"><!-- dps -->
			<name>WarriorShoot</name><iconUrl36>SkillIcon/WarriorShoot_36</iconUrl36>
			<cnName>狂人飞刃</cnName><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>5</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>500</conditionRange><target>me</target>
			<addType>no</addType>
			<meActionLabel>shootAttack</meActionLabel>
			<description>狂人机器瞬移至敌人面前，发出狂人飞刃，被击中的目标将被沉默并且无法动弹。</description>
		</skill>
		<skill name="狂人斩"><!-- dps -->
			<name>WarriorSprint</name><iconUrl36>SkillIcon/WarriorSprint_36</iconUrl36>
			<cnName>狂人斩</cnName><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>11</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>700</conditionRange><target>me</target>
			<addType>no</addType>
			<meActionLabel>shakeAttack</meActionLabel>
			<description>狂人机器腾空而起，斩击地面的敌人，对其造成重大伤害！</description>
		</skill>
		<skill name="空虚装甲" cnName="空虚装甲"><!-- dps -->
			<name>WarriorShield</name>
			<cnName>空虚装甲</cnName><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<otherConditionArr>lifePerLess,standOrRun</otherConditionArr>
			<conditionRange>0.3</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>noUnderHitB</effectType>
			<duration>0.5</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="body" con="add">generalEffect/nothing</stateEffectImg>
			<description>生命值低于30%时并且在站立或者行走时开启空虚装甲，不受到任何物理攻击。</description>
		</skill>
		
		
		
		<skill index="0" name="沉默"><!-- dps -->
			<name>silenceWarrior</name>
			<cnName>沉默</cnName>
			<noBeClearB>1</noBeClearB>
			<ignoreImmunityB>1</ignoreImmunityB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>silenceB</effectType>
			<value>1</value>
			<duration>5</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">skillEffect/silence_enemy</stateEffectImg>
			<description>击中目标后使其无法释放技能，持续[duration]秒。</description>
		</skill>
		<skill index="0" name="麻痹"><!-- dps -->
			<name>noMoveWarrior</name>
			<cnName>麻痹</cnName>
			<noBeClearB>1</noBeClearB>
			<ignoreImmunityB>1</ignoreImmunityB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<mul>0</mul>
			<duration>3</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg con="add">skillEffect/paralysis_enemy</stateEffectImg>
		</skill>
	</father>
</data>