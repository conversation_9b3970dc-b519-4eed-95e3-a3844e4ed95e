<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="enemy" cnName="敌方">
		<body name="铁犬" shell="normal">
			
			<name>IronDog</name>
			<cnName>铁犬</cnName>
			<raceType>human</raceType>
			<swfUrl>swf/enemy/IronDog294.swf</swfUrl>
			<!-- 基本系数 -->
			<showLevel>999</showLevel>
			<headIconUrl>IconGather/IronDog</headIconUrl>
			<imgType>normal</imgType>
			<imgArr>
				stand,move
				,normalAttack,die1
				,fiveAttack,mopAttack,daggerAttack,iceAttack,hideAttack
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-12, -90, 24, 90</hitRect><!-- 站立碰撞体积-->
			<maxVx>13</maxVx>
			<extraAIClassLabel>IronDog_AIExtra</extraAIClassLabel>
			<avtiveSkillCdOverT>0.1</avtiveSkillCdOverT>
			<!-- 技能 -->
			<skillArr>IronDogIce,IronDogDagger,IronDogFive,fastForward_enemy,IronDogBuff,verShield,underToLand,rigidBody_enemy,likeMissleNo,summonShortLife,findHide,State_SpellImmunity</skillArr>
			<bossSkillArr>IronDogMop</bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<wilderSkillArr>weaponDefence</wilderSkillArr>
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel><cn>刺砍</cn>
					<hurtRatio>1</hurtRatio>
					<attackType>holy</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit" soundRan="3">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>fiveAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.00000000000001</hurtRatio>
					<hurtMul>0.6</hurtMul>
					<attackType>holy</attackType>
					<skillArr>invincibleEmp</skillArr>
					<grapRect>-400,-111,100,105</grapRect>
					<hitImgUrl name="IronDogFiveHit"/>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>mopAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.00000000000001</hurtRatio>
					<hurtMul>0.07</hurtMul>
					<attackType>holy</attackType>
					<skillArr>invincibleEmp</skillArr>
					<grapRect>-400,-111,100,105</grapRect>
					<hitImgUrl name="IronDogFiveHit"/>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>daggerAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>1</hurtRatio>
					<attackType>holy</attackType>
					<skillArr>invincibleEmp</skillArr>
					<grapRect>-400,-111,100,105</grapRect>
					<hitImgUrl name="IronDogFiveHit"/>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>iceAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.00000000000001</hurtRatio>
					<hurtMul>0.15</hurtMul>
					<attackType>holy</attackType>
					<skillArr>invincibleEmp</skillArr>
					<grapRect>-400,-111,100,105</grapRect>
					<hitImgUrl name="IronDogFiveHit"/>
				</hurt>
			</hurtArr>
		</body>
	</father>
	<father name="enemy" cnName="技能">
		<bullet cnName="七人斩-子弹">
			<name>IronDogFiveBullet</name>
			<cnName>七人斩-子弹</cnName><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtMul>0.5</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletNum>1</bulletNum>
			<bulletAngle>90</bulletAngle>
			<twoHitGap>0.8</twoHitGap>
			<twoHitSameNameB>1</twoHitSameNameB>
			
			<noHitTime>0.47</noHitTime><!-- 这个必须比bulletLife少0.09秒 -->
			<bulletLife>0.56</bulletLife>
			<imgClearDelay>0.43</imgClearDelay>
			<bulletWidth>700</bulletWidth>
			<hitType>longLine</hitType>
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<skillArr>invincibleEmp</skillArr>
			<bulletImgUrl con="add">IronDog/fiveBullet</bulletImgUrl>
			<hitImgUrl name="IronDogFiveHit"/><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="冰魄斩-子弹">
			<name>IronDogIceBullet</name>
			<cnName>冰魄斩-子弹</cnName><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<!--伤害属性------------------------------------------------------------ -->
			<hitType>rect</hitType>
			<hurtMul>0.05</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletNum>1</bulletNum>
			<bulletLife>0.0001</bulletLife>
			<imgClearDelay>0.35</imgClearDelay>
			<bulletWidth>80</bulletWidth>
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<skillArr>invincibleEmp,IronDogIceHit</skillArr>
			<bulletImgUrl con="add">IronDog/iceBullet</bulletImgUrl>
			<hitImgUrl name="IronDogFiveHit"/>
		</bullet>
	</father>
	
	
	<father name="enemy" cnName="技能">
		<skill>
			<name>IronDogBuff</name>
			<cnName>铁犬buff</cnName><noBeClearB>1</noBeClearB><noCopyB>1</noCopyB>
			<conditionType>passive</conditionType><condition>add</condition><target>me</target>
			<addType>state</addType>
			<effectType>IronDogBuff</effectType>
			<stateEffectImg con="add" partType="body,mouth,hand_left,hand_right,foot_left,foot_right">generalEffect/purBigLight</stateEffectImg>
			<duration>99999999</duration>
		</skill>
		<skill>
			<name>IronDogFive</name><![CDATA[对付使用高伤害副手(跳斩、凯撒、噬原、屠夫)的]]>
			<cnName>九人斩</cnName><noBeClearB>1</noBeClearB><ignoreNoSkillB>1</ignoreNoSkillB><ignoreSilenceB>1</ignoreSilenceB>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>3</cd><iconUrl36>SkillIcon/IronDogFive_36</iconUrl36>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>IronDogFive</otherConditionArr>
			<target>me</target>
			
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_IronDogFive</effectType>
			<obj>"name":"IronDogFiveBullet"</obj>
			<duration>1.6</duration>
			<meEffectImg name="skillEffectTeleport"/>
			<pointEffectImg name="SaberTiger_laser_point"/>
			<meActionLabel>fiveAttack</meActionLabel>
			<description>铁犬释放9个分身一起劈斩地面，对敌人造成重大伤害。</description>
		</skill>
		
		<skill>
			<name>IronDogMop</name><![CDATA[对付开启伤害技能的敌人]]>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>飞横扫</cnName>
			<noBeClearB>1</noBeClearB><![CDATA[ 不会被清除 ]]>
			<ignoreNoSkillB>1</ignoreNoSkillB><![CDATA[ 无视封锁 ]]>
			<ignoreSilenceB>1</ignoreSilenceB><![CDATA[ 无视沉默 ]]>
			<minTriggerT>5</minTriggerT><![CDATA[ 最短触发间隔 ]]>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType><![CDATA[ 被动技能 ]]>
			<condition>underHit</condition><![CDATA[ 受到攻击时触发 ]]>
			<otherConditionArr>IronDogMop</otherConditionArr><![CDATA[ 额外触发条件 ]]>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>IronDogMop</effectType>
			<value>2</value>
			<duration>5</duration><!-- 这个时间要长一点，能覆盖所有次数的扫荡 -->
			<pointEffectImg name="SaberTiger_laser_point"/>
			<meActionLabel>mopAttack</meActionLabel>
			<description>当敌人使用攻击效果时，铁犬将以极快的步伐左右横扫敌人。</description>
		</skill>
					<skill>
						<name>IronDogMopTask</name><![CDATA[任务中主动开启飞横扫]]>
						<cnName>飞横扫</cnName><noBeClearB>1</noBeClearB><ignoreNoSkillB>1</ignoreNoSkillB><ignoreSilenceB>1</ignoreSilenceB>
						<cd>20</cd><iconUrl36>SkillIcon/fastForward_Cheetah_36</iconUrl36>
						<conditionType>active</conditionType>
						<condition>avtiveSkillCdOver</condition>
						<target>me</target>
						<!--效果------------------------------------------------------------ -->
						<addType>state</addType>
						<effectType>IronDogMop</effectType>
						<value>2</value>
						<duration>5</duration><!-- 这个时间要长一点，能覆盖所有次数的扫荡 -->
						<pointEffectImg name="SaberTiger_laser_point"/>
						<meActionLabel>mopAttack</meActionLabel>
						<description></description>
					</skill>
		<skill>
			<name>IronDogDagger</name><![CDATA[对付空中单位]]>
			<cnName>翔空刺</cnName><noBeClearB>1</noBeClearB><ignoreNoSkillB>1</ignoreNoSkillB><ignoreSilenceB>1</ignoreSilenceB>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>7</cd><iconUrl36>SkillIcon/IronDogDagger_36</iconUrl36>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>IronDogDagger</effectType>
			<duration>1.5</duration>
			<meActionLabel>daggerAttack</meActionLabel>
			<stateEffectImg name="groupSpeedUp_enemy_state" cnName="腿部滑翔特效" />
			<pointEffectImg name="skillEffectTeleport" cnName="闪烁" />
			<otherEffectImg name="skillEffectTeleportShow" cnName="闪烁出现"/>
			<description>紧紧咬住对手，并在1秒内快速刺出6刀。</description>
		</skill>
		<skill>
			<name>IronDogIce</name><![CDATA[对付空中单位]]>
			<cnName>震魂斩</cnName><noBeClearB>1</noBeClearB><ignoreNoSkillB>1</ignoreNoSkillB>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>10</cd><iconUrl36>SkillIcon/IronDogIce_36</iconUrl36>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_IronDogIce</effectType>
			<value>13</value><!-- 闪电个数 -->
			<mul>0.066</mul><!-- 闪电时间间隔 -->
			<secMul>120</secMul><!-- 闪电距离间隔 -->
			<duration>2.1</duration>
			<obj>"name":"IronDogIceBullet"</obj>
			<meActionLabel>iceAttack</meActionLabel>
			<description>用力砍向地面，激发出向周围扩散的震魂波，震魂波将麻痹敌人。</description>
		</skill>
					<skill><!-- 限制 -->
						<name>IronDogIceHit</name>
						<cnName>震魂斩-麻痹沉默</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>hit</condition>
						<target>target</target>
						<!--效果------------------------------------------------------------ -->
						<addType>state</addType>
						<effectType>IronDogIceHit</effectType>
						<duration>3</duration>
						<!--图像------------------------------------------------------------ --> 
						<stateEffectImg name="noMoveWarrior_state" cnName="麻痹-状态" />
						<stateEffectImg2 name="silenceWarrior_state" cnName="沉默状态" />
					</skill>
	</father>	
</data>