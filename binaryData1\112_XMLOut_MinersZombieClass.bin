<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="enemy">
		<body>
			<name><PERSON><PERSON><PERSON><PERSON><PERSON></name>
			<cnName>矿工僵尸</cnName><headIconUrl>IconGather/MinersZombie</headIconUrl>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/MinersZombie.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.2</lifeRatio>
			<rosRatio>0.2</rosRatio>
			<showLevel>999</showLevel>
			<!-- 图像 -->
			<imgArr>
				stand,move,run
				,normalAttack,shootAttack
				,sprintAttack,magicAttack
				,hurt1,hurt2,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<bossImgArr>sprintAttack,magicAttack</bossImgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-76,28,76</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>6</maxVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel></extraAIClassLabel>
			<avtiveSkillCdOverT>0</avtiveSkillCdOverT>
			<!-- 技能 -->
			<skillArr>reflectiveShell,noBounce_enemy,MinersZombieBuff</skillArr>
			<bossSkillArr>minersSprint,minersShake,meteoriteRain,weaponDefence,moreBullet,State_SpellImmunity,toLand</bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel><cn>镐击</cn>
					<hurtRatio>2</hurtRatio>
					<shakeValue>4</shakeValue>
					<attackType>direct</attackType>
					<skillArr></skillArr>
					<hitImgUrl name="oreWormHit" />
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel><cn>飞镐</cn>
					<bulletLabel>MinersZombie_1</bulletLabel>
					<grapRect>-450,-111,350,105</grapRect>
					<hurtRatio>1.5</hurtRatio>
					<attackType>holy</attackType>
				</hurt>
				
				<hurt info="不加入ai选择">
					<imgLabel>sprintAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-450,-111,350,105</grapRect>
					<hurtRatio>0.000000001</hurtRatio>
					<hurtMul>0.04</hurtMul>
					<attackType>holy</attackType>
					<hitImgUrl name="oreWormHit" />
					<skillArr>minersSprintHit</skillArr>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>magicAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-450,-111,350,105</grapRect>
					<hurtRatio>0.000000001</hurtRatio>
					<hurtMul>0.45</hurtMul>
					<attackType>holy</attackType>
					<skillArr></skillArr>
				</hurt>
			</hurtArr>
		</body>
		
		
		
		<bullet>
			<name>MinersZombie_1</name>
			<cnName>飞镐</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>3</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.7</attackGap>
			<attackDelay>0.5</attackDelay>
			<bulletAngle>-179</bulletAngle>
			<bulletAngleRange>70</bulletAngleRange>
			<shootPoint>-71,-84</shootPoint>
			<bulletSpeed>12</bulletSpeed>
			<!--特别属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<skillArr></skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>MinersZombie/bullet</bulletImgUrl>
			<smokeImgUrl con="filter">MinersZombie/bulletSmoke</smokeImgUrl>
			<hitImgUrl name="oreWormHit" />
		</bullet>
		
		
		
		<skill>
			<name>MinersZombieBuff</name>
			<cnName>矿工僵尸buff</cnName>
			<ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
			<everNoClearB>1</everNoClearB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<conditionType>passive</conditionType>
			<condition>bodyAdd</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>MinersZombieBuff</effectType>
			<duration>9999999</duration>
		</skill>
		<skill cnName="致命冲刺"><!-- dps -->
			<name>minersSprint</name>
			<cnName>致命冲刺</cnName><iconUrl36>SkillIcon/GasDefenseShake_36</iconUrl36>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
			<cd>8</cd>
			<delay>0.1</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>noAllSkill</effectType>
			<duration>2</duration>
			<range>999999</range>
			<!--图像------------------------------------------------------------ --> 
			<meEffectImg soundUrl="sound/silence_enemy"></meEffectImg>
			<stateEffectImg partType="mouth" con="add">skillEffect/silenceMore</stateEffectImg>
			<meActionLabel>sprintAttack</meActionLabel>
			<description>矿工僵尸瞬移到敌人面前，全场封锁，并给目标一致命冲刺。</description>
		</skill>
		
				<skill>
					<name>minersSprintHit</name>
					<cnName>冲刺碰撞</cnName>
					<ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
					<noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
					<conditionType>passive</conditionType>
					<condition>hit</condition>
					<target>target</target>
					<!--效果------------------------------------------------------------ -->
					<addType>state</addType>
					<effectType>minersSprintHit</effectType>
					<duration>0.1</duration>
				</skill>
				
		<skill cnName="地震"><!-- dps -->
			<name>minersShake</name>
			<cnName>地震</cnName><iconUrl36></iconUrl36><ignoreNoSkillB>1</ignoreNoSkillB>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
			<cd>12</cd>
			<delay>0.3</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_minersShake</effectType>
			<duration>6</duration>
			<obj>"name":"minersShakeBullet"</obj>
			<mul>0.3</mul><!-- 受到伤害乘以 -->
			<range>700</range><!-- 范围 -->
			<intervalT>0.5</intervalT><!-- 每隔几秒 -->
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg name="minersShake" />
			<pointEffectImg name="minersShakeBody" />
			<meActionLabel>magicAttack</meActionLabel>
			<description>矿工僵尸开启自身的地震发生器，每过[intervalT]秒震一次地面，对附近的敌人造成重大伤害，持续[duration]秒。</description>
		</skill><![CDATA[
					<bullet cnName="矿工僵尸-地震">
						<name>minersShakeBullet</name>
						<cnName>矿工僵尸-地震</cnName>
						<!--伤害属性------------------------------------------------------------ -->
						<hurtRatio>0</hurtRatio>
						<hurtMul>0.05</hurtMul>
						<!--基本属性------------------------------------------------------------ -->
						<shakeAngle>5</shakeAngle>
						<bulletLife>0.04</bulletLife>
						<bulletWidth>70</bulletWidth>
						<hitType>rect</hitType>
						<!--攻击时的属性------------------------------------------------------------ -->
						<attackGap>0</attackGap>
						<attackDelay>0</attackDelay>
						<bulletAngle>270</bulletAngle>
						<bulletNum>1</bulletNum>				
						<shootNum>20</shootNum>					
						<shootGap>0.03</shootGap>					
						<shootAngle>0</shootAngle>					
						<positionD specialType="minersShakeBullet"/>
						<!--特殊属性------------------------------------------------------------ -->	
						<penetrationGap>1000</penetrationGap>
						<skillArr>blindness_anger_PetFightWolf</skillArr>
						<!--运动属性------------------------------------------------------------ -->	
						<shootPoint>0,0</shootPoint>
						<bulletSpeed>0</bulletSpeed>
						<!--图像动画属性------------------------------------------------------------ -->
						<flipX>1</flipX>
						<imgClearDelay>0.30</imgClearDelay>
						<bulletImgUrl>boomEffect/blackBoom</bulletImgUrl>
						<hitImgUrl con="add" soundUrl="sound/body_hit">bladeHitEffect/blood</hitImgUrl>
					</bullet>
					]]>
					<bullet cnName="矿工僵尸-地震">
						<name>minersShakeBullet</name>
						<cnName>矿工僵尸-地震</cnName>
						<!--伤害属性------------------------------------------------------------ -->
						<hurtRatio>0</hurtRatio>
						<hurtMul>0.05</hurtMul>
						<attackType>holy</attackType>
						<noMagneticB>1</noMagneticB>
						<!--基本属性------------------------------------------------------------ -->
						<shakeAngle>5</shakeAngle>
						<bulletLife>0.01</bulletLife>
						<bulletWidth>50</bulletWidth>
						<hitType>rect</hitType>
						<!--攻击时的属性------------------------------------------------------------ -->
						<attackGap>0</attackGap>
						<attackDelay>0</attackDelay>
						<bulletAngle>270</bulletAngle>
						<bulletNum>1</bulletNum>				
						<!--特殊属性------------------------------------------------------------ -->	
						<penetrationGap>1000</penetrationGap>
						<penetrationNum>999</penetrationNum>
						<!--运动属性------------------------------------------------------------ -->	
						<shootPoint>0,0</shootPoint>
						<bulletSpeed>0</bulletSpeed>
						<!--图像动画属性------------------------------------------------------------ -->
						<imgClearDelay>0.30</imgClearDelay>
						<bulletImgUrl>boomEffect/blackBoom</bulletImgUrl>
						<hitImgUrl con="add" soundUrl="sound/body_hit">bladeHitEffect/blood</hitImgUrl>
					</bullet>
					
		
		<skill>
			<name>reflectiveShell</name>
			<cnName>反光服</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<otherConditionArr>hurtArmsType</otherConditionArr>
			<conditionString>laser</conditionString>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>lostPro</effectType>
			<mul>0.8</mul>
			<duration>1</duration>
			<stateEffectImg name="blindnessPhantomHit_state" />
			<description>受到激光枪攻击时，反光服将激光发射到攻击者的眼中，使其致盲，攻击成功率降低[mul]，持续[duration]秒。</description>
		</skill>
	</father>
</data>