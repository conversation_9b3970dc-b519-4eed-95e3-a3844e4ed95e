<?xml version="1.0" encoding="utf-8" ?>
<data>
	<gather><!-- 开始等级，最大等级1，最大等级2，结束等级 -->
		<father cnName="时装" name="fashion">
			<!-- 男性 -->
			<image name="bumblebee" cnName="大黄蜂时装">
				<addObjJson>{'dpsAll':0.15,'lifeAll':0.15}</addObjJson>
			</image>
			<image name="wolverine" cnName="金刚狼时装">
				<addObjJson>{'dpsAll':0.15,'lifeAll':0.15}</addObjJson>
			</image>
			<image name="ironMan_red" cnName="赤焰钢铁侠">
				<addObjJson>{'dpsAll':0.13,'lifeAll':0.13}</addObjJson>
			</image>
			<image name="ironMan_blue" cnName="苍穹钢铁侠">
				<addObjJson>{'dpsAll':0.13,'lifeAll':0.13}</addObjJson>
			</image>
			<image name="captain" cnName="队长时装">
				<addObjJson>{'dpsAll':0.1,'lifeAll':0.1}</addObjJson>
			</image>
			<image name="luffy" cnName="小飞时装">
				<addObjJson>{'dpsAll':0.1,'lifeAll':0.1}</addObjJson>
			</image>
			<image name="hollowification_head" cnName="虚化面具" fashionPartShow="head">
				<addObjJson>{'dpsAll':0.07,'lifeAll':0.07}</addObjJson>
			</image>
			<image name="hollowificationFull_head" cnName="虚化完全体" fashionPartShow="head">
				<addObjJson>{'dpsAll':0.1,'lifeAll':0.1}</addObjJson>
			</image>
			
			<image name="massEffect" cnName="三染自带无敌时装" skillArr="dragonHeadSkill,thorns_pig,fleshFeast_pig,invisibility_SpiderKing,acidRain_SpiderKing,selfBurn_enemy,selfBurn_hero_link,wisdomAnger_hero,cmldef_enemy,through_enemy,hammer_hit,despise_enemy,gaze_enemy,rigidBody_enemy,defenceBounce_enemy,noBounce_enemy,corrosion_hugePosion,atry_skeleton,blindness_skeleton,treater_FightWolf,booby_godArmsSkill,likeMissle_Shapers,selfBurn_enemy,State_AddMove,invincible_eeg,magneticField_egg,outfit_follow,outfit_blood,outfit_shootMissile,outfit_jump,outfit_crit,redMoto_state,slowMoveHalo_enemy,disabledHalo_enemy,recoveryHalo_enemy,trueshot_enemy,corrosion_enemy,desertedHalo_enemy,noSpeedReduce_knights,crazy_knights,hammer_knights,treater_knights,trample_knights,boundless_enemy,boundless_enemy_link,boundless_enemy_pass,boundless_enemy_link,boundless_enemy_pass,outfit_boom,outfit_wolong,outfit_wolong_hurt,Triceratops_deserted,Triceratops_noDegradation,FlyDragon_fireSurround,FlyDragon_dodgePro,FlyDragon_likeMissle,FlyDragon_fightDedut,YouthWolf_crazy,YouthWolfUnder,YouthWolfUnder2,YouthWolfHeroAngerAdd,blueMoto_state,heroSprint,SaberTiger_shield_defence,wisdomAnger_hero">
				<addObjJson>{'headMul':9999999,'expMul':9999999,'lottery':9999999,'lifeRateBlack':9999999,'lifeAllBlack':9999999,'bulletDedut':9999999,'skillDedut':9999999,'moreLifeMul':9999999,'moreLifeMul':9999999,'moreDpsMul':9999999,'weaponDropPro':9999999,'bloodStoneDropPro':9999999,'converStoneDropPro':9999999,'godStoneDropPro':9999999,'lifeCatalystDropPro':9999999,'vehicleCashDropNum':9999999,'arenaStampDropNum':9999999,'taxStampDropPro':9999999,'blackEquipDropPro':9999999,'blackArmsDropPro':9999999,'rareEquipDropPro':9999999,'rareArmsDropPro':9999999,'orredEquipDropPro':9999999,'orredArmsDropPro':9999999,'expMul':9999999,'headMul':9999999,'head':9999999,'reload':9999999,'charger':9999999,'dodge':9999999,'cdMul':9999999,'moveMul':9999999,'fightDedut':9999999,'attackGap':9999999}</addObjJson>
			</image>
			
			
			
			
			<image name="xiaoKa" cnName="小卡时装">
				<addObjJson>{'dpsAll':0.14,'lifeAll':0.14,"attackGap":0.07}</addObjJson>
			</image>
			<image name="xiaoLong" cnName="小隆时装">
				<addObjJson>{'dpsAll':0.10,'lifeAll':0.10,"attackGap":0.07}</addObjJson>
			</image>
		
			
			<!-- 新时装 -->
			<image name="xiaoBei_head" cnName="小贝头盔" fashionPartShow="head">
				<addObjJson>{'dpsAll':0.10,'lifeAll':0.20}</addObjJson>
			</image>
			<image name="xiaoLuo" cnName="小罗时装">
				<addObjJson>{'dpsAll':0.14,'lifeAll':0.17}</addObjJson>
			</image>
			<image name="xiaoRong" cnName="小鼬时装">
				<addObjJson>{'dpsAll':0.12,'lifeAll':0.15}</addObjJson>
			</image>
			<image name="nightmare" cnName="夜魔时装" replaceWeaponType="blade">
				<addObjJson>{'dpsAll':0.13,'lifeAll':0.18,"hurtAll":0.08,"blackArmsDropPro":0.30}</addObjJson>
			</image>
			
			<image name="levi" cnName="团长时装">
				<addObjJson>{'dpsAll':0.16,'lifeAll':0.15,"blackEquipDropPro":0.40}</addObjJson>
			</image>
			<image name="xiaoJin" cnName="小金时装">
				<addObjJson>{'dpsAll':0.13,'lifeAll':0.18,"hurtAll":0.06,"blackEquipDropPro":0.60}</addObjJson>
			</image>
			<image name="bigBoss" cnName="大Boss时装" skillArr="summonWolf_bigBoss">
				<addObjJson>{'dpsAll':0.13,'lifeAll':0.18,"hurtAll":0.08,"blackArmsDropPro":0.60,"blackEquipDropPro":0.40}</addObjJson>
			</image>
			
			<image name="motoDriver" cnName="摩托车" skillArr="zoomOut">
				<addObjJson>{'dpsAll':0.07,'damageMul':0.05,'lifeAll':0.10}</addObjJson>
			</image>
			
			<image name="antMan" cnName="小蚁时装" skillArr="zoomOut" composeMustNum="200">
				<addObjJson>{'dpsAll':0.07,'damageMul':0.05,'lifeAll':0.10}</addObjJson>
			</image>
			
			<![CDATA[竞技场-限时]]>
			<image name="xiaoAi" cnName="小艾时装" life="15" skillArr="xiaoAiShoot">
				<addObjJson>{'dpsAll':0.23,'lifeAll':0.24,"attackGap":0.06,"blackArmsDropPro":0.2,"blackEquipDropPro":0.2}</addObjJson>
			</image>
			<image name="xiaoBo" cnName="小波时装" life="15"  skillArr="xiaoBoShoot">
				<addObjJson>{'dpsAll':0.25,'lifeAll':0.28,"hurtAll":0.18,"blackArmsDropPro":0.3,"blackEquipDropPro":0.3}</addObjJson>
			</image>
			<image name="xiaoMing" cnName="小鸣时装" life="15"  skillArr="xiaoMingShoot">
				<addObjJson>{'dpsAll':0.28,'lifeAll':0.31,"hurtAll":0.21,"blackArmsDropPro":0.4,"blackEquipDropPro":0.4}</addObjJson>
			</image>
			<![CDATA[特别]]>
			<image name="santaClaus" cnName="圣诞老人时装">
				<addObjJson>{'dpsAll':0.16,'lifeAll':0.16,'sweepingNum':3}</addObjJson>
			</image>
			
			
			<image name="soldier76" cnName="小7时装" composeMustNum="200">
				<addObjJson>{'hurtAll':0.06,"blackArmsDropPro":0.42}</addObjJson>
			</image>
			<image name="dogHead" cnName="神烦狗" fashionPartShow="head" composeMustNum="150">
				<addObjJson>{'hurtAll':0.10,'lifeAll':0.05,'moveMul':0.5,'sweepingNum':2}</addObjJson>
			</image>
			<image name="pumpkinHead" cnName="南瓜头" fashionPartShow="head" skillArr="pumpkinHead" life="5">
				<addObjJson>{'hurtAll':0.24,'moveMul':0.3,'maxJumpNumAdd':3}</addObjJson>
			</image>
			
			
			<image name="undeadBird" cnName="菲尼克斯时装">
				<addObjJson>{'damageMul':0.05,'maxJumpNumAdd':2,'cdMul':0.2}</addObjJson>
			</image>
			<image name="workerHead" cnName="工人帽" fashionPartShow="head" composeMustNum="40">
				<addObjJson>{'headMul':0.50,'coinMul':1,'expMul':1}</addObjJson>
			</image>
			<image name="wolfFashion" cnName="狼首" fashionPartShow="head,coat" skillArr="wolfFashionSkill" composeMustNum="240">
				<addObjJson>{'damageMul':0.06,"blackArmsDropPro":0.45}</addObjJson>
			</image>
			
			<image name="sanji" cnName="小治" composeMustNum="120" skillArr="crazy_sanji">
				<addObjJson>{'attackGap':0.08,'moveMul':0.2,'lottery':10}</addObjJson>
			</image>
			
			
			<image name="chinaCaptain" cnName="五星队长" life="20" skillArr="chinaCaptainSkill">
				<addObjJson>{'dpsAll':0.15,'lifeAll':0.15,"blackArmsDropPro":0.7,"blackEquipDropPro":0.7}</addObjJson>
			</image>
			<image name="chinaCaptain2" cnName="五星队长" skillArr="chinaCaptainSkill">
				<addObjJson>{'dpsAll':0.20,'lifeAll':0.18,"blackArmsDropPro":0.8,"blackEquipDropPro":0.8}</addObjJson>
			</image>
			
			<image name="bioShock" cnName="生化奇兵" life="60" composeMustNum="160" skillArr="bioShockSkill">
				<addObjJson>{'dpsAll':0.18,'lifeAll':0.18,"lottery":30}</addObjJson>
			</image>
			<image name="bioShockDoom" cnName="末日奇兵" life="25" skillArr="bioShockSkill">
				<addObjJson>{'dpsAll':0.25,'lifeAll':0.18,"lottery":10}</addObjJson>
			</image>
			
			
			<image name="armyCommander" cnName="军长时装" skillArr="armyCommanderSkill">
				<addObjJson>{'dpsAll':0.20,'lifeAll':0.18,"blackArmsDropPro":0.8,"gemDropPro":0.45}</addObjJson>
			</image>
			<image name="phage" cnName="噬原时装"  replaceWeaponType="blade" hd="1" skillArr="bladeSkill">
				<addObjJson>{'dpsAll':0.20,'lifeAll':0.18,"blackEquipDropPro":0.8,"gemDropPro":0.45}</addObjJson>
			</image>
			<image name="phageNew" cnName="噬魂时装"  replaceWeaponType="blade" hd="1" skillArr="bladeSkill">
				<addObjJson>{'dpsAll':0.20,'lifeAll':0.18,"blackEquipDropPro":0.8,"gemDropPro":0.45}</addObjJson>
			</image>
			
			<image name="snowShadow" cnName="雪影时装"  hd="1" skillArr="snowShadowSkill" sex="female" replaceWeaponType="dagger">
				<addObjJson>{'dpsAll':0.20,'lifeAll':0.18,"blackEquipDropPro":0.8,"gemDropPro":0.45,"demBallDropNum":2}</addObjJson>
			</image>
			
			<image name="snowShadow2" cnName="雪影限时皮肤"  hd="1" sex="female"  life="5" >
				<addObjJson></addObjJson>
			</image>
			
			<image name="whiteUniver" cnName="白宙时装" hd="1">
			</image>
			<image name="blackGhost" cnName="黑魂时装" hd="1">
			</image>
			
			<image name="hundredGhosts" cnName="刹鬼时装" replaceWeaponType="sword" hd="1" skillArr="hundredGhostsSkill">
				<addObjJson>{'dpsAll':0.20,'lifeAll':0.18,"blackEquipDropPro":0.45,"gemDropPro":0.45,"demStroneDropNum":1}</addObjJson>
			</image>
			
			<image name="greatSage" cnName="大圣时装" replaceWeaponType="stick" hd="1" skillArr="greatSageSkill">
				<addObjJson>{'dpsAll':0.20,'lifeAll':0.18,"blackArmsDropPro":0.6,"blackEquipDropPro":0.6,"gemDropPro":0.40}</addObjJson>
			</image>
			<image name="greatSage2" cnName="大圣限时皮肤"  hd="1"  life="7" imgSwf="greatSage" iconLabel="greatSage/fashion_icon2">
				<addObjJson></addObjJson>
			</image>
			
			
			<image name="neZha" cnName="哪吒时装"  replaceWeaponType="dagger">
				<addObjJson>{'dpsAll':0.13,'lifeAll':0.18,"hurtAll":0.08,"blackArmsDropPro":0.40,"specialPartsDropPro":0.4}</addObjJson>
			</image>
			<image name="wuKong" cnName="悟空时装"  replaceWeaponType="stick">
				<addObjJson>{'dpsAll':0.13,'lifeAll':0.18,"hurtAll":0.08,"blackArmsDropPro":0.40,"specialPartsDropPro":0.4}</addObjJson>
			</image>
			
			
			<image name="cyanArmy" cnName="伏魔时装" skillArr="cyanArmySkill" life="40">
				<addObjJson>{'dpsAll':0.18,'lifeAll':0.18,"gemDropPro":0.20}</addObjJson>
			</image>
			
			<image name="dragonHead" cnName="龙首" fashionPartShow="head" skillArr="dragonHeadSkill" life="7">
				<addObjJson>{'damageMul':0.07,"blackArmsDropPro":0.33}</addObjJson>
			</image>
			<image name="dragonHead2" cnName="闪电龙首" fashionPartShow="head" skillArr="dragonHeadSkill,screwBall2" life="7">
				<addObjJson>{'damageMul':0.07,"blackArmsDropPro":0.33}</addObjJson>
			</image>
			<image name="goldFalcon" cnName="黄金隼" skillArr="goldFalcon" hd="1">
				<addObjJson>{'dpsAll':0.13,'lifeAll':0.18,"hurtAll":0.08,"blackEquipDropPro":0.40,"specialPartsDropPro":0.4}</addObjJson>
			</image>
			<image name="zombieMask" cnName="僵尸面具" skillArr="zombieMask" life="10" fashionPartShow="head">
				<addObjJson>{'moveMul':0.5,'chargerMul':2,'capacityMul':2}</addObjJson>
			</image>
			<![CDATA[
			<image name="hookWitchHead" cnName="斩之头盔" skillArr="hookWitchHead" fashionPartShow="head">
				<addObjJson></addObjJson>
			</image>
			]]>
			
			
			<image name="spaceSuit" cnName="宇航服"></image>
			
			
			<![CDATA[女性]]>
			<image name="xiaoXiang" cnName="小香时装" sex="female">
				<addObjJson>{'dpsAll':0.18,'lifeAll':0.30}</addObjJson>
			</image>
			<image name="xiaoNa" cnName="小娜时装" sex="female">
				<addObjJson>{'dpsAll':0.20,'lifeAll':0.20}</addObjJson>
			</image>
			<image name="xiaoTian" cnName="小田时装" sex="female">
				<addObjJson>{'dpsAll':0.15,'lifeAll':0.15}</addObjJson>
			</image>
			
			<image name="saber" cnName="小巴时装" sex="female">
				<addObjJson>{'dpsAll':0.25,'lifeAll':0.29,"dayLoveAdd":20}</addObjJson>
			</image>
			<image name="kagura" cnName="小乐时装" sex="female">
				<addObjJson>{'dpsAll':0.25,'lifeAll':0.29}</addObjJson>
				<description>战斗中倒下将不扣除好感度。</description>
			</image>
			<image name="christmasWoman" cnName="圣诞女郎时装"  sex="female">
				<addObjJson>{'dpsAll':0.25,'lifeAll':0.29,'dodge':0.2}</addObjJson>
			</image>
			<![CDATA[心零]]>
			<image name="studentLing" cnName="学生心零" sex="female" onlyRole="XinLing">
				<addObjJson>{'dpsAll':0.25,'lifeAll':0.29}</addObjJson>
			</image>
			<image name="helperLing" cnName="助手心零" sex="female" onlyRole="XinLing">
				<addObjJson>{'dpsAll':0.25,'lifeAll':0.29}</addObjJson>
			</image>
			<image name="swimLing" cnName="清凉心零" sex="female" onlyRole="XinLing" hd="1">
				<addObjJson>{'dpsAll':0.35,'lifeAll':0.40,"blackEquipDropPro":0.2,"specialPartsDropPro":0.34}</addObjJson>
			</image>
			<![CDATA[小樱]]>
			<image name="cowboyYing" cnName="牛仔小樱" sex="female" onlyRole="Girl,XiaoMei" hd="1">
				<addObjJson>{'dpsAll':0.35,'lifeAll':0.40,"blackEquipDropPro":0.2,"specialPartsDropPro":0.34}</addObjJson>
			</image>
			<image name="mechaYing" cnName="机甲小樱" sex="female" onlyRole="Girl,XiaoMei" hd="1">
				<addObjJson>{'dpsAll':0.35,'lifeAll':0.40,"blackEquipDropPro":0.2,"specialPartsDropPro":0.34}</addObjJson>
			</image>
			<![CDATA[小美]]>
			<image name="purpleMei" cnName="紫嫣小美" sex="female" onlyRole="Girl,XiaoMei" hd="1">
				<addObjJson>{'dpsAll':0.35,'lifeAll':0.40,"gemDropPro":0.3,"specialPartsDropPro":0.34}</addObjJson>
			</image>
		</father>
		
	</gather>
</data>