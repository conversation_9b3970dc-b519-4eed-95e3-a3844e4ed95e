<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="hit">
		<body cnName="采矿虫碰撞" name="oreWormHit" con="add" soundUrl="sound/vehicle_hit" soundRan="3">bulletHitEffect/fitHit</body>
		<body cnName="全域光波碰撞" name="silverScreenBulletHit" con="add" soundUrl="hitSound/waveHit" soundRan="3">bulletHitEffect/fitHit</body>
		<body cnName="绿色能量击中" name="greenEnergyHit" con="add" soundUrl="hitSound/shieldHit">bulletHitEffect/poison</body>
		
		<body cnName="黄色砍击-无声" name="yellowHitSilence"con="add"  >bulletHitEffect/bluntBig</body>
		<body cnName="紫色砍击" name="purpleHit" soundUrl="hitSound/chopMetal" soundRan="3" con="add"  >bulletHitEffect/bluntBigPurple</body>
		<body cnName="蓝色砍击" name="blueHit" soundUrl="hitSound/chopMetal" soundRan="3" con="add"  >bulletHitEffect/bigBlue</body>
		<body cnName="黄色棍击" name="yellowStickHit" soundUrl="hitSound/chopBody" soundRan="3" con="add" >bulletHitEffect/bluntBig</body>
		
		<body cnName="利爪击中" name="clawHit" con="add" soundUrl="hitSound/clawHit" soundRan="3" >bladeHitEffect/blood</body>
		<body cnName="拳头击中" name="fistHit" con="add" soundUrl="hitSound/fistHit" soundRan="3" >bulletHitEffect/fitHit</body>
		<body cnName="大激光击中-紫" name="purpleLaserBig"  raNum="1" con="add">WorldSnake/purLaser</body>
		
	</father>
	
	
	
	<father name="boom">
		<body con="add" name="stoneSeaWalk" cnName="石海步行特效">boomEffect/posionSmall</body>
		<body cnName="太空小爆炸" name="smallSpace" soundUrl="hitSound/fire"  soundRan="3" soundVolume="0.2">boomEffect/boom1</body>
		<body cnName="太空爆炸-大" name="bigSpace" soundUrl="boomSound/big" soundRan="4" shake="8,0.3,16,0,mess" len="70">boomEffect/bigCircle</body>
		<body cnName="太空爆炸-大-震动大" name="bigSpaceShake" soundUrl="boomSound/big" soundRan="4" shake="8,0.3,30,0,mess" len="70">boomEffect/bigCircle</body>
		<body cnName="太空爆炸-中" name="midSpace" soundUrl="boomSound/midBoom" soundRan="7" shake="8,0.3,16,0,mess" len="70">boomEffect/bigCircle</body>
		<body cnName="石头爆炸" name="stoneBoom" soundUrl="boomSound/microBoom" soundRan="4" shake="8,0.2,16,0,mess" soundVolume="0.6" len="50">boomEffect/bigSmoke</body>
		<body cnName="幻银火光" name="SilverShipFire"  raNum="30" con="add" soundUrl="sound/laserShoot" soundVolume="0.5">gunFire/enegry</body>
		<body cnName="紫蜂火光" name="PurpleBeeFire"  raNum="30" con="add" soundUrl="specialGun/yearCattleSound">bulletHitEffect/purLaser</body>
		<body cnName="矿弹出现" name="oreBombShow" con="add"  soundUrl="hitSound/waveHit" soundRan="3">generalEffect/blackHoleHide</body>
		
		<body cnName="能量波" name="waveBoom" con="add" raNum="4">boomEffect/wave</body>
		<body cnName="元素波" name="orangeWaveBoom" con="add" raNum="4">boomEffect/orangeWave</body>
		
		<body cnName="尸化特效" name="changeToZombie" soundUrl="sound/changeToZombie_enemy">boomEffect/posion3</body>
	</father>
	
	
	
	<father name="bullet" cnName="子弹">
		<body cnName="幻银跟踪导弹" name="SilverShipMissile"  raNum="30" >bullet/orangeMissile</body>
		<body cnName="幻银子弹" name="SilverShipBullet"  con="add" raNum="60" >bullet/blueLaserThin</body>
		<![CDATA[<body cnName="幻银光波子弹" name="silverScreenBullet"  con="add" raNum="2" >SilverShip/screenBullet</body>]]>
		<body cnName="紫蜂子弹" name="PurpleBeeBullet"  con="add" raNum="30" >bullet/laser2</body>
		<![CDATA[
		<body cnName="鸟头机子弹" name="OreBirdBullet"  con="add" raNum="30" >bullet/laser2</body>]]>
		<body cnName="矿锯" name="oreSawBullet" raNum="1" >OreBomb/stand</body>
		<body cnName="隼武子弹" name="falconArmsBullet" raNum="30" con="filter" >bullet/greenDiam</body>
		<body cnName="利爪僵尸子弹" name="clawsBullet" con="filter" raNum="30">ClawsZombie/bullet</body>
		<body cnName="双头僵尸-回旋镖" name="DoubleZombieRotate">DoubleZombie/rotateBullet</body>
		<body cnName="极源子弹" name="extremeGunBullet" con="add">generalEffect/frozenBallMid</body>
		<body cnName="漫游子弹" name="roamRocketBullet" raNum="30">bullet/redMissile</body>
		<body cnName="气球子弹" name="balloonBullet" raNum="30">PartsIcon/balloonBullet</body>
		<body cnName="月饼子弹" name="mooncakeBullet">PartsIcon/mooncakeBullet</body>
		<body cnName="神圣盖亚子弹" name="gaiaBigBullet" raNum="30">bullet/gaiaBigBullet</body>
		<body cnName="神圣盖亚-装甲压制" name="goldGaiaSkillBullet">GoldGaia/skillBullet</body>
	</father>
	<father name="bulletLine" cnName="线性子弹" >
		<body cnName="幻银光波子弹" name="silverScreenBullet" raNum="4" con="add" len="69" everFleshB="1" imgDieType="die" time="-1" bottomLayerB="1" >SilverShip/screenBullet</body>
		<body cnName="技能复制线" name="skillCopyLine" raNum="1" con="add" len="45" time="0.2" waveAn="45" everFleshB="1" imgDieType="ranPlay" soundUrl="sound/skillCopy_enemy">skillEffect/skillCopy_enemy_bullet</body>
	</father>
	
	
	<father name="smoke">
		<body cnName="爆炸尾烟-中" name="boomMoveSmoke" urlRandomValue="2" len="30">boomEffect/midInside</body>
		<body cnName="爆炸尾烟-小" name="boomMoveSmokeSmall" urlRandomValue="2" len="20">boomEffect/smallInside</body>
		<body cnName="灰尾烟-小" name="midGreySmoke" urlRandomValue="2" len="20">smokeEffect/midGrey</body>
		<body cnName="矿锯尾烟" name="oreBombState" con="filter" >bulletHitEffect/blueLaser</body>
		<body cnName="导弹尾烟" name="SilverShipMissileSmoke" con="filter" raNum="30" >smokeEffect/lineMid</body>
	</father>
	
	
	
	<father name="sputtering" cnName="溅射">
		<body name="greenBlood" urlRandomValue="4" time="3">sputtering/greenBlood</body>
		<body name="spark" urlRandomValue="4"  time="3">sputtering/spark</body>
		<body name="whiteBlood" urlRandomValue="4"  time="3">sputtering/whiteBlood</body>
		<body name="fire" urlRandomValue="4"  time="3">sputtering/fire</body>
		<body name="stone" urlRandomValue="4"  time="3">sputtering/stone</body>
		
		<![CDATA[<body name="bigFire" urlRandomValue="4"  time="3" raNum="20">sputtering/bigFire</body>]]>
		<body cnName="爆炸物-中" name="boomMove" sm="boomMoveSmoke" time="1.5" raNum="20" urlRandomValue="4">sputtering/bigFire</body>
		<body cnName="爆炸物-小" name="boomMoveSmall" sm="boomMoveSmokeSmall" time="1.5" raNum="20" urlRandomValue="4">sputtering/bigFire</body>
		<body cnName="爆炸石头" name="stoneMoveSmall" sm="midGreySmoke" time="1.5" raNum="20" urlRandomValue="4">sputtering/bigFire</body>
	</father>
	
	
	
	<father name="state" cnName="状态">
		<body cnName="火烟" name="boomMoveSmokeState" sm="boomMoveSmoke" partType="body">boomEffect/zero</body>
		<body cnName="火烟-小" name="boomMoveSmokeSmallState" sm="boomMoveSmokeSmall" partType="body">boomEffect/zero</body>
		<body cnName="陨石烟" name="stoneSmoke" sm="midGreySmoke" partType="body" xGap="50" con="add">boomEffect/zero</body>
		<body cnName="黄金隼烈火" name="falconSawState" con="add"  partType="head,body,leg_right_1,leg_left_1,arm_right_1,arm_left_1,mouth">generalEffect/blackHoleHide</body>
		<body cnName="水脚" name="waterFoot" con="add"  partType="2foot">generalEffect/waterFace</body>
		<body cnName="石脚" name="stoneFoot" con="add"  partType="2foot">skillEffect/imploding_enemy_bullet</body>
		
		<body cnName="武器冒烟" name="gunSmokeSmall" sm="midGreySmoke" partType="shootPoint">boomEffect/zero</body>
		<body cnName="四肢冒烟" name="minersShake" partType="2foot,2hand" sm="midGreySmoke" >boomEffect/zero</body>
		
		<body cnName="治疗兵护盾" name="healerShield" con="add"  partType="body">HealerZombie/shieldMc</body>
		<body cnName="自闭-符文" name="noAttackOrder" con="add"  partType="mouth">skillEffect/noAttackOrder</body>
		<body cnName="圣诞铁盒-buff" name="iconGiftBoxBuff" con="add">IconGiftBody/buff</body>
		
		<body cnName="嘴巴冒烟" name="mouthSmoke" partType="mouth" sm="midGreySmoke" >boomEffect/zero</body>
		<body cnName="驱魂特效" name="lightConePurgold" partType="head" con="add">generalEffect/lightConePurgold</body>
		<body cnName="技能回复" name="cdHole" partType="mouth" con="add" >skillEffect/rune_purple_s</body>
		<body cnName="技能回复降低" name="cdDown" partType="mouth">skillEffect/rune_black_s</body>
		<body cnName="怒气回复" name="angerHole" partType="mouth" con="add" >skillEffect/rune_anger</body>
		<body cnName="脑袋金光" name="headOrangeLight" partType="head" con="add" >lightEffect/orange</body>
		
		<body cnName="身上绿烟" name="bodyGreenSmoke" partType="mouth,head" con="add" imgDieType="ranPlay"  urlRandomValue="2">smokeEffect/midGreen</body>
		
		<body cnName="吞噬者-眼睛冒烟" name="eyeSmokeWSnake" partType="2eye" sm="boomMoveSmoke" >boomEffect/zero</body>
		<body cnName="吞噬者-无敌" name="weaknessWSnake" partType="body" con="add" >WorldSnake/weaknessEffect</body>
		
		<body cnName="爆裂弹" name="burstParts" partType="shootPoint" con="add">PartsIcon/burstEffect</body>
		<body cnName="头电" name="headElectric" partType="head" con="add">generalEffect/electricLineState</body>
		<body cnName="风驰血锯" name="FourDiggersSkill" partType="head" con="add">FourDiggers/effect5</body>
		<body cnName="火种特效" name="madFireBody" partType="body" con="add">generalEffect/madFire</body>
		
		<body cnName="筋斗云" name="greatSageCloud" partType="" con="filter" noShowB="1">greatSage/cloud</body>
		
	</father>
	<father name="target" cnName="瞬发">
		<body cnName="全屏光波" name="silverScreen" partType="body" con="add" soundUrl="SilverShip/screen_s">SilverShip/screenEffect</body>
		<body cnName="召唤矿锯" name="oreBombShowFlower" con="add"  soundUrl="hitSound/magicShoot" soundRan="3">generalEffect/blackHoleHide</body>
		<body cnName="黑洞产生-无声" name="blackHoleShow" con="add">generalEffect/blackHoleHide</body>
		<body cnName="真空" name="vacuumJie" partType="body" con="add">generalEffect/blackHoleHide</body>
		<body cnName="真空-音效" name="vacuumJieSound" soundUrl="sound/invisibility_hero" ></body>
		<body cnName="远视镜-音效" name="longGlassesSound" soundUrl="uiSound/screenScale" ></body>
		
		<body cnName="隼武发射" name="falconArmsFire" con="add" raNum="4">bulletHitEffect/bigBlue</body>
		<body cnName="黄金隼隐身" name="falconHide" con="add" soundUrl="FastGuards/skill1" noFollowB="1">FalconBoss/hideEffect</body>
		
		
		<body cnName="鬼王凯撒-右1" name="ghost_swordAttack_r1" soundUrl="IronDog/combo" soundRan="4" xGap="-1010">hundredGhosts/ghost_swordAttack_e</body>
		<body cnName="鬼王凯撒-右1" name="ghost_swordAttack_r2" soundUrl="IronDog/combo" soundRan="4" xGap="-1010">hundredGhosts/ghost_swordAttack_el</body>
		<body cnName="鬼王凯撒-左1" name="ghost_swordAttack_l1" soundUrl="IronDog/combo" soundRan="4">hundredGhosts/ghost_swordAttack_el</body>
		<body cnName="鬼王凯撒-左2" name="ghost_swordAttack_l2" soundUrl="IronDog/combo" soundRan="4">hundredGhosts/ghost_swordAttack_e</body>
		
		
		<body cnName="大圣大招-右1" name="great_stickAttack_r1" con="add">greatSage/great_stickAttack_r1</body>
		<body cnName="大圣大招-右2" name="great_stickAttack_r2" con="add">greatSage/great_stickAttack_r2</body>
		<body cnName="大圣大招-左1" name="great_stickAttack_l1" con="add">greatSage/great_stickAttack_l1</body>
		<body cnName="大圣大招-左2" name="great_stickAttack_l2" con="add">greatSage/great_stickAttack_l2</body>
		
		
		
		<body cnName="矿工僵尸-地震" name="minersShakeBody" soundUrl="boomSound/microBoom" soundRan="4">boomEffect/bigSoil</body>
		<body cnName="僵尸死后特效" name="zombieDie">smokeEffect/die</body>
		<body cnName="分身消失特效" name="shadowDie" soundUrl="hitSound/magicShoot" soundRan="3">smokeEffect/hide</body>
	</father>
</data>