<?xml version="1.0" encoding="utf-8" ?>
<data>
	
	<father name="wilder">
		
		
		<body name="骷髅巫师">
			
			<name>SkeletalMage</name>
			<cnName>骷髅巫师</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/SkeletalMage2.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.8</lifeRatio>
			<showLevel>999</showLevel>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<imgArr>
				stand,move,run
				,normalAttack,shootAttack,followAttack,shadowAttack
				,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<lifeBarExtraHeight>-40</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>12</maxVx>
			<runStartVx>8</runStartVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>SkeletalMage_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr>fightReduct2,defenceBounce_enemy,SkeletalMageFollow,SkeletalMageCloned</bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel>
					<hurtRatio>2</hurtRatio>
					<attackType>direct</attackType>
					<skillArr>slowMove_enemy</skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel>
					<bulletLabel>SkeletalMageShoot</bulletLabel>
					<grapRect>-400,-111,100,150</grapRect>
					<hurtRatio>2</hurtRatio>
					<shakeValue>4</shakeValue>
					<attackType>direct</attackType>
				</hurt>
				
				<hurt info="追魂术">
					<imgLabel>followAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>SkeletalMageFollowBullet</bulletLabel>
					<grapRect>-400,-111,100,150</grapRect>
					<hurtMul>0.3</hurtMul>
					<shakeValue>4</shakeValue>
					<attackType>holy</attackType>
				</hurt>
				
				<hurt info="制魂术">
					<imgLabel>shadowAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<noShootB>1</noShootB>
					<grapRect>-400,-111,100,150</grapRect>
					<skillArr></skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/fireHit2">bulletHitEffect/headshot</hitImgUrl>
				</hurt>
				
			</hurtArr>
		</body>	
	</father>
	<father name="enemy">
		<bullet cnName="法球">
			<name>SkeletalMageShoot</name>
			<cnName>法球</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>5</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.7</attackGap>
			<attackDelay>0.4</attackDelay>
			<bulletAngle>185</bulletAngle>
			<bulletAngleRange>20</bulletAngleRange>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>				<!-- 1个攻击间隔内的射击次数（默认值为1）-
			->	
								
			<shootAngle>0</shootAngle>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-104,-68</shootPoint>
			<bulletSpeed>12</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<followD value="0.3" maxTime="3"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" con="add">bullet/skeletonBullet</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/fireHit2">SkeletalMage/boom</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		
		
		<bullet cnName="追魂术-子弹">
			<name>SkeletalMageFollowBullet</name>
			<cnName>追魂术-子弹</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>1.5</bulletLife>
			<bulletWidth>50</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.4</attackGap>
			<attackDelay>1</attackDelay>
			<bulletAngle>185</bulletAngle>
			<bulletAngleRange>180</bulletAngleRange>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>				<!-- 1个攻击间隔内的射击次数（默认值为1）-
			->	
								
			<shootAngle>0</shootAngle>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-33,-96</shootPoint>
			<bulletSpeed>30</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<bulletSkillArr>spiralMove,SkeletalMageFollowDieEvent</bulletSkillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" con="filter">bullet/skeletonBullet</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/fireHit2">SkeletalMage/boom</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		
					<bullet cnName="骷髅权杖-子弹">
						<name>skeletonWandBullet</name>
						<cnName>骷髅权杖-子弹</cnName>
						<hurtRatio>0.00001</hurtRatio>
						<attackType>holy</attackType>
						<!--基本属性------------------------------------------------------------ -->
						<shakeAngle>5</shakeAngle>
						<bulletLife>6</bulletLife>
						<bulletWidth>50</bulletWidth>
						<hitType>rect</hitType>
						<!--攻击时的属性------------------------------------------------------------ -->
						<attackGap>0</attackGap>
						<attackDelay>0</attackDelay>
						<bulletAngle>185</bulletAngle>
						<!--运动属性------------------------------------------------------------ -->	
						<shootPoint>-20,-80</shootPoint>
						<bulletSpeed>30</bulletSpeed>
						<penetrationGap>1000</penetrationGap>
						<followD value="0.5"/>
						<skillArr>skeletonWandHit</skillArr>
						
						<!--图像动画属性------------------------------------------------------------ -->
						<flipX>1</flipX>
						<bulletImgUrl raNum="30" con="filter">bullet/skeletonBullet</bulletImgUrl>
						<hitImgUrl con="add" soundUrl="sound/fireHit2">boomEffect/bigCircle</hitImgUrl><!-- 子弹图像【必备】 -->
					</bullet>
					
		
		
		<skill name="追魂术">
			<name>SkeletalMageFollow</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>追魂术</cnName>
			<cd>8</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>1.4</duration>
			<meActionLabel>followAttack</meActionLabel>
			<description>骷髅巫师化成一道灵魂波攻击敌人，击中敌人后现身，并对敌人造成重大伤害。</description>
		</skill>
		
		<skill name="制魂术">
			<name>SkeletalMageCloned</name>
			<cnName>制魂术</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>10</cd>
			<firstCd>10</firstCd>
			<delay>0.9</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition><!-- cd结束后触发 -->
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.4</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>SkeletalMageCloned</effectType>
			<value>3</value>
			<mul>0.25</mul>
			<duration>30</duration>
			
			<linkArr>SkeletalMageHeroAngerAdd</linkArr>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>shadowAttack</meActionLabel>
			<pointEffectImg con="add">bulletHitEffect/purpleLaser</pointEffectImg>
			<description>骷髅巫师生命值低于40%时制造3个魂魄，此时骷髅巫师进入无敌施法状态。这3个魂魄只受近战副手攻击，消灭1个魂魄将对骷髅巫师造成最大生命值3%的伤害。</description>
		</skill>
		
		
					<skill cnName="分身只受副手攻击"><!-- 生存-被动 -->
						<name>SkeletalMageClonedUnder</name>
						<cnName>分身只受副手攻击</cnName><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>underHit</condition>
						<target>target</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instant</addType>
						<effectType>SkeletalMageClonedUnder</effectType>
						<mul>0.03</mul>
						<!--图像------------------------------------------------------------ -->
						<meEffectImg></meEffectImg>
						<pointEffectImg con="add" soundUrl="sound/fireHit2">bulletHitEffect/headshot</pointEffectImg>
						
						<description>分身只受副手攻击，受到副手攻击后，自身死亡，主人受到相当于自身生命值[mul]的伤害。</description>
					</skill>
					
					<skill index="0" name="让P1角色副手怒气回复速度增加"><!-- dps -->
						<name>SkeletalMageHeroAngerAdd</name>
						<cnName>让P1角色副手怒气回复速度增加</cnName>
						<!--英雄技能属性------------------------------------------------------------ -->
						<cd>0.1</cd>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>active</conditionType>
						<target>me,range,enemy</target>
						<!--效果------------------------------------------------------------ -->
						<addType>state</addType>
						<effectType>angerAddMul</effectType>
						<mul>3</mul>
						<duration>999999</duration>
						<range>99999</range>
					</skill>
	</father>
	
</data>