<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="pet" cnName="尸宠">
		<body index="0" name="战斗僵尸">
			
			<name>PetZombieBattle</name>
			<cnName>战斗僵尸</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/pet/PetZombieBattle.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>0.8</lifeRatio>
			<!-- 图像 -->
			<headIconUrl>IconGather/PetZombieBattle</headIconUrl>
			<imgArr>
				stand,move,run
				,normalAttack1,normalAttack2,hurt1,hurt2,die1,die2
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,__fill2_Up,fill2_Up,fill2_Up__fill2_Down,fill2_Down,fill2_Down__die2
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-76,28,76</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr></bossSkillArr>
			<bossSkillArrCn>瞬移，闪电麻痹，自燃</bossSkillArrCn>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack1</imgLabel>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="PetZombieBattle/hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>normalAttack2</imgLabel>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="PetZombieBattle/hit2">bladeHitEffect/blood</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
		<body index="0" name="狂战尸">
			
			<name>PetFightKing</name>
			<cnName>狂战尸</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/pet/PetFightKing.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.8</lifeRatio>
			<rateRatio>0.05</rateRatio>
			<!-- 图像 -->
			<headIconUrl>IconGather/PetFightKing</headIconUrl>
			<imgType>normal</imgType>
			<imgArr>
				stand,move
				,comboAttack,choppedAttack,windAttack,shootAttack,shakeAttack
				,hurt1,hurt2,die1,die2
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,__fill2_Up,fill2_Up,fill2_Up__fill2_Down,fill2_Down,fill2_Down__die2
			</imgArr>
			<lifeBarExtraHeight>-20</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>7</maxVx>
			<runStartVx>12</runStartVx>
			<!-- AI属性 -->
			<avtiveSkillCdOverT>0.2</avtiveSkillCdOverT>
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>FightKing_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr></bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<extraDropArmsB>1</extraDropArmsB>
			<!-- 攻击数据 -->
			<hurtArr>
				
				<hurt>
					<imgLabel>comboAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>0.4</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>7</shakeValue>
					<hitImgUrl con="add" soundUrl="PetFightKing/hit2">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>choppedAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="PetFightKing/hit3" shake="3,0.2,25">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt info="狂刃追踪-不加入ai选择">
					<imgLabel>shootAttack</imgLabel>
					<hurtRatio>2</hurtRatio>
					<noAiChooseB>1</noAiChooseB>
					<bulletLabel>PetFightKing_shoot</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<attackType>direct</attackType>
				</hurt>
				<hurt info="旋风刀-不加入ai选择">
					<imgLabel>windAttack</imgLabel>
					<noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.8</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>6</shakeValue>
					<grapRect>-350,-111,300,105</grapRect>
					<hitImgUrl con="add" soundUrl="PetFightKing/hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt info="怒刃-不加入ai选择">
					<imgLabel>shakeAttack</imgLabel>
					<noAiChooseB>1</noAiChooseB>
					<hurtRatio>2.5</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>10</shakeValue>
					<skillArr>slowMove_GasBomb</skillArr>
					<hitImgUrl con="add" soundUrl="PetFightKing/hit1" shake="5,0.4,35">bladeHitEffect/blood</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
		<body index="0" name="屠刀僵尸">
			
			<name>PetZombieCleaver</name>
			<cnName>屠刀僵尸</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/pet/PetZombieCleaver.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.3</lifeRatio>
			
			<!-- 图像 -->
			<headIconUrl>IconGather/PetZombieCleaver</headIconUrl>
			<imgArr>
				stand,move
				,normalAttack1,normalAttack2,hurt1,hurt2,die1,die2
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,__fill2_Up,fill2_Up,fill2_Up__fill2_Down,fill2_Down,fill2_Down__die2
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-18,-86,36,86</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<!-- AI属性 -->
			<!-- 技能 -->
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack2</imgLabel>
					<bulletLabel>PetZombieCleaver_1</bulletLabel>
					<grapRect>-400,-111,350,105</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
			</hurtArr>
		</body>	
		<body index="0" name="僵尸王">
			
			<name>PetZombieKing</name>
			<cnName>僵尸王</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/pet/PetZombieKing.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.8</lifeRatio>
			<rateRatio>0.05</rateRatio>
			<!-- 图像 -->
			<headIconUrl>IconGather/PetZombieKing</headIconUrl>
			<imgType>normal</imgType>
			<imgArr>
				stand,move,run
				,floorAttack,gunAttack,normalAttack1,normalAttack2,hurt1,hurt2,die1,die2
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,__fill2_Up,fill2_Up,fill2_Up__fill2_Down,fill2_Down,fill2_Down__die2
			</imgArr>
			<lifeBarExtraHeight>-20</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>8</maxVx>
			<runStartVx>10</runStartVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr></bossSkillArr>
			<extraDropArmsB>1</extraDropArmsB>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>floorAttack</imgLabel>
					<bulletLabel>PetZombieKing_floor</bulletLabel>
					<grapRect>-350,-111,200,105</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt  info="极速炮轰-不加入ai选择">
					<imgLabel>gunAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>PetZombieKing_gun</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>0.95</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt>
					<imgLabel>normalAttack1</imgLabel>
					<hurtRatio>2</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="PetZombieKing/hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>normalAttack2</imgLabel>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="PetZombieKing/hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>
		<body index="0" name="橄榄僵尸">
			
			<name>PetZombieFootball</name>
			<cnName>橄榄僵尸</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/pet/PetZombieFootball.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>0.6</lifeRatio>
			<expRatio>0.8</expRatio>
			<headHurtMul>0.3</headHurtMul>
			<!-- 图像 -->
			<headIconUrl>IconGather/PetZombieFootball</headIconUrl>
			<imgArr>
				stand,move
				,normalAttack1,normalAttack2,shootAttack2,hurt1,hurt2,die1,die2
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,__fill2_Up,fill2_Up,fill2_Up__fill2_Down,fill2_Down,fill2_Down__die2
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-76,28,76</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<!-- AI属性 -->
			<nextAttackTime>1</nextAttackTime>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr></bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack1</imgLabel>
					<bulletLabel>PetZombieFootball_1</bulletLabel>
					<grapRect>-350,-111,300,105</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt>
					<imgLabel>normalAttack2</imgLabel>
					<hurtRatio>1</hurtRatio>
					<shakeValue>2</shakeValue>
					<attackType>direct</attackType>
					<hitImgUrl con="add" soundUrl="PetZombieFootball/hit1" raNum="30">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>shootAttack2</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>PetZombieFootball_2</bulletLabel>
					<grapRect>-350,-111,300,105</grapRect>
					<hurtRatio>0.5</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
			</hurtArr>
		</body>	
		
		<body index="0" name="飓风巫尸">
			
			<name>PetTyphoonWitch</name>
			<cnName>飓风巫尸</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/pet/PetTyphoonWitch.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.5</lifeRatio>
			<rateRatio>0.05</rateRatio>
			<showLevel>65</showLevel>
			<!-- 图像 -->
			<headIconUrl>IconGather/PetTyphoonWitch</headIconUrl>
			<flipCtrlBy>target</flipCtrlBy>
			<imgType>normal</imgType>
			<imgArr>
				stand,move,__stru,stru,hurt1,hurt2,die1
				,normalAttack1,shootAttack,shootAttack2,batAttack,windAttack,roarAttack
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<lifeBarExtraHeight>-40</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>7</maxVx>
			<motionState>fly</motionState>
			<flyUseSpiderB>1</flyUseSpiderB>
			<flyType>tween</flyType>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr></bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<extraDropArmsB>1</extraDropArmsB>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack1</imgLabel>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="PetTyphoonWitch/hit2">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<bulletLabel>PetTyphoonWitch_shoot1</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>0.5</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				
				<hurt info="不加入ai选择">
					<imgLabel>shootAttack2</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>PetTyphoonWitch_shoot2</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>10</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				
				<hurt info="不加入ai选择">
					<imgLabel>windAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>PetTyphoonWitch_wind</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>roarAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>PetTyphoonWitch_anger</bulletLabel>
					<grapRect>-500,-111,100,105</grapRect>
					<hurtRatio>0.5</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
			</hurtArr>
		</body>
		
		<body index="0" name="铁魁" shell="metal">
			
			<name>PetIronChief</name>
			<cnName>铁魁</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/pet/PetIronChief.swf</swfUrl>
			<headIconUrl>IconGather/PetIronChief</headIconUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.3</lifeRatio>
			<!-- 图像 -->
			<imgArr>
				stand,move
				,normalAttack1,shootAttack1,shootAttack2
				,hurt1,die1
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-18,-86,36,86</hitRect>
			<!-- 运动 -->
			<maxVx>7</maxVx>
			<maxJumpNum>1</maxJumpNum>
			<motionState>fly</motionState>
			<flyUseSpiderB>1</flyUseSpiderB>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr></bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack1</imgLabel>
					<hurtRatio>2</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/metal_hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack1</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<bulletLabel>PetIronChief_shoot1</bulletLabel>
					<grapRect>-250,-111,100,105</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt>
					<imgLabel>shootAttack2</imgLabel>
					<bulletLabel>PetIronChief_shoot2</bulletLabel>
					<grapRect>-350,-111,100,105</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
			</hurtArr>
		</body>	
		
		<body index="0" name="爆骷" shell="compound">
		
			<name>PetBoomSkull</name>
			<cnName>爆骷</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/pet/PetBoomSkull.swf</swfUrl>
			<headIconUrl>IconGather/PetBoomSkull</headIconUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgType>normal</imgType>
			<rotateBySlopeB>1</rotateBySlopeB>
			<imgArr>
				stand,move,die1
				,shootAttack1,fireAttack1
				,__jumpUp,jumpUp,jumpDown,jumpDown__
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<motionD F_G="0.6" jumpDelayT="0.10" F_I="0.2" F_F="0.9" moveWhenVB="1" />
			<maxVx>10</maxVx>
			<maxJumpNum>1</maxJumpNum>
			<!-- 技能 -->
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>fireAttack1</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<grapMinLen>120</grapMinLen>
					<hurtRatio>0.25</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/fireHit1">bulletHitEffect/spark_motion2</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack1</imgLabel>
					<bulletLabel>PetBoomSkull_shoot1</bulletLabel>
					<grapRect>-350,-111,100,105</grapRect>
					<hurtRatio>1.6</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				
				<hurt info="不加入ai选择">
					<imgLabel>move</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>jumpDown</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>jumpDown__</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>
		
		<body index="0" name="嗜血尸狼">
			
			<name>PetZombieWolf</name>
			<cnName>嗜血尸狼</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/pet/PetZombieWolf.swf</swfUrl>
			<headIconUrl>IconGather/PetZombieWolf</headIconUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<imgArr>
				stand,move,run
				,normalAttack1,normalAttack2,laserAttack,summonAttack,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-30,28,30</hitRect>
			<lifeBarExtraHeight>-20</lifeBarExtraHeight>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>9</maxVx>
			<runStartVx>11</runStartVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>ZombieWolf_AIExtra</extraAIClassLabel>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack1</imgLabel>
					<hurtRatio>3</hurtRatio>
					<shakeValue>4</shakeValue>
					<attackType>direct</attackType>
					<hitImgUrl con="add" soundUrl="sound/body_hit" raNum="30">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>normalAttack2</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>3</hurtRatio>
					<shakeValue>4</shakeValue>
					<attackType>direct</attackType>
					<grapRect>-171,-100,128,120</grapRect>
					<hitImgUrl con="add" soundUrl="sound/body_hit" raNum="30">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				
				<hurt info="不加入ai选择">
					<imgLabel>laserAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<skillArr>laser_PetFightWolf_extra,dizziness_anger_PetFightWolf</skillArr>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>0.3</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>7</shakeValue>
					<hitImgUrl con="add">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt  info="不加入ai选择">
					<imgLabel>summonAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>summonWolf_FightWolf_slow</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>0.01</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>7</shakeValue>
				</hurt>
			</hurtArr>
		</body>
		
		<body index="0" name="雷克" shell="compound">
			
			<name>PetLake</name>
			<cnName>雷克</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/pet/PetLake.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<!-- 图像 -->
			<headIconUrl>IconGather/PetLake</headIconUrl>
			<imgArr>
				stand,move
				,normalAttack,shootAttack,chargedAttack,ballAttack,staticAttack
				,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-76,28,76</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>PetLake_AIExtra</extraAIClassLabel>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel>
					<hurtRatio>2</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/hand_hit">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel>
					<hurtRatio>2</hurtRatio>
					<bulletLabel>shoot_PetLake</bulletLabel>
					<grapRect>-450,-88,351,77</grapRect>
				</hurt>
				
				<hurt info="不加入ai选择-充能">
					<imgLabel>chargedAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-400,-111,100,105</grapRect>
				</hurt>
				<hurt info="不加入ai选择-辐射光球">
					<imgLabel>ballAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>lightBall_PetLake</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>12</hurtRatio>
				</hurt>
				<hurt info="不加入ai选择-充能">
					<imgLabel>staticAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-400,-111,100,105</grapRect>
				</hurt>
			</hurtArr>
		</body>	
		<body index="0" name="雷尔" shell="compound">
			
			<name>PetLaer</name>
			<cnName>雷尔</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/pet/PetLaer.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<!-- 图像 -->
			<headIconUrl>IconGather/PetLaer</headIconUrl>
			<imgArr>
				stand,move
				,normalAttack,shootAttack,chargedAttack,ballAttack,staticAttack
				,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-76,28,76</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>PetLake_AIExtra</extraAIClassLabel>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel>
					<hurtRatio>2</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/hand_hit">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel>
					<hurtRatio>2</hurtRatio>
					<bulletLabel>shoot_PetLaer</bulletLabel>
					<grapRect>-450,-88,351,77</grapRect>
				</hurt>
				
				<hurt info="不加入ai选择-充能">
					<imgLabel>chargedAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-400,-111,100,105</grapRect>
				</hurt>
				<hurt info="不加入ai选择-辐射光球">
					<imgLabel>ballAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>lightBall_PetLaer</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>12</hurtRatio>
				</hurt>
				<hurt info="不加入ai选择-充能">
					<imgLabel>staticAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-400,-111,100,105</grapRect>
				</hurt>
			</hurtArr>
		</body>	
	</father>	
		
		
	
	
	<father name="pet" cnName="升级后尸宠">
		
		<body index="0" name="混沌狂战尸">
			
			<name>PetChaosKing</name>
			<cnName>混沌狂战尸</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/pet/PetChaosKing.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.8</lifeRatio>
			<rateRatio>0.05</rateRatio>
			<!-- 图像 -->
			<headIconUrl>IconGather/PetChaosKing</headIconUrl>
			<imgType>normal</imgType>
			<imgArr>
				stand,move
				,comboAttack,choppedAttack,windAttack,shootAttack,shakeAttack
				,hurt1,hurt2,die1,die2
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,__fill2_Up,fill2_Up,fill2_Up__fill2_Down,fill2_Down,fill2_Down__die2
			</imgArr>
			<lifeBarExtraHeight>-20</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>7</maxVx>
			<runStartVx>12</runStartVx>
			<!-- AI属性 -->
			<avtiveSkillCdOverT>0.2</avtiveSkillCdOverT>
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>FightKing_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr></bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<extraDropArmsB>1</extraDropArmsB>
			<!-- 攻击数据 -->
			<hurtArr>
				
				<hurt>
					<imgLabel>comboAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>0.4</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>7</shakeValue>
					<hitImgUrl con="add" soundUrl="PetChaosKing/hit2">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>choppedAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="PetChaosKing/hit3" shake="3,0.2,25">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt info="狂刃追踪-不加入ai选择">
					<imgLabel>shootAttack</imgLabel>
					<hurtRatio>2</hurtRatio>
					<noAiChooseB>1</noAiChooseB>
					<bulletLabel>PetChaosKing_shoot</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<attackType>direct</attackType>
				</hurt>
				<hurt info="旋风刀-不加入ai选择">
					<imgLabel>windAttack</imgLabel>
					<noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.8</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>6</shakeValue>
					<grapRect>-350,-111,300,105</grapRect>
					<hitImgUrl con="add" soundUrl="PetChaosKing/hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt info="怒刃-不加入ai选择">
					<imgLabel>shakeAttack</imgLabel>
					<noAiChooseB>1</noAiChooseB>
					<hurtRatio>2.5</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>10</shakeValue>
					<skillArr>slowMove_GasBomb</skillArr>
					<hitImgUrl con="add" soundUrl="PetChaosKing/hit1" shake="5,0.4,35">bladeHitEffect/blood</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
		
		<body index="0" name="钢铁僵尸王" shell="metal">
			
			<name>PetIronZombieKing</name>
			<cnName>僵尸王</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/pet/PetIronZombieKing.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.8</lifeRatio>
			<rateRatio>0.05</rateRatio>
			<!-- 图像 -->
			<headIconUrl>IconGather/PetIronZombieKing</headIconUrl>
			<imgType>normal</imgType>
			<imgArr>
				stand,move,run
				,floorAttack,gunAttack,normalAttack1,normalAttack2,hurt1,hurt2,die1,die2
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,__fill2_Up,fill2_Up,fill2_Up__fill2_Down,fill2_Down,fill2_Down__die2
			</imgArr>
			<lifeBarExtraHeight>-20</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>8</maxVx>
			<runStartVx>10</runStartVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr></bossSkillArr>
			<extraDropArmsB>1</extraDropArmsB>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>floorAttack</imgLabel>
					<bulletLabel>PetIronZombieKing_floor</bulletLabel>
					<grapRect>-350,-111,200,105</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt  info="极速炮轰-不加入ai选择">
					<imgLabel>gunAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>PetIronZombieKing_gun</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>0.95</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt>
					<imgLabel>normalAttack1</imgLabel>
					<hurtRatio>2</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="PetIronZombieKing/hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>normalAttack2</imgLabel>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="PetIronZombieKing/hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>
		<body index="0" name="金盔僵尸">
			
			<name>PetZombieHelmet</name>
			<cnName>金盔僵尸</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/pet/PetZombieHelmet.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>0.6</lifeRatio>
			<expRatio>0.8</expRatio>
			<headHurtMul>0.3</headHurtMul>
			<!-- 图像 -->
			<headIconUrl>IconGather/PetZombieHelmet</headIconUrl>
			<imgArr>
				stand,move
				,normalAttack1,normalAttack2,shootAttack2,hurt1,hurt2,die1,die2
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,__fill2_Up,fill2_Up,fill2_Up__fill2_Down,fill2_Down,fill2_Down__die2
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-76,28,76</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<!-- AI属性 -->
			<nextAttackTime>1</nextAttackTime>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr></bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack1</imgLabel>
					<bulletLabel>PetZombieHelmet_1</bulletLabel>
					<grapRect>-350,-111,300,105</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt>
					<imgLabel>normalAttack2</imgLabel>
					<hurtRatio>1</hurtRatio>
					<shakeValue>2</shakeValue>
					<attackType>direct</attackType>
					<hitImgUrl con="add" soundUrl="PetZombieHelmet/hit1" raNum="30">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>shootAttack2</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>PetZombieHelmet_2</bulletLabel>
					<grapRect>-350,-111,300,105</grapRect>
					<hurtRatio>0.5</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
			</hurtArr>
		</body>	
		
		
		<body index="0" name="铁魁X2" shell="metal">
			
			<name>PetIronChiefSecond</name>
			<cnName>铁魁</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/pet/PetIronChiefSecond.swf</swfUrl>
			<headIconUrl>IconGather/PetIronChiefSecond</headIconUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.3</lifeRatio>
			<!-- 图像 -->
			<imgArr>
				stand,move
				,normalAttack1,shootAttack1,shootAttack2
				,hurt1,die1
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-18,-86,36,86</hitRect>
			<!-- 运动 -->
			<maxVx>7</maxVx>
			<maxJumpNum>1</maxJumpNum>
			<motionState>fly</motionState>
			<flyUseSpiderB>1</flyUseSpiderB>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr></bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack1</imgLabel>
					<hurtRatio>2</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/metal_hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack1</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<bulletLabel>PetIronChief_shoot1</bulletLabel>
					<grapRect>-250,-111,100,105</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt>
					<imgLabel>shootAttack2</imgLabel>
					<bulletLabel>PetIronChief_shoot2</bulletLabel>
					<grapRect>-350,-111,100,105</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
			</hurtArr>
		</body>	
		<body index="0" name="哈迪斯" shell="metal">
			
			<name>PetIronChiefThird</name>
			<cnName>哈迪斯</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/pet/PetIronChiefThird210.swf</swfUrl>
			<headIconUrl>IconGather/PetIronChiefThird</headIconUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.3</lifeRatio>
			<!-- 图像 -->
			<imgArr>
				stand,move
				,normalAttack1,normalAttack2,shootAttack,sprintAttack
				,hurt1,die1
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-18,-86,36,86</hitRect>
			<!-- 运动 -->
			<maxVx>7</maxVx>
			<maxJumpNum>1</maxJumpNum>
			<motionState>fly</motionState>
			<flyUseSpiderB>1</flyUseSpiderB>
			<flyType>tween</flyType>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr></bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack1</imgLabel>
					<hurtRatio>2</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/metal_hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>normalAttack2</imgLabel>
					<hurtRatio>5</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/metal_hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<bulletLabel>PetIronChiefThird_shoot1</bulletLabel>
					<grapRect>-250,-111,100,105</grapRect>
					<hurtRatio>0.3</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				
				
				<hurt info="不参加ai选择">
					<imgLabel>sprintAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>PetIronChief_shoot2</bulletLabel>
					<grapRect>-350,-111,100,105</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
			</hurtArr>
		</body>	
		<body index="0" name="爆骷X2" shell="compound">
		
			<name>PetBoomSkullSecond</name>
			<cnName>爆骷X2</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/pet/PetBoomSkullSecond.swf</swfUrl>
			<headIconUrl>IconGather/PetBoomSkullSecond</headIconUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgType>normal</imgType>
			<rotateBySlopeB>1</rotateBySlopeB>
			<imgArr>
				stand,move,die1
				,shootAttack1,fireAttack1
				,__jumpUp,jumpUp,jumpDown,jumpDown__
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<motionD F_G="0.6" jumpDelayT="0.10" F_I="0.2" F_F="0.9" moveWhenVB="1" />
			<maxVx>10</maxVx>
			<maxJumpNum>1</maxJumpNum>
			<!-- 技能 -->
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>fireAttack1</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<grapMinLen>120</grapMinLen>
					<hurtRatio>0.25</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/fireHit1">bulletHitEffect/spark_motion2</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack1</imgLabel>
					<bulletLabel>PetBoomSkullSecond_shoot1</bulletLabel>
					<grapRect>-350,-111,100,105</grapRect>
					<hurtRatio>1.6</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				
				<hurt info="不加入ai选择">
					<imgLabel>move</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>jumpDown</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>jumpDown__</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>
		<body index="0" name="爆骷X3" shell="compound">
		
			<name>PetBoomSkullThird</name>
			<cnName>爆骷X3</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/pet/PetBoomSkullThird.swf</swfUrl>
			<headIconUrl>IconGather/PetBoomSkullThird</headIconUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgType>normal</imgType>
			<rotateBySlopeB>1</rotateBySlopeB>
			<imgArr>
				stand,move,die1
				,shootAttack1,shootAttack2,shootAttack3
				,__jumpUp,jumpUp,jumpDown,jumpDown__
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<motionD F_G="0.6" jumpDelayT="0.10" F_I="0.2" F_F="0.9" moveWhenVB="1" />
			<maxVx>10</maxVx>
			<maxJumpNum>1</maxJumpNum>
			<!-- 技能 -->
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				
				<hurt>
					<imgLabel>shootAttack1</imgLabel>
					<bulletLabel>PetBoomSkullThird_shoot1</bulletLabel>
					<grapRect>-300,-111,100,105</grapRect>
					<hurtRatio>3.2</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt>
					<imgLabel>shootAttack2</imgLabel>
					<bulletLabel>PetBoomSkullThird_machine</bulletLabel>
					<grapRect>-500,-175,300,247</grapRect>
					<hurtRatio>0.2</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				<hurt>
					<imgLabel>shootAttack3</imgLabel>
					<bulletLabel>PetBoomSkullThird_shoot3</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>1.8</hurtRatio>
					<attackType>direct</attackType>
				</hurt>
				
				<hurt info="不加入ai选择">
					<imgLabel>move</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>jumpDown</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>jumpDown__</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>
		
		<body index="0" name="狂战狼" shell="normal">
			
			<name>PetFightWolf</name>
			<cnName>狂战狼</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/pet/PetFightWolf.swf</swfUrl>
			<headIconUrl>IconGather/PetFightWolf</headIconUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.8</lifeRatio>
			<rosRatio>1</rosRatio>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<imgArr>
				stand,move
				,fistAttack,hammerAttack,legAttack,stoneAttack,laserAttack,summonAttack
				,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<lifeBarExtraHeight>-20</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>8</maxVx>
			<runStartVx>12</runStartVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>FightWolf_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<preBulletArr>anger_FightWolf</preBulletArr>
			<!-- 副本ai数据 -->
			
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>fistAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>1.5</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>7</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>hammerAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>3</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>7</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bladeHitEffect/blood</hitImgUrl>
				</hurt> 
				<hurt>
					<imgLabel>legAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>1.5</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>7</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				
				<hurt info="不加入ai选择">
					<imgLabel>laserAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<skillArr>laser_PetFightWolf_extra</skillArr>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>0.3</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>7</shakeValue>
					<hitImgUrl con="add">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				<hurt  info="不加入ai选择">
					<imgLabel>summonAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>summonWolf_FightWolf_slow</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>0.01</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>7</shakeValue>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>stoneAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>0.2</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>7</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/body_hit">bladeHitEffect/blood</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
	</father>	
</data>