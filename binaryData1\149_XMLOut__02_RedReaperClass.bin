<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="car" cnName="战车定义">
		<equip name="RedReaper" cnName="收割者" evolutionLabel="RedCrusher">
			<canComposeB>1</canComposeB>
			<main dpsMul="0.7" len="67"/>
			<sub dpsMul="0.9" len="47" />
			<mainFrontB>1</mainFrontB>
			<lifeMul>1.2</lifeMul>
			<attackMul>1.3</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>120</mustCash>
			<addObjJson>{'dpsAll':0.05,'lifeAll':0.05}</addObjJson>
		</equip>
		<equip name="RedCrusher" cnName="碾压者" evolutionLabel="RedSweeps">
			<evolutionLv>2</evolutionLv>
			<main label="RedReaper_main" dpsMul="0.9" len="67"/>
			<sub label="RedReaper_sub" dpsMul="1.2" len="47" />
			<mainFrontB>1</mainFrontB>
			<lifeMul>1.2</lifeMul>
			<attackMul>1.6</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>120</mustCash>
			<addObjJson>{'dpsAll':0.08,'lifeAll':0.08}</addObjJson>
			<skillArr>vehicleFit_Civilian</skillArr>
		</equip>
		<equip name="RedSweeps" cnName="扫荡者" evolutionLabel="">
			<evolutionLv>4</evolutionLv>
			<main label="RedReaper_main" dpsMul="1.1" len="67"/>
			<sub label="RedReaper_sub" dpsMul="1.4" len="47" />
			<mainFrontB>1</mainFrontB>
			<lifeMul>1.7</lifeMul>
			<attackMul>2</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>120</mustCash>
			<addObjJson>{'dpsAll':0.1,'lifeAll':0.1}</addObjJson>
			<skillArr>vehicleFit_Civilian</skillArr>
		</equip>
		
		
		<bullet cnName="收割者-主炮">
			<name>RedReaper_main</name>
			<cnName>收割者-主炮</cnName>
			<!--武器属性------------------------------------------------------------ -->
			<hurtRatio>1.07</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>1</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.8</attackGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootRecoil>15</shootRecoil>
			<screenShakeValue>16</screenShakeValue>
			<bulletSpeed>45</bulletSpeed>
			<boomD  bodyB="1" floorB="1" radius="120"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">rocket/rocketBullet</bulletImgUrl>
			<fireImgUrl raNum="30" soundUrl="rocket/barrel2_sound" con="add">gunFire/rocket</fireImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom1"  shake="2,0.2,10">boomEffect/boom3</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="收割者-副炮">
			<name>RedReaper_sub</name>
			<cnName>收割者-副炮</cnName>
			<!--武器属性------------------------------------------------------------ -->
			<hurtRatio>0.86</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>0.001</bulletLife>
			<hitType>longLine</hitType>
			<bulletWidth>800</bulletWidth>
			<bulletShakeWidth>100</bulletShakeWidth>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.13</attackGap>
			<attackDelay>0</attackDelay>
			<!--运动属性------------------------------------------------------------ -->	
			<shootRecoil>4</shootRecoil>
			<screenShakeValue>9</screenShakeValue>
			<shakeAngle>3</shakeAngle>
			<bulletSpeed>0</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<lineD lightColor="0xFFFF00" size="2" lightSize="6"/>
			<bulletImgUrl>longLine</bulletImgUrl>
			<fireImgUrl raNum="30" soundUrl="ak/barrel2_sound">gunFire/f</fireImgUrl>
			<hitImgUrl>bulletHitEffect/yellow_motion</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
	</father>
	
	
	
	
	
	
	<father name="vehicle" cnName="战车body">
		<body index="0" name="收割者" shell="metal">
			
			<name>RedReaper</name>
			<cnName>收割者</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/vehicle/RedReaper38.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgClass>CarImage</imgClass>
			<imgType>normal</imgType>
			<rotateBySlopeB>1</rotateBySlopeB>
			<imgArr>
				stand,move,die1
				,__jumpUp,jumpUp,jumpDown,jumpDown__
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<motionD F_G="0.8" jumpDelayT="0.15" F_I="0.3" F_F="0.9" moveWhenVB="1" />
			<maxVx>16</maxVx>
			<maxJumpNum>1</maxJumpNum>
			<!-- 技能 -->
			<attackAIClass>CarAttack_AI</attackAIClass>
			<keyClass>CarBodyKey</keyClass>
			<bulletLauncherClass>CarBulletLauncher</bulletLauncherClass>
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>move</imgLabel>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpDown</imgLabel>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpUp</imgLabel>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpDown__</imgLabel>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>
		
		<body name="碾压者" fixed="RedReaper" shell="metal">
			<name>RedCrusher</name>
			<cnName>碾压者</cnName>
			<swfUrl>swf/vehicle/RedCrusher.swf</swfUrl>
			<bmpUrl>BodyImg/RedCrusher</bmpUrl>
		</body>
		<body name="扫荡者" fixed="RedReaper" shell="metal">
			<name>RedSweeps</name>
			<cnName>扫荡者</cnName>
			<swfUrl>swf/vehicle/RedSweeps.swf</swfUrl>
			<bmpUrl>BodyImg/RedSweeps</bmpUrl>
		</body>
		
	</father>
</data>