<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="car" cnName="战车定义">
		<equip name="NianCar" cnName="年兽"  rideLabel="RedMotoRide">
			<main label="Diggers_sub" hideB="1" dpsMul="0"/>
			<sub label="Diggers_sub" hideB="1" dpsMul="0"/>
			<lifeMul>6</lifeMul>
			<attackMul>1</attackMul>
			<duration>60</duration>
			<cd>110</cd>
			<addObjJson>{'dpsAll':0.07,'lifeAll':0.07}</addObjJson>
			<specialInfoArr>受到必亡伤害时无敌</specialInfoArr>
			<canComposeB>1</canComposeB>
			<onceB>1</onceB>
			<mustCash>210</mustCash>
		</equip>
	</father>
	<father name="vehicle" cnName="战车body">
		<body shell="compound">
			
			<name>NianCar</name>
			<cnName>年兽坐骑</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/vehicle/NianCar.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>1</headHurtMul>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgClass>CarImage</imgClass>
			<imgType>normal</imgType>
			<rotateBySlopeB>1</rotateBySlopeB>
			<imgArr>
				stand,move
				,__jumpUp,jumpUp,jumpDown,jumpDown__,jumpUp__jumpDown
				,dartsAttack
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<maxVx>8</maxVx>
			<maxJumpNum>1</maxJumpNum>
			
			<!-- 技能 -->
			<attackAIClass>CarAttack_AI</attackAIClass>
			<keyClass>CarBodyKey</keyClass>
			<bulletLauncherClass>CarBulletLauncher</bulletLauncherClass>
			<skillArr>godHandNian</skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>move</imgLabel>
					<hurtRatio>0.52</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpDown</imgLabel>
					<hurtRatio>0.52</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/body_hit">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpDown__</imgLabel>
					<hurtRatio>0.9</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/body_hit">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>dartsAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>2</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>3</meBack>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>
		
		<skill cnName="上帝的护佑"><!-- dps -->
			<name>godHandNian</name>
			<cnName>上帝的护佑</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>beforeDie</condition><!-- 被攻击后触发 -->
			<minTriggerT>55</minTriggerT>
			<firstTriggerT>55</firstTriggerT>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>godHand</effectType>
			<value>1</value>
			<duration>8</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/invisibility_hero" con="add"></meEffectImg>
			<description>在受到必亡的伤害时，你将接受上帝的护佑，进入隐身无敌模式，持续[duration]秒，同时剩余1点生命值。技能触发间隔不小于[minTriggerT]秒。</description>
		</skill>
	</father>
</data>