<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="space">
		<body>
			<name>OreSaw</name>
			<cnName>矿棘</cnName><lifeRatio>1</lifeRatio>
			<raceType>ship</raceType>
			<swfUrl>swf/ship/OreSaw.swf</swfUrl>
			<!-- 图像 -->
			<dieImg name="stoneBoom"/>
			<dieJumpMul>0</dieJumpMul>
			<lockLeftB>1</lockLeftB>
			<imgArr>
				stand,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-33,-22,66,44</hitRect>
			<motionState>fly</motionState>
			<flyType>space</flyType>
			<motionD vRan="0" F_AIR="4"/>
			<maxVx>8</maxVx>
			<skillArr>OreSawBuff,hitCheckDie,sumMeSaw</skillArr>
		</body>
		<skill>
			<name>OreSawBuff</name><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
			<cnName>矿棘buff</cnName><everNoClearB>1</everNoClearB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>OreSawBuff</effectType>
			<duration>9999999</duration>
		</skill>
		<skill>
			<name>sumMeSaw</name><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
			<cnName>召唤矿棘</cnName><everNoClearB>1</everNoClearB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>sumMeSaw</effectType>
			<mul>0.2</mul><!-- 每隔这么久召唤1个同类单位 -->
			<!-- 子弹所需 -->
			<obj>"cnName":"矿棘","num":1,"lifeMul":0.5,"dpsMul":1,"mulByFatherB":1,"maxNum":10,"lifeTime":-1</obj>
			<duration>1.2</duration>
			
		</skill>
	</father>
</data>