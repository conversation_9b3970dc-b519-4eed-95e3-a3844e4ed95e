<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="we">
		<body name="大圣分身" shell="normal">
			
			<name>GreatSageShadow</name>
			<cnName>大圣分身</cnName>
			<raceType>human</raceType>
			<showLevel>9999</showLevel>
			<swfUrl>swf/enemy/GreatSageShadow.swf</swfUrl>
			<!-- 基本系数 -->
			<showLevel>999</showLevel>
			<headIconUrl>greatSage/head_icon</headIconUrl>
			<motionD vRan="0.2"/>
			<dieJumpMul>0</dieJumpMul>
			<dieImg name="shadowDie"/>
			<imgType>bmp</imgType>
			
			<imgArr>
				stand,move
				,normalAttack1,normalAttack2,die1
				,greatAttack
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-12, -90, 24, 90</hitRect><!-- 站立碰撞体积-->
			<maxVx>13</maxVx>
			<avtiveSkillCdOverT>0.1</avtiveSkillCdOverT>
			<!-- 技能 -->
			<skillArr>State_SpellImmunity</skillArr>
			<hurtArr>
				<hurt cd="3">
					<imgLabel>normalAttack1</imgLabel><cn>戳棍</cn>
					<hurtRatio>1</hurtRatio>
					<attackType>holy</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl name="fistHit"/>
				</hurt>
				<hurt>
					<imgLabel>normalAttack2</imgLabel><cn>挥棍</cn>
					<hurtRatio>0.5</hurtRatio>
					<attackType>holy</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl name="fistHit"/>
				</hurt>
				
				<hurt info="不加入ai选择">
					<imgLabel>greatAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>4</hurtRatio>
					<attackType>holy</attackType>
					<hitImgUrl name="yellowStickHit"/>
				</hurt>
			</hurtArr>
		</body>
	</father>
	<father name="fashionSkill">
		<skill>
			<name>greatSageSkill</name>
			<cnName>分身</cnName><ignoreNoSkillB>1</ignoreNoSkillB><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>isWeaponHurt,summonedNumLess</otherConditionArr>
			<conditionRange>5</conditionRange>
			<applyArr>great_stickAttack</applyArr>
			<target>me</target>
			<minTriggerT>0.1</minTriggerT>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<summonedUnitsB>1</summonedUnitsB>
			<effectType>greatSageSkill</effectType>
			<extraValueType>heroWeaponHurt</extraValueType>
			<duration>40</duration>
			<targetEffectImg name="cloned_BlackLaer_me"/>
			<!-- 子弹所需 -->
			<obj>"cnName":"大圣分身","num":1,"lifeMul":1,"dpsMul":0.1,"position":"hurtPoint","mulByFatherB":true,"skillArr":["State_noAiFind","State_InvincibleThrough","greatSageSkillHit"]</obj>
			<description>初始拥有30%的怒气。使用沃龙牌棍棒时将有专属动作，专属动作砍击敌人时，都会召唤一个持续[duration]秒的分身，同时还能触发在场的所有分身的砍击动作；分身不受伤害，分身伤害与副手伤害相关；分身每次攻击敌人还能给大圣添加0.6点的怒气。当前最多能同时存在[conditionRange]个分身。</description>
		</skill>
		<skill>
			<name>greatSageSkillHit</name>
			<cnName>分身吸取怒气</cnName><ignoreNoSkillB>1</ignoreNoSkillB><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>meSummonedFather</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>addAnger</effectType>
			<minTriggerT>0.1</minTriggerT>
			<value>0.6</value>
			<description>每次攻击敌人都会给主人添加[value]点的怒气。</description>
		</skill>
	</father>
</data>