<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="deviceSkill" cnName="装置技能">
		<skill index="0" name="地震"><!-- 生存-群体-主动 -->
			<name>earthquake</name>
			<cnName>地震</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0</cd>
			<changeText>持续时间：[duration]秒</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>300</conditionRange>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>dizziness</effectType>
			<duration>1</duration>
			<range>1000</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg shake="10,1,40" soundUrl="sound/earthquake"></meEffectImg>
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
			<description>释放一次强烈地震，使周围[range]码内的所有敌人进入眩晕状态，持续[duration]秒。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><duration>1000</duration></skill>
				<skill><duration>1000</duration></skill>
				<skill><duration>1000</duration></skill>
				<skill><duration>1000</duration></skill>
				<skill><duration>1000</duration></skill>
				<skill><duration>1000</duration></skill>
				<skill><duration>1000</duration></skill>
				<skill><duration>1000</duration><linkArr>earthquake_link</linkArr><changeText>同时降低目标10%的防御力</changeText><description>释放一次强烈地震，使周围[range]码内的所有敌人进入眩晕状态，同时降低目标10%的防御力，持续[duration]秒。</description></skill>
				<skill><duration>1000</duration><ignoreImmunityB>1</ignoreImmunityB><linkArr>earthquake_link</linkArr><changeText>眩晕无视技能免疫</changeText><description>释放一次强烈地震，使周围[range]码内的所有敌人进入眩晕状态（无视技能免疫），同时降低目标10%的防御力，持续[duration]秒。</description></skill>
			</growth>
		</skill>
						<skill cnName="地震-使目标防御力降低"><!-- 生存-主动 -->
							<name>earthquake_link</name>
							<cnName>地震-使目标防御力降低</cnName>
							<!--触发条件与目标------------------------------------------------------------ -->
							<conditionType>passive</conditionType>
							<target>me,range,enemy</target>
							<!--效果------------------------------------------------------------ -->
							<addType>state</addType>
							<effectType>underHurtMul</effectType>
							<mul>1.1</mul>
							<duration>3.6</duration>
							<range>1000</range>
						</skill>
		<skill index="0" name="无尽轰炸">
			<name>endlessRocket</name>
			<cnName>无尽轰炮</cnName>
			<cd>0</cd>
			<changeText>导弹伤害倍数：[mul]</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition><!-- cd结束后触发 -->
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>400</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<extraValueType>nowArmsDpsUI</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<duration>5</duration>
			<mul>0.2</mul>
			<!-- 子弹所需 -->
			<obj>"name":"endlessRocket","site":"me","flipB":true,"launcherB":true,"followB":true</obj>
			<!--图像------------------------------------------------------------ -->
			<description>释放技能后，释放者在5秒之内释放出50颗跟踪弹，每颗跟踪弹的伤害为释放者当前武器战斗力的[mul]。</description>
			<growth>
				<skill><mul>520</mul></skill>
				<skill><mul>520</mul></skill>
				<skill><mul>520</mul></skill>
				<skill><mul>520</mul></skill>
				<skill><mul>520</mul></skill>
				<skill><mul>520</mul></skill>
				<skill><mul>520</mul></skill>
				<skill><mul>520</mul></skill>
				<skill><mul>520</mul></skill>
			</growth>
		</skill>
		
		<skill index="0" cnName="召唤闪电塔"><!-- 限制 -->
			<name>lightningTower</name>
			<cnName>召唤闪电塔</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0</cd>
			<changeText>闪电伤害倍数：[obj.dpsMul]倍</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>200</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<extraValueType>nowArmsDpsUI</extraValueType>
			<!-- 子弹所需 -->
			<obj>"cnName":"闪电塔","num":1,"lifeMul":1,"dpsMul":1,"maxNum":1,"cx":0,"cy":-30,"mulByFatherB":true,"existB":false</obj>
			<duration>15</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/changeToZombie_enemy" con="add">boomEffect/showLight</meEffectImg>
			<description>召唤出一座闪电塔，每隔1秒自动攻击周围的敌人。伤害值为召唤人当前武器战斗力的[obj.dpsMul]倍，闪电塔[duration]秒后消失。</description>
			
			<growth>
				<skill><obj>"cnName":"闪电塔","num":1,"lifeMul":1,"dpsMul":520,"maxNum":1,"cx":0,"cy":-30,"mulByFatherB":true,"existB":false</obj></skill>
				<skill><obj>"cnName":"闪电塔","num":1,"lifeMul":1,"dpsMul":520,"maxNum":1,"cx":0,"cy":-30,"mulByFatherB":true,"existB":false</obj></skill>
				<skill><obj>"cnName":"闪电塔","num":1,"lifeMul":1,"dpsMul":520,"maxNum":1,"cx":0,"cy":-30,"mulByFatherB":true,"existB":false</obj></skill>
				<skill><obj>"cnName":"闪电塔","num":1,"lifeMul":1,"dpsMul":520,"maxNum":1,"cx":0,"cy":-30,"mulByFatherB":true,"existB":false</obj></skill>
				<skill><obj>"cnName":"闪电塔","num":1,"lifeMul":1,"dpsMul":520,"maxNum":1,"cx":0,"cy":-30,"mulByFatherB":true,"existB":false</obj></skill>
				<skill><obj>"cnName":"闪电塔","num":1,"lifeMul":1,"dpsMul":520,"maxNum":1,"cx":0,"cy":-30,"mulByFatherB":true,"existB":false</obj></skill>
				<skill><obj>"cnName":"闪电塔","num":1,"lifeMul":1,"dpsMul":520,"maxNum":1,"cx":0,"cy":-30,"mulByFatherB":true,"existB":false</obj></skill>
				<skill><obj>"cnName":"闪电塔","num":1,"lifeMul":1,"dpsMul":520,"maxNum":1,"cx":0,"cy":-30,"mulByFatherB":true,"existB":false</obj></skill>
				<skill><obj>"cnName":"闪电塔","num":1,"lifeMul":1,"dpsMul":520,"maxNum":1,"cx":0,"cy":-30,"mulByFatherB":true,"existB":false</obj></skill>
			</growth>
		</skill>
		<skill index="0" name="能量外壳"><!-- 生存-主动 -->
			<name>energyShield</name>
			<cnName>能量外壳</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0</cd>
			<changeText>持续时间：[duration]秒{n}作用范围：[range]码</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>weLifePerLess</otherConditionArr>
			<conditionRange>0.3</conditionRange>
			<target>me,range,we</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>energyShield</effectType>
			<duration>1</duration>
			<range>500</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/energyShield"></meEffectImg>
			<stateEffectImg con="add">skillEffect/energyShield</stateEffectImg>
			<description>使周围[range]码内的友方单位获得能量外壳，能量外壳能抵挡任何伤害。持续[duration]秒。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><duration>520</duration><range>99999</range></skill>
				<skill><duration>1000</duration><range>99999</range></skill>
				<skill><duration>1000</duration><range>99999</range></skill>
				<skill><duration>3000</duration><range>99999</range></skill>
				<skill><duration>3000</duration><range>99999</range></skill>
				<skill><duration>4000</duration><range>99999</range></skill>
				<skill><duration>4000</duration><range>99999</range></skill>
				<skill><duration>4000</duration><range>99999</range></skill>
				<skill><duration>5000</duration><range>99999</range></skill>
			</growth>
		</skill>
		
		
		<skill index="0" name="全域电击"><!-- 生存-主动 -->
			<name>electricDevicer</name>
			<cnName>全域电击</cnName>
			<noBeClearB>1</noBeClearB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0</cd>
			<changeText>持续时间：[duration]秒</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me,range,all</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeedNoReduce</effectType>
			<duration>2</duration>
			<value>0</value>
			<mul>0</mul>
			<range>99999</range>
			<linkArr>electricDevicerClearKing</linkArr>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/paralysis_enemy_shoot"></meEffectImg>
			<targetEffectImg con="add">skillEffect/electricDevicer</targetEffectImg>
			<stateEffectImg con="add" soundUrl="sound/paralysis_enemy_hit">skillEffect/paralysis_enemy</stateEffectImg>
			
			<description>向大地释放强大的电流，使地图上的所有单位（包括友方单位）无法移动，持续[duration]秒。此效果不会被净化技能净化。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><duration>2000</duration></skill>
				<skill><duration>3000</duration></skill>
				<skill><duration>4000</duration></skill>
				<skill><duration>5000</duration></skill>
				<skill><duration>6000</duration></skill>
				<skill><duration>7000</duration></skill>
				<skill><duration>6000</duration><target>me,range,enemy</target><changeText>持续时间：[duration]秒{n}只对敌人有效</changeText><description>向大地释放强大的电流，使地图上的所有敌人无法移动，持续[duration]秒。此效果不会被净化技能净化。</description></skill>
				<skill><duration>7000</duration><target>me,range,enemy</target><changeText>持续时间：[duration]秒</changeText><description>向大地释放强大的电流，使地图上的所有敌人无法移动，持续[duration]秒。此效果不会被净化技能净化。</description></skill>
				<skill><duration>8000</duration><target>me,range,enemy</target><changeText>持续时间：[duration]秒</changeText><description>向大地释放强大的电流，使地图上的所有敌人无法移动，持续[duration]秒。此效果不会被净化技能净化。</description></skill>
			</growth>
		</skill>
		
		<!-- 全域电击-额外技能 -->
					<skill name="全域电击-清除王者之剑buff"><!-- 生存-主动 -->
						<name>electricDevicerClearKing</name>
						<cnName>全域电击-清除王者之剑buff</cnName>
						<!--英雄技能属性------------------------------------------------------------ -->
						<cd>1</cd>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>active</conditionType>
						<target>me,range,all</target>
						<!--效果------------------------------------------------------------ -->
						<range>99999</range>
						<addType>instant</addType>
						<effectType>clearStateByBaseLabelArr</effectType>
						<valueString>KingRabbitKingHit</valueString>
					</skill>
		<!-- 闪电塔技能 -->
					<!-- 逐渐死亡 -->
					<skill index="0"cnName="逐渐死亡"><!-- 生存 -->
						<name>lightningTower_die</name>
						<cnName>逐渐死亡</cnName>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>add</condition>
						<target>me</target>
						<!--效果------------------------------------------------------------ -->
						<addType>state</addType>
						<effectType>lifeRate</effectType>
						<mul>-0.05</mul>
						<duration>999999</duration>
						<!--图像------------------------------------------------------------ --> 
						<description>每秒损失[mul]的生命值。</description>
					</skill>
					<!-- 定时发射闪电 -->
					<skill index="0" cnName="闪电"><!-- dps -->
						<name>lightningTower_lightning</name>
						<cnName>闪电</cnName>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>interval</condition>
						<target>me,near,enemy</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instant</addType>
						<effectType>lightningTower_lightning</effectType>
						<extraValueType>producterDpsFactor</extraValueType>
						<value>0</value>
						<mul>1</mul>
						<duration>0.5</duration>
						<range>400</range>
						<!--图像------------------------------------------------------------ -->
						<targetEffectImg con="add" randomRange="10" soundUrl="sound/electric">bulletHitEffect/spark_motion2</targetEffectImg>
						<stateEffectImg partType="2foot">skillEffect/disabled_enemy</stateEffectImg>
						<description>闪电塔每隔[duration]秒攻击[range]范围内的敌人。</description>
					</skill>
		
		
		<skill index="0" cnName="穿梭"><!-- dps -->
			<name>shuttleDevicer</name>
			<cnName>穿梭</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0</cd>
			<changeText>持续时间：[duration]秒{n}技能冷却时间：[cd]秒</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>400</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>shuttleDevicer</effectType>
			<valueString></valueString>
			<mul>3</mul>
			<duration>0.5</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/teleport_enemy" con="add">lightEffect/basinShow</meEffectImg>
			<stateEffectImg con="add">skillEffect/energyShield</stateEffectImg>
			<stateEffectImg2 partType="shootPoint" con="add" raNum="30" followPartRaB="1">skillEffect/through_hero</stateEffectImg2>
			<description>通过镭射器，穿梭到鼠标所在的位置并且无敌，镭元素使得武器能够无视地形攻击，并且提高[mul-1]的攻击力，持续[duration]秒（修罗模式不超过3秒）。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><cd>0</cd><duration>520</duration></skill>
				<skill><cd>0</cd><duration>520</duration></skill>
				<skill><cd>0</cd><duration>520</duration></skill>
				<skill><cd>0</cd><duration>520</duration></skill>
				<skill><cd>0</cd><duration>520</duration></skill>
				<skill><cd>0</cd><duration>520</duration></skill>
				<skill><cd>0</cd><duration>600</duration></skill>
				<skill><cd>0</cd><duration>700</duration></skill>
			</growth>
		</skill>
		
		<skill cnName="黑洞"><!-- dps -->
			<name>blackHoleDevicer</name>
			<cnName>黑洞</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0</cd>
			<changeText>技能冷却时间：[cd]秒{n}作用范围：[range]码{n}最多伤害次数：[value]</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<doCondition>haveEnemyClearBulletRange</doCondition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>blackHoleDevicer</effectType>
			<extraValueType>nowArmsTrueDps</extraValueType>
			<value>1</value>
			<range>300</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/invisibility_hero" partType="body">generalEffect/blackHoleHide</meEffectImg>
			<description>清空[range]码内所有敌人子弹，每清除1个子弹就对释放子弹的敌人造成伤害，伤害等于角色当前武器战斗力，释放1次技能最多伤害[value]次。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><cd>50</cd><value>1000</value><range>3000</range></skill>
				<skill><cd>46</cd><value>3000</value><range>4500</range></skill>
				<skill><cd>42</cd><value>5000</value><range>5500</range></skill>
				<skill><cd>38</cd><value>7000</value><range>6500</range></skill>
				<skill><cd>30</cd><value>1000</value><range>7500</range></skill>
				<skill><cd>30</cd><value>2000</value><range>15000</range></skill>
				<skill><cd>25</cd><value>2005</value><range>15000</range></skill>
				<skill><cd>20</cd><value>3000</value><range>15000</range></skill>
			</growth>
		</skill>
		
		
		
		
		
		<skill index="0" cnName="异龙蛋"><!-- dps -->
			<name>dinosaurEgg</name>
			<cnName>异龙蛋</cnName>
			<summonedUnits>1</summonedUnits>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0</cd>
			<changeText>技能冷却时间：[cd]秒</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>400</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<mul>3</mul>
			<obj>"cnName":"无敌异龙蛋","num":1,"lifeMul":1000,"cx":-50,"cy":-50,"maxNum":10,"noUnderHurtB":1,"noAiFindB":1,"skillArr":["magneticField_egg","trueshot_eeg"]</obj>
			<duration>10</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/teleport_enemy" con="add"></meEffectImg>
			<description>产生一个无敌的异龙蛋，在异龙蛋300码范围内，获得10%攻击力加成，异龙蛋具有磁力场效果，并且敌人子弹无法穿透异龙蛋。异龙蛋持续存在[duration]秒。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><cd>0</cd></skill>
				<skill><cd>0</cd></skill>
				<skill><cd>0</cd></skill>
			</growth>
		</skill>
		
		<skill index="0" cnName="爆竹"><!-- dps -->
			<name>squibDevice</name>
			<cnName>爆竹</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0</cd>
			<changeText>技能冷却时间：[cd]秒</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>400</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<extraValueType>nowTrueArmsDps</extraValueType>
			<obj>"name":"squibDevice","site":"me"</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/teleport_enemy"></meEffectImg>
			<description>在单位脚下释放1个爆竹，爆竹碰到年兽后爆炸并对其造成惊吓，使它逃跑并且防御力降低70%。惊吓效果只对年兽有效。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><cd>0</cd></skill>
			</growth>
		</skill>
		
		<skill index="0" cnName="恐怖盒子"><!-- dps -->
			<name>terroristBox</name>
			<cnName>恐怖盒子</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0</cd><continueNum>2</continueNum>
			<changeText>技能冷却时间：[cd]秒</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>400</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<extraValueType>nowTrueArmsDps</extraValueType>
			<obj>"name":"terroristBox","site":"mouse","minYB":true</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/teleport_enemy"></meEffectImg>
			<description>能吓哭僵尸的神奇盒子，释放后一定范围内的敌方单位恐惧8秒，并能同时释放2次。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><cd>0</cd></skill>
			</growth>
		</skill>
		
		<skill index="0" cnName="无疆之章"><!-- dps -->
			<name>knightsMedal</name>
			<cnName>无疆骑士</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0</cd>
			<changeText>生命值倍数：[obj.lifeMul]</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>400</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<summonedUnitsB>3</summonedUnitsB>
			<effectType>summonedKnights</effectType>
			<mul>1</mul>
			<duration>1000</duration>
			<!-- 子弹所需 -->
			<obj>"cnName":"无疆骑士","num":100,"lifeMul":100,"dpsMul":1,"mulByFatherB":true,"maxNum":1,"lifeTime":3000,"skillArr":["crazy_knights","boundless_enemy"]</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/teleport_enemy"></meEffectImg>
			<description>召唤出无疆骑士为你战斗，持续[duration]秒！无疆骑士生命倍数:[obj.lifeMul]、战斗力倍数:[obj.dpsMul]。修罗模式下，持续时间减半。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill></skill>
				<skill><obj>"cnName":"无疆骑士","num":100,"lifeMul":350,"dpsMul":1,"mulByFatherB":true,"maxNum":1,"lifeTime":3000,"skillArr":["crazy_knights","boundless_enemy"]</obj></skill>
				<skill>
					<obj>"cnName":"无疆骑士","num":100,"lifeMul":500,"dpsMul":2,"mulByFatherB":true,"maxNum":1,"lifeTime":3000,"skillArr":["crazy_knights","boundless_enemy"]</obj>
					<changeText>生命值倍数：[obj.lifeMul]{n}战斗力倍数：[obj.dpsMul]</changeText>
				</skill>
				<skill>
					<obj>"cnName":"无疆骑士","num":100,"lifeMul":700,"dpsMul":3,"mulByFatherB":true,"maxNum":1,"lifeTime":3000,"skillArr":["crazy_knights","boundless_enemy"]</obj>
					<changeText>生命值倍数：[obj.lifeMul]{n}战斗力倍数：[obj.dpsMul]</changeText>
				</skill>
			</growth>
		</skill>
		
		<skill index="0" cnName="暴君之骷"><!-- dps -->
			<name>skeletonMedal</name>
			<cnName>召唤暴君</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0</cd>
			<changeText>持续时间：[duration]</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>400</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<summonedUnitsB>3</summonedUnitsB>
			<effectType>summonedKnights</effectType>
			<mul>1</mul>
			<duration>1000</duration>
			<!-- 子弹所需 -->
			<obj>"cnName":"暴君","num":100,"lifeMul":50,"dpsMul":3,"mulByFatherB":true,"maxNum":1,"skillArr":["State_Invincible","knife_skeleton2","crazy_skeleton","teleport_skeleton","atry_skeleton"]</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/teleport_enemy"></meEffectImg>
			<description>召唤出无敌暴君为你战斗，持续[duration]秒！</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><duration>1500</duration></skill>
				<skill><duration>2000</duration></skill>
				<skill><duration>2500</duration></skill>
				<skill><duration>3000</duration></skill>
			</growth>
		</skill>
		
		<skill name="skeletonWand"><!-- 限制 -->
			<name>skeletonWand</name>
			<cnName>骷髅权杖</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>0</cd>
			<changeText>技能冷却时间：[cd]</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>600</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<!-- 子弹所需 -->
			<obj>"name":"skeletonWandBullet","site":"me","flipB":true</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/lifeReplace_enemy"></meEffectImg>
			<description>发出亡命之光，对目标造成其当前生命值10%的伤害，无视技能免疫。99级主线任务效果消失。</description>
			<growth>
				<skill><cd>0</cd></skill>
				<skill><cd>0</cd></skill>
			</growth>
		</skill>
					<skill name="skeletonWandHit"><!-- 限制 -->
						<name>skeletonWandHit</name>
						<cnName>骷髅权杖-击中</cnName>
						<noSkillDodgeB>1</noSkillDodgeB>
						<ignoreImmunityB>1</ignoreImmunityB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>hit</condition>
						<otherConditionArr>noHardB</otherConditionArr>
						<target>target</target>
						<!--效果------------------------------------------------------------ -->
						<changeHurtB>1</changeHurtB>
						
						<addType>instant</addType>
						<effectType>skeletonWandHit</effectType>
						<mul>1</mul>
					</skill>
		
		
		<skill>
			<name>hookDevice</name>
			<cnName>变身</cnName>
			<cd>0</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess30</otherConditionArr>
			<target>me</target>
			
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<summonedUnitsB>1</summonedUnitsB>
			<effectType>hookDevice</effectType>
			<extraValueType>heroMaxArmsDps</extraValueType>
			<duration>10</duration>
			<!-- 子弹所需 -->
			<obj>"cnName":"斩之使者","num":1,"lifeMul":100,"dpsMul":30,"mulByFatherB":1,"maxNum":1,"skillArr":["State_SpellImmunity","alloyShell_10","HookWitchHook","HookWitchRoll","HookWitchShake","mulHurtToValue","State_AddMove50"]</obj>
			<description>变身斩之使者，斩之使者的基础伤害为角色最高武器战斗力的[obj.dpsMul]倍，持续[duration]秒。双击载具键可退出。</description>
			<growth>
				<skill><duration>5200</duration></skill>
			</growth>
		</skill>			
		
		
		<skill>
			<name>phantomDevice</name>
			<cnName>集体照</cnName><noInClonedB>1</noInClonedB>
			<cd>120</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target noMeB="1">me,range,we</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>phantomDevice</effectType>
			<value>1</value><!-- 分身个数 -->
			<mul>1</mul><!-- 攻击力倍数 -->
			<secMul>1</secMul><!-- 血量倍数 -->
			<duration>30</duration>
			<range>9999</range>
			<!--图像------------------------------------------------------------ -->
			<description>为[range]码范围内的我方单位（不包括角色、尸宠、载具、魂卡、分身）产生[value]个分身，持续[duration]秒。</description>
			<growth>
				<skill><duration>30</duration></skill>
			</growth>
		</skill>			
		
	</father>
	
	
	<father name="deviceSkill_link" cnName="装置技能-link">
		
		<skill index="0" name="terroristBox_screaming" cnName="恐怖盒子-惊吓"><!-- 生存-主动 -->
			<name>terroristBox_screaming</name>
			<cnName>惊吓</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target limitNum="10">target,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>screaming_hero</effectType>
			<mul>0.4</mul>
			<duration>8</duration>
			<range>500</range>
			<!--图像------------------------------------------------------------ --> 
			<meEffectImg soundUrl="sound/magicHit1"></meEffectImg>
			<stateEffectImg partType="mouth" con="add">skillEffect/screaming_hero_target</stateEffectImg>
		</skill>
		<skill index="0" name="squibDevice_screaming" cnName="爆竹-惊吓"><!-- 生存-主动 -->
			<name>squibDevice_screaming</name>
			<cnName>惊吓</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>tergetName</otherConditionArr>
			<conditionString>NianMonster</conditionString>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>squibDevice_screaming</effectType>
			<mul>0.7</mul>
			<duration>5</duration>
			<!--图像------------------------------------------------------------ --> 
			<meEffectImg soundUrl="sound/magicHit1"></meEffectImg>
			<stateEffectImg partType="mouth" con="filter">skillEffect/screaming_hero_target</stateEffectImg>
			<description></description>
		</skill>
		
		
		
	</father>
</data>