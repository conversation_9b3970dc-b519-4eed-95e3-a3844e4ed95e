<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="normal">
		<gather name="寒光市">
			
			<level name="HanGuang5_city">
				<!-- 发兵集************************************************ -->
				<info enemyLv="95"/>
				<fixed target="HanGuang5_1" info="no" drop="all" unitG="no" rectG="all" eventG="no"/>
				<!-- 基本属性 -->
				<sceneLabel>HanGuang5</sceneLabel>
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="会计尸" num="5"/>
						<unit cnName="制图尸" num="5"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>number</numberType>
						<unit cnName="水管僵尸" num="6"/>
						<unit cnName="制图尸" num="5"/>
						
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="水管僵尸" num="8"/>
						<unit cnName="会计尸" num="3"/>
						<unit cnName="制图尸" num="5"/>
					</unitOrder>
					<unitOrder id="enemy5">
						<numberType>number</numberType>
						<unit cnName="战争狂人" unitType="boss" dieGotoState="stru" dropLabel="no" />
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="狂人机器" unitType="boss" lifeMul="2" dpsMul="2"/>
					</unitOrder>
					
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy5; r1</order><order>createUnit:enemy5; r2</order><order>createUnit:enemy5; r3</order></event> 
						
						<event id="e_win">
							<condition delay="0.5">bodyEvent:die; 战争狂人</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_11">
							<condition delay="0.2" >say:listOver; s1</condition>
							<order>createUnit:enemy4; r1</order>
						</event>
						<event id="e_win2">
							<condition delay="0.1">bodyEvent:die; 狂人机器</condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e2_11">
							<condition delay="0.1">say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>worldMap:levelName; HanGuang5:HanGuang5_1</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
			
			<level name="HanGuang5_1">
				<!-- 发兵集************************************************ -->
				<info enemyLv="95" diff="2" />
				<sceneLabel>HanGuang5</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="会计尸" num="5"/>
						<unit cnName="制图尸" num="5"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>number</numberType>
						<unit cnName="水管僵尸" num="6"/>
						<unit cnName="制图尸" num="5"/>
						
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="水管僵尸" num="8"/>
						<unit cnName="会计尸" num="3"/>
						<unit cnName="制图尸" num="5"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="狂人机器" unitType="boss" lifeMul="2" dpsMul="2"/>
					</unitOrder>
					
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
	</father>
</data>