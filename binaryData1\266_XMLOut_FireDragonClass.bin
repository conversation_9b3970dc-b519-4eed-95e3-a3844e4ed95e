<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="enemy">
		<body index="0" name="异火龙">
			
			<name>FireDragon</name>
			<cnName>异火龙</cnName><headIconUrl>IconGather/FireDragon</headIconUrl>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/FireDragon293.swf</swfUrl>
			<rosRatio>2</rosRatio>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<flipCtrlBy>target</flipCtrlBy>
			<showLevel>999</showLevel>
			<preBulletArr>FireDragon_combo,FireDragon_comboFire</preBulletArr>
			<imgArr>
				stand,move,hurt1,die1
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,shootAttack
				,thorn<PERSON>ttack
				,sprintAttack
				,fireAttack
				,comboAttack
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-27,-23,50,54</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>10</maxVx>
			<motionState>fly</motionState><flyUseSpiderB>1</flyUseSpiderB>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>FireDragon_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr>paralysis_enemy,FireDragonFire,FireDragonSprint,FireDragonCombo,cmldef2_enemy,fightReduct,defenceBounce_enemy</bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<uiSkillArr></uiSkillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>thornAttack</imgLabel><cn>尾刺</cn>
					<mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>2</hurtRatio>
					<attackType>direct</attackType>
					<skillArr></skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl soundUrl="sound/body_hit">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				
				<hurt>
					<imgLabel>shootAttack</imgLabel><cn>喷火球</cn>
					<bulletLabel>FireDragon_fireball</bulletLabel>
					<grapRect>-400,-50,300,200</grapRect>
					<hurtRatio>1.2</hurtRatio>
					
				</hurt>
				
				<hurt info="不加入ai选择">
					<imgLabel>sprintAttack</imgLabel><mustGrapRectB>1</mustGrapRectB><noAiChooseB>1</noAiChooseB>
					<hurtMul>0.1</hurtMul>
					<grapRect>-350,-17,270,97</grapRect>
					<hurtRatio>0.5</hurtRatio>
					<attackType>holy</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl soundUrl="sound/body_hit">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				
				<hurt info="不加入ai选择">
					<imgLabel>fireAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtMul>0.05</hurtMul>
					<bulletLabel>FireDragon_fireSurround</bulletLabel>
					<grapRect>-400,-50,300,200</grapRect>
					<attackType>holy</attackType>
				</hurt>
				
				<hurt info="不加入ai选择">
					<imgLabel>comboAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0</hurtRatio>
					<hurtMul>0.5</hurtMul>
					<noShootB>1</noShootB>
					<grapRect>-400,-50,300,200</grapRect>
					<attackType>holy</attackType>
				</hurt>
			</hurtArr>
		</body>
		
		
		<bullet cnName="异祖龙-火球">
			<name>FireDragon_fireball</name>
			<cnName>异角龙-火球</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>175</bulletAngle>
			<bulletAngleRange>20</bulletAngleRange>
			<bulletLife>4</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.6</attackGap>
			<attackDelay>0.3</attackDelay>
			<bulletNum>3</bulletNum>
			<shootAngle>25</shootAngle>
			<shootPoint>-97,-6</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>10</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" con="filter">FireDragon/fireball</bulletImgUrl>
			<hitImgUrl name="redFire_hitFloor"></hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		
		
		<bullet cnName="异祖龙-龙焰">
			<name>FireDragon_fireSurround</name>
			<cnName>异祖龙-龙焰</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.3</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>180</bulletAngle>
			<shakeAngle>2</shakeAngle>
			<bulletLife>0.62</bulletLife>
			<shootNum>20</shootNum>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<shootPoint>-85,-10</shootPoint>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.4</attackGap>
			<attackDelay>0.8</attackDelay>
			<!--运动属性------------------------------------------------------------ -->	
			<speedD random="0.1" max="30" />
			<bulletSpeed>30</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<shootSoundUrl></shootSoundUrl>
			<bulletImgUrl name="fireFlamer_bullet"/>
			<hitImgUrl raNum="30">bulletHitEffect/smoke_motion</hitImgUrl><!-- 子弹图像【必备】 -->
			<hitFloorImgUrl raNum="30">bulletHitEffect/smoke_motion</hitFloorImgUrl><!-- 子弹图像【必备】 -->
			<fireImgType>no</fireImgType>
		</bullet>
		
		<bullet cnName="异祖龙-连续火球">
			<name>FireDragon_combo</name>
			<cnName>异祖龙-连续火球</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.5</hurtMul><transBackMul>0.4</transBackMul>
			<bulletSkillArr></bulletSkillArr>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>-120</bulletAngle>
			<shakeAngle>30</shakeAngle>
			
			<bulletLife>6</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>1</bulletNum>
			<shootAngle>15</shootAngle>
			<shootPoint>-62,-71</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>10</bulletSpeed>
			<followD value="1" maxTime="1"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" con="add">FireDragon/fireball</bulletImgUrl>
			<hitImgUrl name="redFire_hitFloor"></hitImgUrl>
		</bullet>
		<bullet cnName="异祖龙-连续火球-爆开火焰">
			<name>FireDragon_comboFire</name>
			<cnName>异祖龙-连续火球-爆开火焰</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.1</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>0</bulletAngle>
			<bulletLife>0.62</bulletLife>
			<shootNum>1</shootNum>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--运动属性------------------------------------------------------------ -->	
			<speedD random="0.1" max="30" />
			<bulletSpeed>30</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<shootSoundUrl></shootSoundUrl>
			<bulletImgUrl name="fireFlamer_bullet"/>
			<selfBoomImgUrl raNum="30">bulletHitEffect/smoke_motion</selfBoomImgUrl><!-- 子弹图像【必备】 -->
			<fireImgType>no</fireImgType>
		</bullet>
	</father>	
	
	
	<father name="enemy">
		<skill name="龙破"><!-- dps -->
			<name>FireDragonSprint</name>
			<cnName>龙破</cnName><iconUrl36>SkillIcon/laser_PetFightWolf_36</iconUrl36>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>12</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>700</conditionRange><target>me</target>
			<addType>no</addType>
			<meActionLabel>sprintAttack</meActionLabel>
			<description>向目标连续冲刺3次，造成巨大伤害。</description>
		</skill>
		
		<skill name="龙焰"><!-- dps -->
			<name>FireDragonFire</name>
			<cnName>龙焰</cnName><iconUrl36>SkillIcon/FireDragonFire_36</iconUrl36>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>8</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>700</conditionRange><target>me</target>
			<addType>no</addType>
			
			<meActionLabel>fireAttack</meActionLabel>
			<description>瞬移至目标前方，释放高温火焰，灼伤敌人！</description>
		</skill>
		
		<skill name="龙雨"><!-- dps -->
			<name>FireDragonCombo</name>
			<cnName>龙雨</cnName><iconUrl36>SkillIcon/FireDragonCombo_36</iconUrl36>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>15</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.3</conditionRange>
			<target>me</target>
			<addType>no</addType>
			
			<meActionLabel>comboAttack</meActionLabel>
			<description>当生命值低于30%时，向空中释放大量的限时跟踪火球，此时异飞龙无敌。</description>
		</skill>
	</father>
	
</data>