<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="exploitCards" cnName="军队商店" priceType="exploitCards" labelArr="all" type="other">
		
		<goods dataType="fashion" inBagType="equip" defineLabel="dragonHead" price="17" 			limitOneB="1" />
		<goods defineLabel="workerHead" price="2" 			buyLimitNum="30" />
		<goods defineLabel="arenaChest" price="2" 			dayBuyLimitNum="2" />
		<goods defineLabel="zodiacCash" price="2" 			dayBuyLimitNum="2" />
		<goods defineLabel="PetBoomSkullBook" num="2" price="1" 			dayBuyLimitNum="20" />
		<goods defineLabel="PetIronChiefBook" num="2" price="1" 			dayBuyLimitNum="20" />
		<goods defineLabel="PetLakeBook" num="2" price="1" 			dayBuyLimitNum="20" />
		
		<goods defineLabel="alertGem" num="5" price="1" 			dayBuyLimitNum="40" />
		<goods defineLabel="wisdomGem" num="5" price="1" 		dayBuyLimitNum="40"/>
		<goods defineLabel="agileGem" num="5" price="1" 				dayBuyLimitNum="40"/>
		<goods defineLabel="defenceGem" num="5" price="1" 		dayBuyLimitNum="40"/>
		<goods defineLabel="fireGem" num="5" price="1" 				dayBuyLimitNum="40"/>
		<goods defineLabel="electricGem" num="5" price="1" 			dayBuyLimitNum="40"/>
		<goods defineLabel="frozenGem" num="5" price="1" 			dayBuyLimitNum="40"/>
		<goods defineLabel="poisonGem" num="5" price="1" 			dayBuyLimitNum="40"/>
		
		<goods cnName="重生石" name="rebirthStone_exploit" defineLabel="rebirthStone" price="2" chooseNumB="1" dayBuyLimitNum="4"><priceType>exploitCards</priceType></goods>
		<goods cnName="技能刷新卡" name="skillFleshCard_exploit" defineLabel="skillFleshCard" price="2" chooseNumB="1" dayBuyLimitNum="4"><priceType>exploitCards</priceType></goods>
		<goods cnName="队友重生卡" name="teamRebirthCard_exploit" defineLabel="teamRebirthCard" price="2" chooseNumB="1" dayBuyLimitNum="2"><priceType>exploitCards</priceType></goods>
		
		<goods cnName="神谕头盔碎片" name="oracleSuit_head_exploit" defineLabel="oracleSuit_head" price="1" chooseNumB="1" dayBuyLimitNum="30"><priceType>exploitCards</priceType></goods>
		<goods cnName="中型银币堆" name="coinHeap_2_exploit" defineLabel="coinHeap_2" price="1" chooseNumB="1"><priceType>exploitCards</priceType></goods>
		<goods cnName="大型银币堆" name="coinHeap_3_exploit" defineLabel="coinHeap_3" price="5" chooseNumB="1"><priceType>exploitCards</priceType></goods>
		<goods cnName="神武碎片" name="allBlackCash_exploit" defineLabel="allBlackCash" price="1" chooseNumB="1" dayBuyLimitNum="40"><priceType>exploitCards</priceType></goods>
		<goods cnName="神护碎片" name="allBlackEquipCash_exploit" defineLabel="allBlackEquipCash" price="1" chooseNumB="1" dayBuyLimitNum="40"><priceType>exploitCards</priceType></goods>
		
		<goods cnName="终级装备守护卡" name="ultiEquipEchelonCard_exploit" defineLabel="ultiEquipEchelonCard" price="7" chooseNumB="1" dayBuyLimitNum="10"><priceType>exploitCards</priceType></goods>
		<goods cnName="终级武器守护卡" name="ultiArmsEchelonCard_exploit" defineLabel="ultiArmsEchelonCard" price="7" chooseNumB="1" dayBuyLimitNum="10"><priceType>exploitCards</priceType></goods>
		<![CDATA[
		<goods cnName="75级零件箱" name="partsChest75_union" defineLabel="partsChest75" price="160" chooseNumB="1"><priceType>exploitCards</priceType></goods>
		<goods cnName="69级零件箱" name="partsChest69_union" defineLabel="partsChest69" price="40" chooseNumB="1"><priceType>exploitCards</priceType></goods>
		<goods cnName="远古宝箱" name="dragonChest_union" defineLabel="dragonChest" chooseNumB="1" 											price="10"><priceType>exploitCards</priceType></goods>
		<goods cnName="稀有宝箱" name="normalChest_union" defineLabel="normalChest" chooseNumB="1" 											price="16"><priceType>exploitCards</priceType></goods>
		]]>
		
		
		<goods cnName="镭晶" name="armsRadium_union" defineLabel="armsRadium" price="1" chooseNumB="1" num="3" dayBuyLimitNum="20"><priceType>exploitCards</priceType></goods>
		<goods cnName="钛晶" name="armsTitanium_union" defineLabel="armsTitanium" price="1" chooseNumB="1" num="3" dayBuyLimitNum="20"><priceType>exploitCards</priceType></goods>
		<goods cnName="武器守护卡" name="armsEchelonCard_union" defineLabel="armsEchelonCard" price="1" num="4" chooseNumB="1"><priceType>exploitCards</priceType></goods>
		<goods cnName="装备守护卡" name="equipEchelonCard_union" defineLabel="equipEchelonCard" price="1" num="4" chooseNumB="1"><priceType>exploitCards</priceType></goods>
		<goods cnName="高级武器守护卡" name="highArmsEchelonCard_union" defineLabel="highArmsEchelonCard" price="2" chooseNumB="1" dayBuyLimitNum="30"><priceType>exploitCards</priceType></goods>
		<goods cnName="高级装备守护卡" name="highEquipEchelonCard_union" defineLabel="highEquipEchelonCard" price="2" chooseNumB="1" dayBuyLimitNum="30"><priceType>exploitCards</priceType></goods>
		
		
		
		<goods cnName="军备物资" name="militarySupplies_union" defineLabel="militarySupplies" chooseNumB="1" dayBuyLimitNum="6" 		price="1" num="8"><priceType>exploitCards</priceType></goods>
		
		
		<goods dataType="gene" inBagType="gene" cnName="铁魁基因体" name="PetIronChief_union" defineLabel="PetIronChief" 		price="148"><priceType>exploitCards</priceType></goods>
		<goods dataType="gene" inBagType="gene" cnName="爆骷基因体" name="PetBoomSkull_union" defineLabel="PetBoomSkull" 		price="148"><priceType>exploitCards</priceType></goods>
		<goods dataType="fashion" inBagType="equip" cnName="红魔时装" name="massEffect_union" defineLabel="massEffect" 			price="160"><priceType>exploitCards</priceType></goods>
		<goods dataType="arms" cnName="赤龙" name="shotgunRed_union" defineLabel="shotgunRed" 													price="80"><priceType>exploitCards</priceType></goods>
		<goods dataType="arms" cnName="朱雀" name="pistolRed_union" defineLabel="pistolRed" 															price="80"><priceType>exploitCards</priceType></goods>
		<goods dataType="arms" cnName="猎鹰" name="sniperBlue_union" defineLabel="sniperBlue" 														price="74"><priceType>exploitCards</priceType></goods>
		<goods dataType="arms" cnName="沙暴" name="rifleYellow_union" defineLabel="rifleYellow" 														price="72"><priceType>exploitCards</priceType></goods>
	</father>
</data>