<?xml version="1.0" encoding="utf-8" ?>
<data>
	
	<father name="wilder">
		
		
		<body name="电棍僵尸">
			
			<name>PoliceZombie</name>
			<cnName>电棍僵尸</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/PoliceZombie213.swf</swfUrl>
			<!-- 基本系数 -->
			<rosRatio>1</rosRatio>
			<lifeRatio>1.8</lifeRatio>
			<showLevel>999</showLevel>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<imgArr>
				stand,move,run
				,normalAttack,shootAttack,electricAttack,chargeAttack,shakeAttack
				,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<lifeBarExtraHeight>-40</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>12</maxVx>
			<runStartVx>8</runStartVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel></extraAIClassLabel>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr>PoliceZombieCharge,PoliceZombieElectric,PoliceZombieShoot,PoliceZombieShake,fightReduct2,defenceBounce_enemy</bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/hand_hit">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt cd="3">
					<imgLabel>shootAttack</imgLabel>
					<bulletLabel>PoliceZombieBullet</bulletLabel>
					<grapRect>-650,-171,600,305</grapRect>
					<hurtRatio>5</hurtRatio>
					<attackType>holy</attackType>
				</hurt>
				<hurt info="电击">
					<imgLabel>electricAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0</hurtRatio>
					<hurtMul>0.15</hurtMul>
					<attackType>holy</attackType>
					<shakeValue>4</shakeValue>
					<skillArr>PoliceZombieElectricHit</skillArr>
					<hitImgUrl con="add" soundUrl="sound/hand_hit">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt info="爬行电流">
					<imgLabel>shakeAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>PoliceZombieShake</bulletLabel>
					<grapRect>-550,-151,500,205</grapRect>
					<hurtRatio>0</hurtRatio>
					<hurtMul>0.2</hurtMul>
					<attackType>holy</attackType>
					
				</hurt>
			</hurtArr>
		</body>	
	</father>
	<father name="enemy">	
		<bullet>
			<name>PoliceZombieBullet</name>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<attackType>holy</attackType>
			<attackGap>0.7</attackGap>
			<attackDelay>0.4</attackDelay>
			<!--随机属性------------------------------------------------------------ -->
			<bulletAngle>185</bulletAngle>
			<bulletAngleRange>60</bulletAngleRange>
			<bulletWidth>20</bulletWidth>
			<bulletLife>10</bulletLife>
			<hitType>rect</hitType>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-90,-153</shootPoint>
			<bulletSpeed>15</bulletSpeed>
			<speedD random="0.3"  selfVra="-1"/>
			<!--特殊------------------------------------------------------------ -->
			<gravity>0.4</gravity>
			<boomD  bodyB="1" selfB="1" radius="200" noExcludeBodyB="1" />
			<bounceD glueFloorB="1" />	<!-- 反弹 -->
			<skillArr>PoliceZombieShakeHit</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">PoliceZombie/bullet</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2">boomEffect/boom3</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl>
			<hitFloorImgUrl>no</hitFloorImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet>
			<name>PoliceZombieShake</name>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.3</hurtMul>
			<attackType>holy</attackType>
			<shootNum>2</shootNum>
			<bulletNum>2</bulletNum>
			<shootAngle>90</shootAngle>
			<attackGap>1.7</attackGap>
			<attackDelay>0.59</attackDelay>
			<shootGap>0.9</shootGap>
			<!--随机属性------------------------------------------------------------ -->
			<bulletAngle>90</bulletAngle>
			<bulletWidth>40</bulletWidth>
			<bulletLife>10</bulletLife>
			<hitType>rect</hitType>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-83,-5</shootPoint>
			<bulletSpeed>14</bulletSpeed>
			<!--特殊------------------------------------------------------------ -->
			<skillArr>PoliceZombieShakeHit</skillArr>
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1" con="add">PoliceZombie/electricLine</bulletImgUrl>
			<hitImgUrl soundUrl="sound/paralysis_enemy_hit">bulletHitEffect/fitHit</hitImgUrl><!-- 子弹图像【必备】 -->
			<hitFloorImgUrl>no</hitFloorImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		<![CDATA[【其他技能】===============================================]]>
		<skill>
			<name>PoliceZombieShoot</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>粘性手雷</cnName>
			<cd>99999999</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>0.1</duration>
			<description>向目标投掷手雷，碰撞目标或者经过几秒会爆炸，对周围敌人产生巨大伤害，并封锁目标所有主动技能和被动技能。</description>
		</skill>
		<skill>
			<name>PoliceZombieCharge</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>充能</cnName><noBeClearB>1</noBeClearB>
			<cd>12</cd><delay>0.5</delay>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.5</conditionRange>
			<target>me</target>
			<addType>state</addType>
			<effectType>PoliceZombieCharge</effectType>
			<value>1.6</value><!-- 移动速度增加 -->
			<mul>0.6</mul><!-- cd减少 -->
			<duration>6</duration>
			<stateEffectImg partType="2eye" con="filter" raNum="30">skillEffect/smallFire</stateEffectImg>
			<meActionLabel>chargeAttack</meActionLabel>
			<description>生命值小于50%时，电棍僵尸对自己进行充能，增加移动速度，减少技能冷却时间。</description>
		</skill>
		<![CDATA[【电击】===============================================]]>
		<skill>
			<name>PoliceZombieElectric</name>
			<cnName>电击</cnName>
			<cd>6</cd>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>teleportToAttackBody</effectType>
			<value>0.3</value>
			<mul>0</mul>
			<valueString>130,0</valueString>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>electricAttack</meActionLabel>
			<description>闪烁至敌人面前发动电击，有几率麻痹目标，并造成巨大伤害，无视闪避。</description>
		</skill>
				<skill>
					<name>PoliceZombieElectricHit</name>
					<cnName>电击麻痹</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
					<!--触发条件与目标------------------------------------------------------------ -->
					<conditionType>passive</conditionType>
					<condition>hit</condition>
					<target>target</target>
					<!--效果------------------------------------------------------------ -->
					<addType>state</addType>
					<effectType>moveSpeed</effectType>
					<effectProArr>0.5</effectProArr>
					<mul>0</mul>
					<duration>5</duration>
					<!--图像------------------------------------------------------------ --> 
					
					<stateEffectImg con="add" soundUrl="sound/paralysis_enemy_hit">skillEffect/paralysis_enemy</stateEffectImg>
					<description>有[effectProArr.0]的几率使其麻痹，持续5秒。</description>
				</skill>
				
		<![CDATA[【爬行电流】===============================================]]>		
		<skill>
			<name>PoliceZombieShake</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>爬行电流</cnName>
			<cd>7</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>800</conditionRange>
			
			<target>me</target>
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>1.6</duration>
			<meActionLabel>shakeAttack</meActionLabel>
			<description>将电棍插入地面，产生爬行电流，被击中敌人将被清除有益效果，并封锁所有主动技能和被动技能。</description>
		</skill>		
				<skill>
					<name>PoliceZombieShakeHit</name>
					<cnName>封锁被动技能</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
					<!--触发条件与目标------------------------------------------------------------ -->
					<conditionType>passive</conditionType>
					<condition>hit</condition>
					<otherConditionArr>targetArmsNoMeltFlamerPurgold</otherConditionArr>
					<target>target</target>
					<!--效果------------------------------------------------------------ -->
					<addType>instantAndState</addType>
					<effectType>noAllSkill</effectType>
					<duration>6</duration>
					<!--图像------------------------------------------------------------ --> 
					<stateEffectImg partType="mouth" con="add">skillEffect/silenceMore</stateEffectImg>
					<description>。</description>
				</skill>
	</father>
	
</data>