<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="enemy" cnName="怪物技能">
		<skill index="0" name="狂战尸-震地减速">
			<name>FightKing_slowMove</name>
			<cnName>狂战尸-震地减速</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<value>0</value>
			<mul>0.5</mul>
			<duration>2</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="2foot">skillEffect/disabled_enemy</stateEffectImg>
			<description>击中目标后降低其50%的移动速度，持续2秒。</description>
		</skill>
		<skill index="0" name="狂战尸-分身">
			<name>FightKing_cloned</name>
			<cnName>分身</cnName><iconUrl36>SkillIcon/cloned_36</iconUrl36>
			<cd>30</cd>
			<showInLifeBarB>1</showInLifeBarB><noInClonedB>1</noInClonedB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>700</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>cloned</effectType>
			<value>1</value>
			<mul>0.25</mul>
			<secMul>0.75</secMul>
			<duration>30</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/cloned_enemy" con="add">skillEffect/hiding_hero</meEffectImg>
			<description>产生[value]个分身，攻击力与自身同等，血量为自身的[mul]，持续[duration]秒。</description>
		</skill>
		
		
		
		<![CDATA[副本技能]]>
		<skill index="0" name="knifeBoom_FightKing" cnName="狂刃爆发"><!-- dps -->
			<name>knifeBoom_FightKing</name>
			<cnName>狂刃爆发</cnName>
			<showInLifeBarB>1</showInLifeBarB>
			<noInClonedB>1</noInClonedB>
			<ignoreSilenceB>1</ignoreSilenceB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>8</cd>
			<delay>0.3</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>700</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<extraValueType>meDpsFactor</extraValueType><!-- 附加值类型为单位dps系数 -->
			<!-- 子弹所需 -->
			<obj>"name":"knifeBoom_FightKing","site":"me","flipB":true</obj>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>shakeAttack</meActionLabel>
			<description>从单位背后爆发出多把旋刃。</description>
		</skill>
		<skill index="0" name="summonedPo_extra" cnName="召唤冥刃"><!-- 限制 -->
			<name>summonedPo_extra</name>
			<cnName>召唤冥刃</cnName>
			<showInLifeBarB>1</showInLifeBarB>
			<noInClonedB>1</noInClonedB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>4</cd>
			<delay>0.25</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>700</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<!-- 子弹所需，血量在副本ai里设置 -->
			<obj>"cnName":"冥刃游尸","num":1,"lifeMul":0.02,"dpsMul":0.5,"maxNum":5,"cx":0,"cy":0</obj>
			<!--图像------------------------------------------------------------ -->
			<meActionLabel>windAttack</meActionLabel>
			<description>狂战尸使出旋风刀，同时召唤出一只[obj.cnName]。</description>
		</skill>
		
		
		<skill index="0" name="狂战尸-分身-副本">
			<name>FightKing_cloned_extra</name>
			<cnName>分身</cnName>
			<cd>30</cd>
			<showInLifeBarB>1</showInLifeBarB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>700</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>cloned</effectType>
			<value>1</value>
			<mul>0.20</mul>
			<secMul>0.75</secMul>
			<duration>20</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/cloned_enemy" con="add">skillEffect/hiding_hero</meEffectImg>
			<description>产生[value]个分身，攻击力与自身同等，血量为自身的[mul]，持续[duration]秒。分身被消灭后</description>
		</skill>
		<skill index="0" name="silence_FightKing" cnName="狂战尸-沉默"><!-- dps -->
			<name>silence_FightKing</name>
			<cnName>沉默</cnName>
			<showInLifeBarB>1</showInLifeBarB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>8</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>silenceB</effectType>
			<value>1</value>
			<duration>5</duration>
			<range>999999</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/silence_enemy"></meEffectImg>
			<stateEffectImg partType="mouth" con="add">skillEffect/silence_enemy</stateEffectImg>
			<description>使[range]码以内的敌方单位无法释放技能，持续[duration]秒。</description>
		</skill>
		
		
		<skill index="0" name="金钟雾"><!-- 生存-群体-主动 -->
			<name>coverFog_FightKing</name>
			<cnName>金钟雾</cnName>
			<noInClonedB>1</noInClonedB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>dodgePro</effectType>
			<value>0.50</value>
			<duration>999999</duration>
			<!--图像 ------------------------------------------------------------ -->
			<stateEffectImg partType="arm_right_1,arm_left_1,leg_right_0,leg_right_1,leg_left_0,leg_left_1" con="filter" raNum="30">bulletHitEffect/smoke_small</stateEffectImg>
			<description>全身覆盖金钟雾，增加50%的闪避值。</description>
		</skill>
		
	</father>
</data>