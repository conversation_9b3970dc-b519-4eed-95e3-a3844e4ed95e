<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="normal">
		<gather name="实验室地下">
			<level name="HospitalUnder_re">
				<!-- 发兵集************************************************ -->
				<info enemyLv="99" diff="2" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" />
				<sceneLabel>HospitalUnder</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="we2" camp="we">
						<unit cnName="战争狂人" num="1" aiOrder="followBody:我" skillArr="madmanHead" />
					</unitOrder>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1">
							<condition delay="0.01"></condition>
							<order>recoverAllHeroHead</order>
							<order>noWearShop</order>
							<order>createUnit:we2; r2</order>
							<order>playMusic; music_face:suspense</order>
						</event>
						<event id="e1_1">
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_0">
							<condition>say:listOver; s1</condition>
							<!-- 取下头盔 -->
							<order>body:战争狂人; doSkill:madmanHead</order>
							<order>playMusic; music_face:piano</order>
						</event>
						<event id="e2_1">
							<condition delay="1.5"></condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e2_11">
							<condition>say:listOver; s2</condition>
							<order>task:now; complete</order>
							
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		<gather name="战斧高地">
			<level name="MainUpland_re">
				<!-- 发兵集************************************************ -->
				<info enemyLv="99" diff="2" allMoreB="1" noPetB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" />
				<sceneLabel>MainUpland</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e1_1">
							<condition delay="0.01"></condition>
							<order>recoverAllHeroHead</order>
							<order>noWearShop</order>
							<order>playMusic; music_face:funny</order>
						</event>
						<event id="e2_1">
							<condition delay="0.1"></condition>
							<order>body:小樱; followBody:文杰表哥</order>
							<order>body:藏师将军; followBody:小樱</order>
							<order>body:心零; followBody:藏师将军</order>
						</event>
						<event id="e1_1">
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e2_11">
							<condition>say:listOver; s1</condition>
							<order>task:now; complete</order>
							<order>playMusic; music_face:descent</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="leavePartner_WenJie">
				<!-- 发兵集************************************************ -->
				<info enemyLv="99" diff="2" allMoreB="1" noPetB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" />
				<sceneLabel>MainUpland</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e1_1">
							<condition delay="0.01"></condition>
							<order>recoverAllHeroHead</order>
							<order>noWearShop</order>
						</event>
						<event id="e2_1">
							<condition delay="0.1"></condition>
							<order>body:小樱; followBody:文杰表哥</order>
							<order>body:藏师将军; followBody:小樱</order>
							<order>body:心零; followBody:藏师将军</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver; s1</condition>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition>say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			
			<level name="madPlot_WenJie">
				<!-- 发兵集************************************************ -->
				<info enemyLv="99" diff="2" allMoreB="1" noPetB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" />
				<sceneLabel>MainUpland</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e1_1">
							<condition delay="0.01"></condition>
							<order>recoverAllHeroHead</order>
							<order>noWearShop</order>
						</event>
						<event id="e2_1">
							<condition delay="0.1"></condition>
							<order>body:小樱; followBody:文杰表哥</order>
							<order>body:藏师将军; followBody:小樱</order>
							<order>body:心零; followBody:藏师将军</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver; s1</condition>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition>say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		

	</father>
</data>